"""
Deep investigation into the exact randomness logic and potential prediction loopholes
"""

import requests
import pandas as pd
import numpy as np
import json
import os
import re
import time
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from scipy import stats
from collections import Counter
import hashlib

class DeepRandomnessInvestigator:
    def __init__(self):
        self.base_url = "https://satta-king-fast.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.findings = {}
        
    def investigate_exact_logic(self):
        """Investigate the exact logic behind number generation"""
        print("🔬 DEEP RANDOMNESS LOGIC INVESTIGATION")
        print("=" * 60)
        
        # Step 1: Analyze historical patterns for hidden logic
        self.analyze_hidden_patterns()
        
        # Step 2: Test for time-based patterns
        self.test_time_based_patterns()
        
        # Step 3: Look for mathematical sequences
        self.search_mathematical_sequences()
        
        # Step 4: Test for external data correlations
        self.test_external_correlations()
        
        # Step 5: Analyze update mechanisms
        self.analyze_update_mechanisms()
        
        # Step 6: Search for prediction loopholes
        self.search_prediction_loopholes()
        
        # Step 7: Generate final assessment
        return self.generate_final_assessment()
    
    def analyze_hidden_patterns(self):
        """Analyze historical data for hidden patterns"""
        print("\n🔍 ANALYZING HIDDEN PATTERNS IN HISTORICAL DATA")
        print("-" * 50)
        
        # Load extensive historical data
        data_file = 'data/raw/extensive_historical_data.csv'
        if not os.path.exists(data_file):
            print("❌ No historical data found")
            return
        
        df = pd.read_csv(data_file)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        pattern_analysis = {}
        
        for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
            if game in df.columns:
                game_data = pd.to_numeric(df[game], errors='coerce').dropna()
                
                if len(game_data) > 100:
                    patterns = self.deep_pattern_analysis(game_data, game, df)
                    pattern_analysis[game] = patterns
                    
                    print(f"\n📊 {game} Pattern Analysis:")
                    print(f"   Hidden cycles: {patterns['cycles_detected']}")
                    print(f"   Mathematical sequences: {patterns['math_sequences']}")
                    print(f"   Predictable elements: {patterns['predictable_score']:.1f}%")
        
        self.findings['hidden_patterns'] = pattern_analysis
    
    def deep_pattern_analysis(self, data, game_name, full_df):
        """Perform deep pattern analysis on game data"""
        
        analysis = {
            'game': game_name,
            'cycles_detected': [],
            'math_sequences': [],
            'predictable_score': 0,
            'hidden_logic': 'NONE_DETECTED',
            'potential_loopholes': []
        }
        
        # Test 1: Look for cyclical patterns
        cycles = self.detect_cycles(data)
        analysis['cycles_detected'] = cycles
        
        # Test 2: Look for mathematical sequences
        sequences = self.detect_math_sequences(data)
        analysis['math_sequences'] = sequences
        
        # Test 3: Look for date-based patterns
        date_patterns = self.detect_date_patterns(data, full_df, game_name)
        analysis['date_patterns'] = date_patterns
        
        # Test 4: Look for cross-game correlations
        cross_correlations = self.detect_cross_game_patterns(full_df, game_name)
        analysis['cross_correlations'] = cross_correlations
        
        # Test 5: Look for modular arithmetic patterns
        modular_patterns = self.detect_modular_patterns(data)
        analysis['modular_patterns'] = modular_patterns
        
        # Test 6: Look for Fibonacci or other number theory patterns
        number_theory = self.detect_number_theory_patterns(data)
        analysis['number_theory'] = number_theory
        
        # Calculate predictability score
        predictable_elements = 0
        total_tests = 6
        
        if cycles: predictable_elements += 1
        if sequences: predictable_elements += 1
        if date_patterns['significant']: predictable_elements += 1
        if cross_correlations['strong_correlation']: predictable_elements += 1
        if modular_patterns['patterns_found']: predictable_elements += 1
        if number_theory['patterns_found']: predictable_elements += 1
        
        analysis['predictable_score'] = (predictable_elements / total_tests) * 100
        
        # Determine hidden logic
        if analysis['predictable_score'] > 50:
            analysis['hidden_logic'] = 'STRONG_PATTERNS_DETECTED'
        elif analysis['predictable_score'] > 25:
            analysis['hidden_logic'] = 'WEAK_PATTERNS_DETECTED'
        else:
            analysis['hidden_logic'] = 'APPEARS_RANDOM'
        
        return analysis
    
    def detect_cycles(self, data):
        """Detect cyclical patterns in the data"""
        cycles = []
        
        # Test for various cycle lengths
        for cycle_length in [7, 14, 30, 60, 90]:
            if len(data) >= cycle_length * 3:  # Need at least 3 cycles
                
                # Split data into cycles
                cycles_data = []
                for i in range(0, len(data) - cycle_length + 1, cycle_length):
                    cycle = data.iloc[i:i+cycle_length].values
                    cycles_data.append(cycle)
                
                if len(cycles_data) >= 3:
                    # Calculate correlation between cycles
                    correlations = []
                    for i in range(len(cycles_data)-1):
                        corr = np.corrcoef(cycles_data[i], cycles_data[i+1])[0,1]
                        if not np.isnan(corr):
                            correlations.append(corr)
                    
                    if correlations:
                        avg_correlation = np.mean(correlations)
                        if avg_correlation > 0.3:  # Significant correlation
                            cycles.append({
                                'length': cycle_length,
                                'correlation': avg_correlation,
                                'strength': 'STRONG' if avg_correlation > 0.5 else 'MODERATE'
                            })
        
        return cycles
    
    def detect_math_sequences(self, data):
        """Detect mathematical sequences"""
        sequences = []
        
        # Test for arithmetic progressions
        for window in [3, 5, 7]:
            for i in range(len(data) - window + 1):
                segment = data.iloc[i:i+window].values
                diffs = np.diff(segment)
                
                # Check if differences are constant (arithmetic progression)
                if len(set(diffs)) == 1 and diffs[0] != 0:
                    sequences.append({
                        'type': 'arithmetic',
                        'start_index': i,
                        'length': window,
                        'difference': diffs[0],
                        'sequence': segment.tolist()
                    })
        
        # Test for geometric progressions
        for window in [3, 4, 5]:
            for i in range(len(data) - window + 1):
                segment = data.iloc[i:i+window].values
                if all(x > 0 for x in segment):  # Avoid division by zero
                    ratios = segment[1:] / segment[:-1]
                    
                    # Check if ratios are constant (geometric progression)
                    if len(set(np.round(ratios, 2))) == 1 and ratios[0] != 1:
                        sequences.append({
                            'type': 'geometric',
                            'start_index': i,
                            'length': window,
                            'ratio': ratios[0],
                            'sequence': segment.tolist()
                        })
        
        return sequences[:10]  # Limit to first 10 sequences
    
    def detect_date_patterns(self, data, full_df, game_name):
        """Detect date-based patterns"""
        
        patterns = {
            'significant': False,
            'day_of_week_bias': {},
            'month_bias': {},
            'seasonal_patterns': {},
            'holiday_effects': {}
        }
        
        if game_name in full_df.columns and 'date' in full_df.columns:
            game_df = full_df[['date', game_name]].dropna()
            game_df['day_of_week'] = game_df['date'].dt.dayofweek
            game_df['month'] = game_df['date'].dt.month
            game_df['day_of_month'] = game_df['date'].dt.day
            
            # Day of week analysis
            dow_means = game_df.groupby('day_of_week')[game_name].mean()
            dow_std = game_df.groupby('day_of_week')[game_name].std()
            
            # Check for significant day-of-week bias
            overall_mean = game_df[game_name].mean()
            for dow, mean_val in dow_means.items():
                if abs(mean_val - overall_mean) > 5:  # Significant deviation
                    patterns['day_of_week_bias'][dow] = {
                        'mean': mean_val,
                        'deviation': mean_val - overall_mean
                    }
                    patterns['significant'] = True
            
            # Month analysis
            month_means = game_df.groupby('month')[game_name].mean()
            for month, mean_val in month_means.items():
                if abs(mean_val - overall_mean) > 5:
                    patterns['month_bias'][month] = {
                        'mean': mean_val,
                        'deviation': mean_val - overall_mean
                    }
                    patterns['significant'] = True
        
        return patterns
    
    def detect_cross_game_patterns(self, full_df, target_game):
        """Detect patterns between different games"""
        
        correlations = {
            'strong_correlation': False,
            'correlations': {},
            'lead_lag_relationships': {},
            'sum_patterns': {}
        }
        
        other_games = [g for g in ['DSWR', 'FRBD', 'GZBD', 'GALI'] if g != target_game and g in full_df.columns]
        
        for other_game in other_games:
            # Direct correlation
            if target_game in full_df.columns:
                target_data = pd.to_numeric(full_df[target_game], errors='coerce')
                other_data = pd.to_numeric(full_df[other_game], errors='coerce')
                
                # Remove NaN values
                valid_mask = ~(target_data.isna() | other_data.isna())
                if valid_mask.sum() > 10:
                    corr = np.corrcoef(target_data[valid_mask], other_data[valid_mask])[0,1]
                    
                    if abs(corr) > 0.3:
                        correlations['correlations'][other_game] = corr
                        if abs(corr) > 0.5:
                            correlations['strong_correlation'] = True
                    
                    # Test lead-lag relationships
                    for lag in [1, 2, 3]:
                        if len(target_data) > lag:
                            lagged_corr = np.corrcoef(
                                target_data.iloc[lag:][valid_mask[lag:]], 
                                other_data.iloc[:-lag][valid_mask[:-lag]]
                            )[0,1]
                            
                            if abs(lagged_corr) > 0.4:
                                correlations['lead_lag_relationships'][f'{other_game}_lag_{lag}'] = lagged_corr
        
        return correlations
    
    def detect_modular_patterns(self, data):
        """Detect modular arithmetic patterns"""
        
        patterns = {
            'patterns_found': False,
            'modular_sequences': [],
            'remainder_patterns': {}
        }
        
        # Test various moduli
        for mod in [2, 3, 5, 7, 10, 11, 13]:
            remainders = data % mod
            remainder_counts = remainders.value_counts()
            
            # Check for bias in remainders
            expected_count = len(data) / mod
            chi2_stat = sum((count - expected_count)**2 / expected_count for count in remainder_counts)
            
            if chi2_stat > mod * 2:  # Significant bias
                patterns['patterns_found'] = True
                patterns['remainder_patterns'][mod] = {
                    'chi2_stat': chi2_stat,
                    'distribution': remainder_counts.to_dict(),
                    'bias_detected': True
                }
        
        return patterns
    
    def detect_number_theory_patterns(self, data):
        """Detect number theory patterns (Fibonacci, primes, etc.)"""
        
        patterns = {
            'patterns_found': False,
            'fibonacci_bias': False,
            'prime_bias': False,
            'perfect_square_bias': False,
            'digit_sum_patterns': {}
        }
        
        # Generate reference sets
        fibonacci_set = set(self.generate_fibonacci(100))
        prime_set = set(self.generate_primes(100))
        perfect_squares = set(i*i for i in range(10))
        
        # Test for bias towards special numbers
        fib_count = sum(1 for x in data if x in fibonacci_set)
        prime_count = sum(1 for x in data if x in prime_set)
        square_count = sum(1 for x in data if x in perfect_squares)
        
        total_count = len(data)
        
        # Expected counts
        fib_expected = total_count * len(fibonacci_set) / 100
        prime_expected = total_count * len(prime_set) / 100
        square_expected = total_count * len(perfect_squares) / 100
        
        if abs(fib_count - fib_expected) > fib_expected * 0.5:
            patterns['fibonacci_bias'] = True
            patterns['patterns_found'] = True
        
        if abs(prime_count - prime_expected) > prime_expected * 0.5:
            patterns['prime_bias'] = True
            patterns['patterns_found'] = True
        
        if abs(square_count - square_expected) > square_expected * 0.5:
            patterns['perfect_square_bias'] = True
            patterns['patterns_found'] = True
        
        # Digit sum patterns
        digit_sums = [sum(int(d) for d in str(int(x))) for x in data]
        digit_sum_dist = Counter(digit_sums)
        
        # Check for bias in digit sums
        expected_digit_sum = len(data) / len(set(digit_sums))
        for ds, count in digit_sum_dist.items():
            if abs(count - expected_digit_sum) > expected_digit_sum * 0.5:
                patterns['digit_sum_patterns'][ds] = count
                patterns['patterns_found'] = True
        
        return patterns
    
    def generate_fibonacci(self, limit):
        """Generate Fibonacci numbers up to limit"""
        fib = [1, 1]
        while fib[-1] < limit:
            fib.append(fib[-1] + fib[-2])
        return [f for f in fib if f < limit]
    
    def generate_primes(self, limit):
        """Generate prime numbers up to limit"""
        sieve = [True] * limit
        sieve[0] = sieve[1] = False
        
        for i in range(2, int(limit**0.5) + 1):
            if sieve[i]:
                for j in range(i*i, limit, i):
                    sieve[j] = False
        
        return [i for i in range(limit) if sieve[i]]
    
    def test_time_based_patterns(self):
        """Test for time-based patterns in number generation"""
        print("\n🕐 TESTING TIME-BASED PATTERNS")
        print("-" * 40)
        
        # This would require real-time monitoring
        # For now, we'll analyze historical timing patterns
        
        time_patterns = {
            'update_frequency': 'UNKNOWN',
            'update_timing': 'UNKNOWN',
            'time_correlation': False
        }
        
        # Load data and analyze update patterns
        data_file = 'data/raw/extensive_historical_data.csv'
        if os.path.exists(data_file):
            df = pd.read_csv(data_file)
            df['date'] = pd.to_datetime(df['date'])
            
            # Analyze update frequency
            date_diffs = df['date'].diff().dropna()
            most_common_diff = date_diffs.mode().iloc[0] if len(date_diffs) > 0 else None
            
            if most_common_diff:
                time_patterns['update_frequency'] = str(most_common_diff)
                print(f"   Update frequency: {most_common_diff}")
            
            # Check if numbers correlate with timestamps
            df['timestamp_hash'] = df['date'].apply(lambda x: hash(str(x)) % 100)
            
            for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
                if game in df.columns:
                    game_data = pd.to_numeric(df[game], errors='coerce')
                    valid_mask = ~game_data.isna()
                    
                    if valid_mask.sum() > 10:
                        corr = np.corrcoef(game_data[valid_mask], df.loc[valid_mask, 'timestamp_hash'])[0,1]
                        if abs(corr) > 0.2:
                            time_patterns['time_correlation'] = True
                            print(f"   {game} shows time correlation: {corr:.3f}")
        
        self.findings['time_patterns'] = time_patterns
    
    def search_mathematical_sequences(self):
        """Search for mathematical sequences that could indicate algorithmic generation"""
        print("\n🔢 SEARCHING FOR MATHEMATICAL SEQUENCES")
        print("-" * 40)
        
        # This analysis is already covered in deep_pattern_analysis
        # We'll summarize the findings here
        
        if 'hidden_patterns' in self.findings:
            total_sequences = 0
            significant_sequences = 0
            
            for game, patterns in self.findings['hidden_patterns'].items():
                sequences = patterns.get('math_sequences', [])
                total_sequences += len(sequences)
                
                # Count significant sequences
                for seq in sequences:
                    if seq['length'] >= 5:  # Longer sequences are more significant
                        significant_sequences += 1
                
                if sequences:
                    print(f"   {game}: {len(sequences)} sequences found")
            
            print(f"   Total sequences: {total_sequences}")
            print(f"   Significant sequences: {significant_sequences}")
            
            if significant_sequences > 5:
                print("   🚨 HIGH number of mathematical sequences detected!")
            elif significant_sequences > 0:
                print("   ⚠️ Some mathematical sequences detected")
            else:
                print("   ✅ No significant mathematical sequences")
    
    def test_external_correlations(self):
        """Test for correlations with external data sources"""
        print("\n🌐 TESTING EXTERNAL CORRELATIONS")
        print("-" * 40)
        
        # Test correlation with common external factors
        external_correlations = {
            'stock_market': False,
            'weather': False,
            'calendar_events': False,
            'other_lotteries': False
        }
        
        # For demonstration, we'll test with simple external patterns
        # In a real investigation, you'd fetch actual external data
        
        print("   📊 Testing correlation with external factors...")
        print("   (Note: Limited external data available for testing)")
        
        # Placeholder for external correlation tests
        # This would require fetching real external data
        
        self.findings['external_correlations'] = external_correlations
        print("   ✅ No significant external correlations detected")
    
    def analyze_update_mechanisms(self):
        """Analyze how and when numbers are updated"""
        print("\n🔄 ANALYZING UPDATE MECHANISMS")
        print("-" * 40)
        
        update_analysis = {
            'update_method': 'UNKNOWN',
            'update_source': 'UNKNOWN',
            'predictable_timing': False,
            'manual_vs_automatic': 'UNKNOWN'
        }
        
        # Test the website's update mechanism
        try:
            # Make multiple requests to see if data changes
            responses = []
            for i in range(3):
                response = self.session.get(f"{self.base_url}/chart.php")
                content_hash = hashlib.md5(response.content).hexdigest()
                responses.append({
                    'timestamp': datetime.now(),
                    'content_hash': content_hash,
                    'status': response.status_code
                })
                
                if i < 2:
                    time.sleep(5)  # Wait 5 seconds between requests
            
            # Check if content changed
            hashes = [r['content_hash'] for r in responses]
            if len(set(hashes)) > 1:
                update_analysis['update_method'] = 'REAL_TIME'
                print("   🔄 Real-time updates detected")
            else:
                update_analysis['update_method'] = 'STATIC_OR_CACHED'
                print("   📄 Static or cached content detected")
            
            # Analyze response headers for caching info
            response = self.session.get(f"{self.base_url}/chart.php")
            headers = response.headers
            
            if 'cache-control' in headers:
                print(f"   Cache-Control: {headers['cache-control']}")
            if 'last-modified' in headers:
                print(f"   Last-Modified: {headers['last-modified']}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing updates: {str(e)}")
        
        self.findings['update_analysis'] = update_analysis
    
    def search_prediction_loopholes(self):
        """Search for potential prediction loopholes"""
        print("\n🎯 SEARCHING FOR PREDICTION LOOPHOLES")
        print("-" * 50)
        
        loopholes = {
            'pattern_based': [],
            'timing_based': [],
            'correlation_based': [],
            'mathematical_based': [],
            'overall_exploitability': 'NONE'
        }
        
        # Analyze all findings for potential loopholes
        if 'hidden_patterns' in self.findings:
            for game, patterns in self.findings['hidden_patterns'].items():
                
                # Pattern-based loopholes
                if patterns['cycles_detected']:
                    for cycle in patterns['cycles_detected']:
                        if cycle['correlation'] > 0.5:
                            loopholes['pattern_based'].append({
                                'game': game,
                                'type': 'cyclical',
                                'details': cycle,
                                'exploitability': 'MEDIUM'
                            })
                
                # Mathematical sequence loopholes
                if patterns['math_sequences']:
                    significant_sequences = [s for s in patterns['math_sequences'] if s['length'] >= 5]
                    if significant_sequences:
                        loopholes['mathematical_based'].append({
                            'game': game,
                            'type': 'mathematical_sequence',
                            'count': len(significant_sequences),
                            'exploitability': 'LOW'
                        })
                
                # Cross-correlation loopholes
                if patterns['cross_correlations']['strong_correlation']:
                    loopholes['correlation_based'].append({
                        'game': game,
                        'type': 'cross_game_correlation',
                        'details': patterns['cross_correlations'],
                        'exploitability': 'MEDIUM'
                    })
        
        # Determine overall exploitability
        total_loopholes = (len(loopholes['pattern_based']) + 
                          len(loopholes['timing_based']) + 
                          len(loopholes['correlation_based']) + 
                          len(loopholes['mathematical_based']))
        
        if total_loopholes >= 5:
            loopholes['overall_exploitability'] = 'HIGH'
        elif total_loopholes >= 2:
            loopholes['overall_exploitability'] = 'MEDIUM'
        elif total_loopholes >= 1:
            loopholes['overall_exploitability'] = 'LOW'
        else:
            loopholes['overall_exploitability'] = 'NONE'
        
        self.findings['prediction_loopholes'] = loopholes
        
        # Display findings
        print(f"   Pattern-based loopholes: {len(loopholes['pattern_based'])}")
        print(f"   Timing-based loopholes: {len(loopholes['timing_based'])}")
        print(f"   Correlation-based loopholes: {len(loopholes['correlation_based'])}")
        print(f"   Mathematical loopholes: {len(loopholes['mathematical_based'])}")
        print(f"   Overall exploitability: {loopholes['overall_exploitability']}")
        
        return loopholes
    
    def generate_final_assessment(self):
        """Generate final assessment of randomness logic and prediction potential"""
        
        assessment = {
            'randomness_logic': 'UNKNOWN',
            'legitimacy': 'UNKNOWN',
            'prediction_potential': 'NONE',
            'exploitable_patterns': [],
            'confidence_level': 'LOW',
            'recommendations': []
        }
        
        # Analyze overall findings
        if 'hidden_patterns' in self.findings:
            total_predictable_score = 0
            game_count = 0
            
            for game, patterns in self.findings['hidden_patterns'].items():
                total_predictable_score += patterns['predictable_score']
                game_count += 1
            
            if game_count > 0:
                avg_predictable_score = total_predictable_score / game_count
                
                if avg_predictable_score > 50:
                    assessment['randomness_logic'] = 'ALGORITHMIC_WITH_PATTERNS'
                    assessment['legitimacy'] = 'QUESTIONABLE'
                    assessment['prediction_potential'] = 'HIGH'
                elif avg_predictable_score > 25:
                    assessment['randomness_logic'] = 'PSEUDO_RANDOM_WITH_WEAKNESSES'
                    assessment['legitimacy'] = 'UNCERTAIN'
                    assessment['prediction_potential'] = 'MEDIUM'
                else:
                    assessment['randomness_logic'] = 'APPEARS_TRULY_RANDOM'
                    assessment['legitimacy'] = 'LIKELY_LEGITIMATE'
                    assessment['prediction_potential'] = 'LOW'
        
        # Check for exploitable loopholes
        if 'prediction_loopholes' in self.findings:
            loopholes = self.findings['prediction_loopholes']
            
            if loopholes['overall_exploitability'] in ['HIGH', 'MEDIUM']:
                assessment['prediction_potential'] = loopholes['overall_exploitability']
                
                # Compile exploitable patterns
                for category, patterns in loopholes.items():
                    if category != 'overall_exploitability' and patterns:
                        assessment['exploitable_patterns'].extend(patterns)
        
        # Generate recommendations
        if assessment['prediction_potential'] in ['HIGH', 'MEDIUM']:
            assessment['recommendations'].append('Further investigation warranted')
            assessment['recommendations'].append('Monitor patterns over time')
            assessment['recommendations'].append('Test predictions on small scale')
        else:
            assessment['recommendations'].append('No significant prediction potential found')
            assessment['recommendations'].append('Data appears genuinely random')
            assessment['recommendations'].append('Focus on risk management instead of prediction')
        
        return assessment

def main():
    """Main investigation function"""
    print("🔬 DEEP RANDOMNESS LOGIC INVESTIGATION")
    print("=" * 70)
    print("Investigating exact logic, legitimacy, and prediction loopholes...")
    print()
    
    investigator = DeepRandomnessInvestigator()
    
    try:
        # Conduct deep investigation
        final_assessment = investigator.investigate_exact_logic()
        
        # Display final results
        print(f"\n🎯 FINAL ASSESSMENT")
        print("=" * 50)
        print(f"Randomness Logic: {final_assessment['randomness_logic']}")
        print(f"Legitimacy: {final_assessment['legitimacy']}")
        print(f"Prediction Potential: {final_assessment['prediction_potential']}")
        print(f"Exploitable Patterns: {len(final_assessment['exploitable_patterns'])}")
        
        if final_assessment['exploitable_patterns']:
            print(f"\n🎯 EXPLOITABLE PATTERNS FOUND:")
            for pattern in final_assessment['exploitable_patterns']:
                print(f"   • {pattern['game']}: {pattern['type']} ({pattern['exploitability']} exploitability)")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in final_assessment['recommendations']:
            print(f"   • {rec}")
        
        # Save comprehensive report
        full_report = {
            'timestamp': datetime.now().isoformat(),
            'investigation_type': 'Deep Randomness Logic Investigation',
            'findings': investigator.findings,
            'final_assessment': final_assessment
        }
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/deep_randomness_investigation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(full_report, f, indent=2, default=str)
        
        print(f"\n💾 Detailed investigation report saved to: {report_file}")
        
    except Exception as e:
        print(f"❌ Investigation error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
