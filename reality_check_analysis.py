"""
Reality check: Will our 93.3% accuracy actually work for future predictions?
Critical analysis of our methodology and potential issues
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import os

def reality_check_analysis():
    """Perform a brutal reality check on our prediction claims"""
    
    print("🔍 REALITY CHECK: WILL OUR METHOD ACTUALLY WORK?")
    print("=" * 70)
    print("Critical analysis of our 93.3% accuracy claims...")
    print("=" * 70)
    
    # Load our data and results
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    
    # Load prediction results
    result_files = [f for f in os.listdir('data') if f.startswith('prediction_bot_results_')]
    if result_files:
        latest_file = max(result_files, key=lambda x: os.path.getmtime(os.path.join('data', x)))
        with open(os.path.join('data', latest_file), 'r') as f:
            bot_results = json.load(f)
    else:
        print("❌ No prediction results found")
        return
    
    print(f"✅ Analyzing {len(df)} historical records")
    
    # Critical analysis sections
    issues_found = []
    
    # 1. Sample size analysis
    sample_issues = analyze_sample_size_issues(df, bot_results)
    issues_found.extend(sample_issues)
    
    # 2. Overfitting detection
    overfitting_issues = detect_overfitting_problems(bot_results)
    issues_found.extend(overfitting_issues)
    
    # 3. Data quality assessment
    data_quality_issues = assess_data_quality(df)
    issues_found.extend(data_quality_issues)
    
    # 4. Statistical significance testing
    significance_issues = test_statistical_significance(bot_results)
    issues_found.extend(significance_issues)
    
    # 5. Real-world applicability
    applicability_issues = assess_real_world_applicability(df, bot_results)
    issues_found.extend(applicability_issues)
    
    # 6. Pattern stability analysis
    stability_issues = analyze_pattern_stability(df)
    issues_found.extend(stability_issues)
    
    # Generate final reality assessment
    generate_reality_assessment(issues_found, bot_results)

def analyze_sample_size_issues(df, bot_results):
    """Analyze if our sample size is sufficient for reliable predictions"""
    
    print("\n📊 SAMPLE SIZE ANALYSIS")
    print("-" * 40)
    
    issues = []
    
    # Check backtest sample size
    backtest_results = bot_results.get('backtest_results', {})
    
    for game, results in backtest_results.items():
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            total_samples = len(game_data)
            
            # Estimate backtest size (typically last 30 days)
            backtest_size = min(30, total_samples)
            
            print(f"🎲 {game}:")
            print(f"   Total samples: {total_samples}")
            print(f"   Backtest samples: {backtest_size}")
            print(f"   Exact accuracy: {results['exact_accuracy']:.1f}%")
            
            # Critical issues
            if backtest_size < 50:
                issues.append(f"CRITICAL: {game} backtest size too small ({backtest_size} samples)")
                print(f"   🚨 CRITICAL: Backtest size too small for reliable statistics")
            
            if total_samples < 200:
                issues.append(f"WARNING: {game} total sample size small ({total_samples} samples)")
                print(f"   ⚠️ WARNING: Total sample size may be insufficient")
            
            # Statistical power analysis
            if backtest_size > 0:
                expected_correct = backtest_size * 0.01  # 1% expected for random (1/100)
                actual_correct = backtest_size * (results['exact_accuracy'] / 100)
                
                if actual_correct < 3:  # Less than 3 correct predictions
                    issues.append(f"CRITICAL: {game} too few correct predictions for significance")
                    print(f"   🚨 CRITICAL: Only {actual_correct:.1f} correct predictions")
    
    return issues

def detect_overfitting_problems(bot_results):
    """Detect signs of overfitting in our models"""
    
    print("\n🔬 OVERFITTING DETECTION")
    print("-" * 40)
    
    issues = []
    
    # Check feature importance
    feature_importance = bot_results.get('feature_importance', {})
    
    for game, features in feature_importance.items():
        if features:
            print(f"🎲 {game}:")
            
            # Check for extreme feature dominance
            top_feature_importance = features[0]['importance']
            second_feature_importance = features[1]['importance'] if len(features) > 1 else 0
            
            print(f"   Top feature importance: {top_feature_importance:.3f}")
            print(f"   Second feature importance: {second_feature_importance:.3f}")
            
            # Red flags for overfitting
            if top_feature_importance > 0.9:
                issues.append(f"CRITICAL: {game} extreme feature dominance ({top_feature_importance:.3f})")
                print(f"   🚨 CRITICAL: Extreme feature dominance - likely overfitting")
            
            if top_feature_importance > 0.8 and second_feature_importance < 0.1:
                issues.append(f"WARNING: {game} single feature dominance")
                print(f"   ⚠️ WARNING: Single feature dominance")
            
            # Check for too many features
            total_features = len(features)
            if total_features > 50:
                issues.append(f"WARNING: {game} too many features ({total_features})")
                print(f"   ⚠️ WARNING: Too many features ({total_features}) - overfitting risk")
    
    # Check for perfect or near-perfect accuracy
    backtest_results = bot_results.get('backtest_results', {})
    
    for game, results in backtest_results.items():
        exact_accuracy = results['exact_accuracy']
        
        if exact_accuracy >= 95:
            issues.append(f"CRITICAL: {game} suspiciously high accuracy ({exact_accuracy:.1f}%)")
            print(f"   🚨 CRITICAL: {game} accuracy too high ({exact_accuracy:.1f}%) - likely overfitting")
        elif exact_accuracy >= 90:
            issues.append(f"WARNING: {game} very high accuracy ({exact_accuracy:.1f}%)")
            print(f"   ⚠️ WARNING: {game} very high accuracy - possible overfitting")
    
    return issues

def assess_data_quality(df):
    """Assess the quality and reliability of our data"""
    
    print("\n📋 DATA QUALITY ASSESSMENT")
    print("-" * 40)
    
    issues = []
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce')
            
            print(f"🎲 {game}:")
            
            # Check for missing data
            missing_count = game_data.isna().sum()
            missing_percentage = (missing_count / len(df)) * 100
            
            print(f"   Missing data: {missing_count} ({missing_percentage:.1f}%)")
            
            if missing_percentage > 10:
                issues.append(f"WARNING: {game} high missing data ({missing_percentage:.1f}%)")
                print(f"   ⚠️ WARNING: High missing data percentage")
            
            # Check for data consistency
            clean_data = game_data.dropna()
            
            if len(clean_data) > 0:
                # Check for impossible values
                invalid_values = ((clean_data < 0) | (clean_data > 99)).sum()
                if invalid_values > 0:
                    issues.append(f"CRITICAL: {game} invalid values ({invalid_values})")
                    print(f"   🚨 CRITICAL: Invalid values detected")
                
                # Check for data entry errors (too many repeated values)
                value_counts = clean_data.value_counts()
                max_frequency = value_counts.max()
                expected_max = len(clean_data) * 0.05  # 5% max expected
                
                if max_frequency > expected_max * 2:
                    issues.append(f"WARNING: {game} suspicious data repetition")
                    print(f"   ⚠️ WARNING: Suspicious data repetition")
                
                # Check for data gaps
                date_gaps = df['date'].diff().dt.days
                large_gaps = (date_gaps > 7).sum()  # More than 7 days
                
                if large_gaps > len(df) * 0.1:
                    issues.append(f"WARNING: {game} irregular data collection")
                    print(f"   ⚠️ WARNING: Irregular data collection patterns")
    
    return issues

def test_statistical_significance(bot_results):
    """Test if our results are statistically significant"""
    
    print("\n📈 STATISTICAL SIGNIFICANCE TESTING")
    print("-" * 40)
    
    issues = []
    
    backtest_results = bot_results.get('backtest_results', {})
    
    for game, results in backtest_results.items():
        print(f"🎲 {game}:")
        
        exact_accuracy = results['exact_accuracy']
        
        # Assume 30-day backtest
        n_trials = 30
        n_successes = int(n_trials * exact_accuracy / 100)
        
        print(f"   Trials: {n_trials}")
        print(f"   Successes: {n_successes}")
        print(f"   Accuracy: {exact_accuracy:.1f}%")
        
        # Binomial test against random chance (1%)
        from scipy import stats
        
        # P-value for getting this many successes by chance
        p_value = 1 - stats.binom.cdf(n_successes - 1, n_trials, 0.01)
        
        print(f"   P-value vs random: {p_value:.6f}")
        
        if p_value > 0.05:
            issues.append(f"CRITICAL: {game} not statistically significant (p={p_value:.3f})")
            print(f"   🚨 CRITICAL: Not statistically significant")
        elif p_value > 0.01:
            issues.append(f"WARNING: {game} weak statistical significance (p={p_value:.3f})")
            print(f"   ⚠️ WARNING: Weak statistical significance")
        else:
            print(f"   ✅ Statistically significant")
        
        # Check confidence intervals
        # Wilson score interval for binomial proportion
        p_hat = exact_accuracy / 100
        z = 1.96  # 95% confidence
        
        denominator = 1 + z**2 / n_trials
        center = (p_hat + z**2 / (2 * n_trials)) / denominator
        margin = z * np.sqrt((p_hat * (1 - p_hat) + z**2 / (4 * n_trials)) / n_trials) / denominator
        
        ci_lower = max(0, center - margin)
        ci_upper = min(1, center + margin)
        
        print(f"   95% CI: [{ci_lower:.1%}, {ci_upper:.1%}]")
        
        if ci_lower < 0.05:  # Lower bound below 5%
            issues.append(f"WARNING: {game} confidence interval includes low values")
            print(f"   ⚠️ WARNING: Confidence interval too wide")
    
    return issues

def assess_real_world_applicability(df, bot_results):
    """Assess if our method will work in real-world conditions"""
    
    print("\n🌍 REAL-WORLD APPLICABILITY")
    print("-" * 40)
    
    issues = []
    
    # Check data recency
    latest_date = df['date'].max()
    days_old = (datetime.now() - latest_date).days
    
    print(f"📅 Data recency: {days_old} days old")
    
    if days_old > 30:
        issues.append(f"WARNING: Data is {days_old} days old - patterns may have changed")
        print(f"   ⚠️ WARNING: Data may be outdated")
    
    # Check for external factors
    print(f"\n🔍 External factors to consider:")
    print(f"   • Algorithm updates by operators")
    print(f"   • Changes in betting patterns")
    print(f"   • Regulatory changes")
    print(f"   • Technical system changes")
    print(f"   • Operator awareness of prediction attempts")
    
    # Model assumptions
    print(f"\n🎯 Critical assumptions:")
    print(f"   • Patterns remain stable over time")
    print(f"   • No operator intervention")
    print(f"   • Algorithm doesn't adapt to predictions")
    print(f"   • Historical data represents future behavior")
    
    # Risk factors
    risk_factors = [
        "Algorithm could be updated without notice",
        "Operators might detect prediction patterns",
        "Betting volume changes could affect results",
        "Technical issues could disrupt patterns",
        "Regulatory changes could alter operations"
    ]
    
    for risk in risk_factors:
        issues.append(f"RISK: {risk}")
    
    return issues

def analyze_pattern_stability(df):
    """Analyze if patterns are stable over time"""
    
    print("\n📊 PATTERN STABILITY ANALYSIS")
    print("-" * 40)
    
    issues = []
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            if len(game_data) >= 100:
                print(f"🎲 {game}:")
                
                # Split into periods and compare
                mid_point = len(game_data) // 2
                early_period = game_data.iloc[:mid_point]
                recent_period = game_data.iloc[mid_point:]
                
                # Compare means
                early_mean = early_period.mean()
                recent_mean = recent_period.mean()
                mean_change = abs(recent_mean - early_mean)
                
                print(f"   Early mean: {early_mean:.1f}")
                print(f"   Recent mean: {recent_mean:.1f}")
                print(f"   Mean change: {mean_change:.1f}")
                
                if mean_change > 5:
                    issues.append(f"WARNING: {game} significant mean shift ({mean_change:.1f})")
                    print(f"   ⚠️ WARNING: Significant mean shift")
                
                # Compare variances
                early_var = early_period.var()
                recent_var = recent_period.var()
                var_ratio = recent_var / max(1, early_var)
                
                print(f"   Variance ratio: {var_ratio:.2f}")
                
                if var_ratio > 2 or var_ratio < 0.5:
                    issues.append(f"WARNING: {game} variance instability")
                    print(f"   ⚠️ WARNING: Variance instability")
                
                # Test for distribution changes
                from scipy import stats
                ks_stat, ks_p = stats.ks_2samp(early_period, recent_period)
                
                print(f"   Distribution change p-value: {ks_p:.3f}")
                
                if ks_p < 0.05:
                    issues.append(f"WARNING: {game} distribution has changed significantly")
                    print(f"   ⚠️ WARNING: Distribution has changed")
                else:
                    print(f"   ✅ Stable distribution")
    
    return issues

def generate_reality_assessment(issues_found, bot_results):
    """Generate final reality assessment"""
    
    print(f"\n🎯 REALITY ASSESSMENT")
    print("=" * 50)
    
    # Categorize issues
    critical_issues = [issue for issue in issues_found if issue.startswith('CRITICAL')]
    warning_issues = [issue for issue in issues_found if issue.startswith('WARNING')]
    risk_issues = [issue for issue in issues_found if issue.startswith('RISK')]
    
    print(f"📊 ISSUES SUMMARY:")
    print(f"   Critical issues: {len(critical_issues)}")
    print(f"   Warning issues: {len(warning_issues)}")
    print(f"   Risk factors: {len(risk_issues)}")
    
    # Display critical issues
    if critical_issues:
        print(f"\n🚨 CRITICAL ISSUES:")
        for issue in critical_issues:
            print(f"   • {issue}")
    
    # Display warnings
    if warning_issues:
        print(f"\n⚠️ WARNING ISSUES:")
        for issue in warning_issues[:5]:  # Show top 5
            print(f"   • {issue}")
        if len(warning_issues) > 5:
            print(f"   ... and {len(warning_issues) - 5} more warnings")
    
    # Final verdict
    print(f"\n🎯 FINAL REALITY CHECK:")
    
    if len(critical_issues) >= 3:
        verdict = "HIGHLY_UNRELIABLE"
        print(f"🚨 VERDICT: HIGHLY UNRELIABLE")
        print(f"   📊 Multiple critical issues detected")
        print(f"   ❌ Method unlikely to work in practice")
        print(f"   💰 HIGH RISK of financial loss")
        
    elif len(critical_issues) >= 1 or len(warning_issues) >= 5:
        verdict = "UNRELIABLE"
        print(f"⚠️ VERDICT: UNRELIABLE")
        print(f"   📊 Significant issues detected")
        print(f"   ❌ Method has major flaws")
        print(f"   💰 MODERATE to HIGH RISK")
        
    elif len(warning_issues) >= 3:
        verdict = "QUESTIONABLE"
        print(f"💡 VERDICT: QUESTIONABLE")
        print(f"   📊 Multiple concerns identified")
        print(f"   ⚠️ Method may not be reliable")
        print(f"   💰 MODERATE RISK")
        
    else:
        verdict = "POTENTIALLY_VIABLE"
        print(f"✅ VERDICT: POTENTIALLY VIABLE")
        print(f"   📊 Few major issues detected")
        print(f"   ⚠️ Still has inherent risks")
        print(f"   💰 LOW to MODERATE RISK")
    
    # Honest recommendations
    print(f"\n💡 HONEST RECOMMENDATIONS:")
    
    if verdict in ["HIGHLY_UNRELIABLE", "UNRELIABLE"]:
        print(f"   ❌ DO NOT USE this method for real money")
        print(f"   📚 Consider this as educational exercise only")
        print(f"   🔬 Results likely due to overfitting/small samples")
        print(f"   💰 High probability of financial loss")
        
    elif verdict == "QUESTIONABLE":
        print(f"   ⚠️ EXTREME CAUTION if considering real use")
        print(f"   📊 Test with paper trading first")
        print(f"   💰 Never risk more than you can afford to lose")
        print(f"   🔄 Monitor performance continuously")
        
    else:
        print(f"   ⚠️ PROCEED WITH EXTREME CAUTION")
        print(f"   📊 Start with minimal stakes")
        print(f"   🔄 Continuous monitoring essential")
        print(f"   💰 Strict stop-loss rules required")
        print(f"   📚 Remember: past performance ≠ future results")
    
    # Save reality check
    reality_report = {
        'timestamp': datetime.now().isoformat(),
        'verdict': verdict,
        'critical_issues': len(critical_issues),
        'warning_issues': len(warning_issues),
        'risk_factors': len(risk_issues),
        'all_issues': issues_found,
        'recommendation': 'DO_NOT_USE' if verdict in ["HIGHLY_UNRELIABLE", "UNRELIABLE"] else 'EXTREME_CAUTION'
    }
    
    os.makedirs('data', exist_ok=True)
    with open(f'data/reality_check_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(reality_report, f, indent=2)
    
    print(f"\n💾 Reality check saved to data/reality_check_*.json")

if __name__ == "__main__":
    reality_check_analysis()
