# Satta King Scraper and Prediction Bot

A comprehensive Python application for scraping Satta King data and predicting future numbers using machine learning.

## Features

- **Web Scraping**: Automatically scrapes historical data from Satta King websites
- **Data Processing**: Cleans and processes data with feature engineering
- **Machine Learning**: Multiple prediction models (Random Forest, LSTM, Ensemble)
- **Web Interface**: User-friendly Streamlit web application
- **Analysis Tools**: Comprehensive data analysis and visualization
- **Prediction Bot**: Automated predictions with confidence scores

## Project Structure

```
satta/
├── main.py              # Main application entry point
├── scraper.py           # Web scraping functionality
├── data_processor.py    # Data cleaning and feature engineering
├── models.py            # Machine learning models
├── predictor.py         # Prediction interface
├── utils.py             # Utility functions
├── web_app.py           # Streamlit web interface
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
├── README.md            # This file
├── data/                # Data directory
│   ├── raw/            # Raw scraped data
│   └── processed/      # Processed data
├── models/              # Trained models
└── logs/                # Log files
```

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Create necessary directories:**
   The application will automatically create required directories on first run.

## Usage

### Command Line Interface

The main application supports several commands:

#### 1. Complete Pipeline (Recommended for first run)
```bash
python main.py full
```
This will:
- Scrape data from the last 3 years
- Process and clean the data
- Train machine learning models
- Generate predictions

#### 2. Individual Commands

**Scrape data:**
```bash
python main.py scrape --start-year 2022 --end-year 2024
```

**Process data:**
```bash
python main.py process
```

**Train models:**
```bash
python main.py train --games DSWR GALI
```

**Generate predictions:**
```bash
python main.py predict --method ensemble
```

**Analyze patterns:**
```bash
python main.py analyze --games DSWR --days 30
```

### Web Interface

Launch the web application:
```bash
streamlit run web_app.py
```

Then open your browser to `http://localhost:8501`

The web interface provides:
- **Home**: Overview of data and recent results
- **Predictions**: Generate and view predictions
- **Analysis**: Detailed statistical analysis
- **Trends**: Interactive charts and visualizations
- **Settings**: Configuration and data management

## Games Supported

- **DSWR** (Desawar)
- **FRBD** (Faridabad)
- **GZBD** (Ghaziabad)
- **GALI** (Gali)

## Prediction Methods

1. **Traditional ML**: Random Forest, Gradient Boosting, Linear Regression, SVR
2. **LSTM**: Deep learning time series model
3. **Ensemble**: Combination of multiple models for better accuracy

## Configuration

Edit `config.py` to customize:
- Date ranges for scraping
- Model parameters
- File paths
- Request delays and retry settings

## Data Flow

1. **Scraping**: Downloads data from Satta King websites
2. **Processing**: Cleans data and creates features
3. **Training**: Trains multiple ML models
4. **Prediction**: Generates predictions with confidence scores
5. **Analysis**: Provides insights and visualizations

## Features in Detail

### Web Scraping
- Handles rate limiting and retries
- Parses HTML data from multiple years/months
- Saves data in CSV and JSON formats
- Error handling and logging

### Data Processing
- Cleans missing and invalid data
- Creates time-based features
- Generates rolling statistics
- Pattern recognition features
- Cross-game correlations

### Machine Learning Models
- **Random Forest**: Ensemble tree-based model
- **Gradient Boosting**: Advanced ensemble method
- **LSTM**: Neural network for sequence prediction
- **Ensemble**: Combines multiple models

### Analysis Tools
- Statistical summaries
- Pattern detection
- Trend analysis
- Correlation analysis
- Visualization dashboards

## Output Files

- **Raw Data**: `data/raw/satta_data_YYYY_YYYY.csv`
- **Processed Data**: `data/processed/processed_satta_data.csv`
- **Models**: `models/GAME_traditional.joblib`, `models/GAME_lstm.h5`
- **Predictions**: `data/prediction_report_TIMESTAMP.json`
- **Logs**: `logs/main.log`, `logs/scraper.log`

## Example Prediction Output

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "predictions": {
    "DSWR": {
      "prediction": 45,
      "confidence": 0.72,
      "method": "ensemble"
    },
    "GALI": {
      "prediction": 23,
      "confidence": 0.68,
      "method": "ensemble"
    }
  },
  "summary": {
    "total_predictions": 4,
    "average_confidence": 0.69
  }
}
```

## Important Notes

⚠️ **Disclaimer**: This tool is for educational and research purposes only. Gambling can be addictive and may be illegal in your jurisdiction. Please gamble responsibly and check local laws.

- **Rate Limiting**: The scraper includes delays to avoid overwhelming the website
- **Data Quality**: Always validate scraped data before using for predictions
- **Model Performance**: Prediction accuracy depends on data quality and patterns
- **Legal Compliance**: Ensure compliance with local laws regarding gambling

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **No Data Found**: Check internet connection and website availability

3. **Model Training Fails**: Ensure sufficient data is available (at least 100 records)

4. **Web App Won't Start**: Check if Streamlit is installed and port 8501 is available

### Logs

Check log files in the `logs/` directory for detailed error information:
- `main.log`: General application logs
- `scraper.log`: Web scraping specific logs

## Contributing

Feel free to contribute by:
- Reporting bugs
- Suggesting new features
- Improving prediction algorithms
- Adding new data sources

## License

This project is for educational purposes only. Use responsibly and in accordance with local laws.
