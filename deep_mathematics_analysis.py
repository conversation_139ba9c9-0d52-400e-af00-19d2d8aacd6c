"""
Deep mathematical analysis to uncover the exact algorithm behind number generation
Focus on finding the precise mathematical formula or psychological pattern
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from scipy import stats
from collections import Counter
import hashlib
import math

def deep_mathematics_analysis():
    """Perform deep mathematical analysis to find exact generation method"""

    print("🔬 DEEP MATHEMATICAL ANALYSIS")
    print("=" * 70)
    print("Uncovering the EXACT mathematics behind number generation")
    print("=" * 70)

    # Load data
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    print(f"✅ Analyzing {len(df)} records")

    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()

            print(f"\n🎲 {game} DEEP MATHEMATICAL ANALYSIS:")
            print("=" * 50)

            # 1. Linear Congruential Generator (LCG) Analysis
            analyze_lcg_patterns(game_data, game)

            # 2. Mersenne Twister Analysis
            analyze_mersenne_twister(game_data, game)

            # 3. Hash-based Generation Analysis
            analyze_hash_based_generation(df, game)

            # 4. Time-based Seed Analysis
            analyze_time_based_seeds(df, game)

            # 5. Mathematical Sequence Analysis
            analyze_mathematical_sequences(game_data, game)

            # 6. Modular Arithmetic Deep Dive
            analyze_modular_arithmetic_deep(game_data, game)

            # 7. Psychological Pattern Analysis
            analyze_psychological_patterns(game_data, game)

            # 8. Cross-correlation with External Factors
            analyze_external_correlations(df, game)

def analyze_lcg_patterns(data, game_name):
    """Analyze if numbers follow Linear Congruential Generator pattern"""

    print(f"\n🔢 LINEAR CONGRUENTIAL GENERATOR ANALYSIS")
    print("-" * 40)

    # LCG formula: X(n+1) = (a * X(n) + c) mod m
    # Try to find parameters a, c, m

    if len(data) < 10:
        print("   ❌ Insufficient data for LCG analysis")
        return

    # Test common LCG parameters
    common_moduli = [2**31 - 1, 2**32, 100, 101, 127, 256]

    best_correlation = 0
    best_params = None

    for m in common_moduli:
        for a in [1103515245, 16807, 48271, 69621, 1664525]:  # Common LCG multipliers
            for c in [0, 1, 12345, 1013904223]:  # Common LCG increments

                # Generate sequence using LCG
                predicted = []
                x = int(data.iloc[0])  # Use first number as seed

                for i in range(1, min(len(data), 50)):  # Test first 50 numbers
                    x = (a * x + c) % m
                    if m == 100:
                        predicted.append(x)
                    else:
                        predicted.append(x % 100)  # Map to 0-99 range

                # Compare with actual data
                actual = data.iloc[1:len(predicted)+1].values

                if len(predicted) == len(actual):
                    correlation = np.corrcoef(predicted, actual)[0, 1]

                    if not np.isnan(correlation) and correlation > best_correlation:
                        best_correlation = correlation
                        best_params = (a, c, m)

    print(f"   Best LCG correlation: {best_correlation:.4f}")

    if best_correlation > 0.3:
        a, c, m = best_params
        print(f"   🚨 POTENTIAL LCG DETECTED!")
        print(f"   Formula: X(n+1) = ({a} * X(n) + {c}) mod {m}")
        print(f"   Correlation: {best_correlation:.4f}")

        # Predict next few numbers
        last_number = int(data.iloc[-1])
        next_predictions = []
        x = last_number

        for i in range(5):
            x = (a * x + c) % m
            if m == 100:
                next_predictions.append(x)
            else:
                next_predictions.append(x % 100)

        print(f"   🔮 Next 5 predictions: {next_predictions}")

    else:
        print(f"   ✅ No LCG pattern detected")

def analyze_mersenne_twister(data, game_name):
    """Analyze if numbers follow Mersenne Twister pattern"""

    print(f"\n🌪️ MERSENNE TWISTER ANALYSIS")
    print("-" * 40)

    # Mersenne Twister has specific statistical properties
    # Test for MT19937 characteristics

    if len(data) < 100:
        print("   ❌ Insufficient data for MT analysis")
        return

    # Test 1: Period length (MT19937 has period 2^19937-1)
    # Look for exact repetitions
    sequence_str = ''.join([f"{int(x):02d}" for x in data])

    # Test for short period repetitions
    for period_len in [10, 20, 50, 100]:
        if len(sequence_str) >= period_len * 2:
            first_part = sequence_str[:period_len]
            second_part = sequence_str[period_len:period_len*2]

            if first_part == second_part:
                print(f"   🚨 PERIOD DETECTED: {period_len} numbers")
                print(f"   Repeating sequence: {first_part}")
                return

    # Test 2: Equidistribution test
    # MT should produce uniform distribution
    chi2_stat, p_value = stats.chisquare(np.bincount(data.astype(int), minlength=100))

    print(f"   Uniformity test: χ² = {chi2_stat:.2f}, p = {p_value:.4f}")

    if p_value > 0.05:
        print(f"   ✅ Passes uniformity test (MT-like)")
    else:
        print(f"   ❌ Fails uniformity test")

    # Test 3: Autocorrelation test
    autocorr_1 = np.corrcoef(data[:-1], data[1:])[0, 1]
    autocorr_2 = np.corrcoef(data[:-2], data[2:])[0, 1]

    print(f"   Lag-1 autocorrelation: {autocorr_1:.4f}")
    print(f"   Lag-2 autocorrelation: {autocorr_2:.4f}")

    if abs(autocorr_1) < 0.05 and abs(autocorr_2) < 0.05:
        print(f"   ✅ Low autocorrelation (MT-like)")
    else:
        print(f"   ⚠️ High autocorrelation (not MT-like)")

def analyze_hash_based_generation(df, game):
    """Analyze if numbers are generated using hash functions"""

    print(f"\n🔐 HASH-BASED GENERATION ANALYSIS")
    print("-" * 40)

    if game not in df.columns:
        return

    game_df = df[['date', game]].dropna()

    # Test if numbers correlate with date hashes
    correlations = {}

    for hash_func in ['md5', 'sha1', 'sha256']:
        hash_values = []

        for date in game_df['date']:
            date_str = date.strftime('%Y-%m-%d')

            if hash_func == 'md5':
                hash_obj = hashlib.md5(date_str.encode())
            elif hash_func == 'sha1':
                hash_obj = hashlib.sha1(date_str.encode())
            else:
                hash_obj = hashlib.sha256(date_str.encode())

            # Convert hash to number 0-99
            hash_int = int(hash_obj.hexdigest()[:8], 16) % 100
            hash_values.append(hash_int)

        # Calculate correlation
        game_values = pd.to_numeric(game_df[game], errors='coerce').dropna()

        if len(hash_values) == len(game_values):
            correlation = np.corrcoef(hash_values, game_values)[0, 1]
            correlations[hash_func] = correlation

            print(f"   {hash_func.upper()} correlation: {correlation:.4f}")

            if abs(correlation) > 0.3:
                print(f"   🚨 STRONG {hash_func.upper()} CORRELATION DETECTED!")

    # Test timestamp-based hashing
    timestamp_hashes = []
    for date in game_df['date']:
        timestamp = int(date.timestamp())
        hash_val = (timestamp * 1103515245 + 12345) % 100
        timestamp_hashes.append(hash_val)

    if len(timestamp_hashes) == len(game_values):
        ts_correlation = np.corrcoef(timestamp_hashes, game_values)[0, 1]
        print(f"   Timestamp hash correlation: {ts_correlation:.4f}")

        if abs(ts_correlation) > 0.3:
            print(f"   🚨 TIMESTAMP-BASED GENERATION DETECTED!")

def analyze_time_based_seeds(df, game):
    """Analyze if numbers are seeded based on time"""

    print(f"\n⏰ TIME-BASED SEED ANALYSIS")
    print("-" * 40)

    if game not in df.columns:
        return

    game_df = df[['date', game]].dropna()
    game_df['hour'] = game_df['date'].dt.hour
    game_df['minute'] = game_df['date'].dt.minute
    game_df['day_of_year'] = game_df['date'].dt.dayofyear
    game_df['timestamp'] = game_df['date'].astype(int) // 10**9

    game_values = pd.to_numeric(game_df[game], errors='coerce')

    # Test correlations with time components
    time_correlations = {}

    for time_component in ['hour', 'minute', 'day_of_year', 'timestamp']:
        if time_component in game_df.columns:
            correlation = np.corrcoef(game_df[time_component], game_values)[0, 1]
            time_correlations[time_component] = correlation

            print(f"   {time_component} correlation: {correlation:.4f}")

            if abs(correlation) > 0.1:
                print(f"   ⚠️ Potential {time_component}-based seeding")

    # Test for time-based modular patterns
    for mod in [24, 60, 365, 7]:  # Hours, minutes, days, week
        if 'hour' in game_df.columns:
            time_mod = game_df['hour'] % mod
            correlation = np.corrcoef(time_mod, game_values)[0, 1]

            if abs(correlation) > 0.15:
                print(f"   🚨 TIME MODULAR PATTERN: mod {mod}, correlation {correlation:.4f}")

def analyze_mathematical_sequences(data, game_name):
    """Deep analysis of mathematical sequences"""

    print(f"\n📐 MATHEMATICAL SEQUENCE ANALYSIS")
    print("-" * 40)

    # Test for various mathematical sequences
    sequences_found = []

    # Fibonacci-like sequences
    fib_correlation = test_fibonacci_like(data)
    if fib_correlation > 0.3:
        sequences_found.append(f"Fibonacci-like (r={fib_correlation:.3f})")

    # Prime number patterns
    prime_correlation = test_prime_patterns(data)
    if prime_correlation > 0.3:
        sequences_found.append(f"Prime-based (r={prime_correlation:.3f})")

    # Polynomial sequences
    poly_correlation = test_polynomial_sequences(data)
    if poly_correlation > 0.3:
        sequences_found.append(f"Polynomial (r={poly_correlation:.3f})")

    # Trigonometric patterns
    trig_correlation = test_trigonometric_patterns(data)
    if trig_correlation > 0.3:
        sequences_found.append(f"Trigonometric (r={trig_correlation:.3f})")

    if sequences_found:
        print(f"   🚨 MATHEMATICAL SEQUENCES DETECTED:")
        for seq in sequences_found:
            print(f"      • {seq}")
    else:
        print(f"   ✅ No strong mathematical sequences detected")

def test_fibonacci_like(data):
    """Test for Fibonacci-like sequences"""

    if len(data) < 10:
        return 0

    # Generate Fibonacci-like sequence starting with first two numbers
    fib_like = [data.iloc[0], data.iloc[1]]

    for i in range(2, min(len(data), 20)):
        next_val = (fib_like[-1] + fib_like[-2]) % 100
        fib_like.append(next_val)

    # Compare with actual data
    actual = data.iloc[:len(fib_like)].values

    if len(fib_like) == len(actual):
        return np.corrcoef(fib_like, actual)[0, 1]

    return 0

def test_prime_patterns(data):
    """Test for prime number patterns"""

    primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97]

    # Test if numbers correlate with prime indices
    prime_indices = []

    for num in data:
        if int(num) in primes:
            prime_indices.append(primes.index(int(num)))
        else:
            prime_indices.append(-1)

    # Remove -1 values and test correlation
    valid_indices = [i for i, x in enumerate(prime_indices) if x != -1]

    if len(valid_indices) > 5:
        prime_vals = [prime_indices[i] for i in valid_indices]
        data_vals = [data.iloc[i] for i in valid_indices]

        return np.corrcoef(prime_vals, data_vals)[0, 1]

    return 0

def test_polynomial_sequences(data):
    """Test for polynomial sequences"""

    if len(data) < 10:
        return 0

    # Test quadratic: an² + bn + c
    indices = np.arange(len(data))

    try:
        # Fit polynomial
        coeffs = np.polyfit(indices, data, 2)
        predicted = np.polyval(coeffs, indices) % 100

        return np.corrcoef(predicted, data)[0, 1]
    except:
        return 0

def test_trigonometric_patterns(data):
    """Test for trigonometric patterns"""

    if len(data) < 10:
        return 0

    indices = np.arange(len(data))

    # Test sine wave pattern
    try:
        # Try different frequencies
        best_correlation = 0

        for freq in [0.1, 0.2, 0.5, 1.0, 2.0]:
            sine_wave = 50 + 30 * np.sin(freq * indices)  # Centered at 50, amplitude 30
            correlation = np.corrcoef(sine_wave, data)[0, 1]

            if abs(correlation) > abs(best_correlation):
                best_correlation = correlation

        return best_correlation
    except:
        return 0

def analyze_modular_arithmetic_deep(data, game_name):
    """Deep dive into modular arithmetic patterns"""

    print(f"\n🔢 DEEP MODULAR ARITHMETIC ANALYSIS")
    print("-" * 40)

    # Test for complex modular relationships
    modular_patterns = {}

    for mod in range(2, 20):
        remainders = data % mod

        # Test for patterns in remainder sequences
        remainder_transitions = {}

        for i in range(len(remainders) - 1):
            current = int(remainders.iloc[i])
            next_val = int(remainders.iloc[i + 1])

            if current not in remainder_transitions:
                remainder_transitions[current] = []
            remainder_transitions[current].append(next_val)

        # Check for deterministic transitions
        deterministic_transitions = 0
        total_transitions = 0

        for current, next_vals in remainder_transitions.items():
            if len(next_vals) > 1:
                most_common = Counter(next_vals).most_common(1)[0]
                frequency = most_common[1] / len(next_vals)

                if frequency > 0.7:  # 70% or more transitions go to same remainder
                    deterministic_transitions += 1

                total_transitions += 1

        if total_transitions > 0:
            deterministic_ratio = deterministic_transitions / total_transitions
            modular_patterns[mod] = deterministic_ratio

            if deterministic_ratio > 0.5:
                print(f"   🚨 MODULAR PATTERN mod {mod}: {deterministic_ratio:.1%} deterministic")

    # Find the most deterministic modulus
    if modular_patterns:
        best_mod = max(modular_patterns.keys(), key=lambda x: modular_patterns[x])
        best_ratio = modular_patterns[best_mod]

        if best_ratio > 0.6:
            print(f"   🎯 STRONGEST PATTERN: mod {best_mod} ({best_ratio:.1%} deterministic)")

            # Show the transition rules
            remainders = data % best_mod
            transitions = {}

            for i in range(len(remainders) - 1):
                current = int(remainders.iloc[i])
                next_val = int(remainders.iloc[i + 1])

                if current not in transitions:
                    transitions[current] = []
                transitions[current].append(next_val)

            print(f"   Transition rules for mod {best_mod}:")
            for current, next_vals in transitions.items():
                most_common = Counter(next_vals).most_common(1)[0]
                print(f"      {current} → {most_common[0]} ({most_common[1]}/{len(next_vals)} times)")

def analyze_psychological_patterns(data, game_name):
    """Analyze psychological patterns in number selection"""

    print(f"\n🧠 PSYCHOLOGICAL PATTERN ANALYSIS")
    print("-" * 40)

    # Test for human-like biases even in algorithmic generation

    # Digit preference analysis
    all_digits = []
    for num in data:
        for digit in str(int(num)):
            all_digits.append(int(digit))

    digit_counts = Counter(all_digits)
    print(f"   Digit frequency: {dict(digit_counts)}")

    # Check for significant digit bias
    expected_per_digit = len(all_digits) / 10
    biased_digits = []

    for digit, count in digit_counts.items():
        if abs(count - expected_per_digit) > expected_per_digit * 0.2:
            bias_direction = "over" if count > expected_per_digit else "under"
            biased_digits.append(f"{digit} ({bias_direction}represented)")

    if biased_digits:
        print(f"   🚨 DIGIT BIAS: {', '.join(biased_digits)}")

    # Number preference analysis
    number_counts = Counter(data.astype(int))
    most_common = number_counts.most_common(5)
    least_common = number_counts.most_common()[-5:]

    print(f"   Most frequent: {[f'{num}({count})' for num, count in most_common]}")
    print(f"   Least frequent: {[f'{num}({count})' for num, count in least_common]}")

    # Check for psychological number preferences
    lucky_numbers = [7, 11, 21, 77, 88, 99]
    unlucky_numbers = [13, 4]

    lucky_frequency = sum(number_counts.get(num, 0) for num in lucky_numbers)
    unlucky_frequency = sum(number_counts.get(num, 0) for num in unlucky_numbers)

    expected_lucky = len(data) * len(lucky_numbers) / 100
    expected_unlucky = len(data) * len(unlucky_numbers) / 100

    print(f"   Lucky numbers frequency: {lucky_frequency} (expected: {expected_lucky:.1f})")
    print(f"   Unlucky numbers frequency: {unlucky_frequency} (expected: {expected_unlucky:.1f})")

def analyze_external_correlations(df, game):
    """Analyze correlations with external factors"""

    print(f"\n🌍 EXTERNAL CORRELATION ANALYSIS")
    print("-" * 40)

    if game not in df.columns:
        return

    game_df = df[['date', game]].dropna()
    game_values = pd.to_numeric(game_df[game], errors='coerce')

    # Test correlation with date components
    correlations = {}

    # Day of month
    day_correlation = np.corrcoef(game_df['date'].dt.day, game_values)[0, 1]
    correlations['day_of_month'] = day_correlation

    # Month
    month_correlation = np.corrcoef(game_df['date'].dt.month, game_values)[0, 1]
    correlations['month'] = month_correlation

    # Day of year
    dayofyear_correlation = np.corrcoef(game_df['date'].dt.dayofyear, game_values)[0, 1]
    correlations['day_of_year'] = dayofyear_correlation

    # Week of year
    week_correlation = np.corrcoef(game_df['date'].dt.isocalendar().week, game_values)[0, 1]
    correlations['week_of_year'] = week_correlation

    print(f"   Date correlations:")
    for factor, correlation in correlations.items():
        print(f"      {factor}: {correlation:.4f}")

        if abs(correlation) > 0.1:
            print(f"         🚨 SIGNIFICANT CORRELATION DETECTED!")

    # Test for moon phase correlation (if we had that data)
    # Test for market correlation (if we had that data)
    # Test for weather correlation (if we had that data)

    print(f"   📊 External factor analysis complete")

if __name__ == "__main__":
    deep_mathematics_analysis()