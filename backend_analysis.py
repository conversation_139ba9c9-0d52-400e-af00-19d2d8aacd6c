"""
Deep analysis of how Satta King sites get their data internally
Investigate backend systems, databases, and manipulation possibilities
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup
import os

class SattaBackendAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    def analyze_backend_systems(self):
        """Analyze how Satta King sites get their data internally"""
        
        print("🔍 SATTA KING BACKEND SYSTEM ANALYSIS")
        print("=" * 60)
        print("Investigating how these sites actually get their data...")
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'server_analysis': {},
            'database_evidence': {},
            'admin_systems': {},
            'manipulation_evidence': {},
            'data_flow_analysis': {},
            'conclusions': {}
        }
        
        # Analyze multiple sites for backend patterns
        sites = [
            "https://satta-king-fast.com",
            "https://sattaking.com",
            "https://sattakinggali.com"
        ]
        
        for site in sites:
            print(f"\n🔍 ANALYZING BACKEND: {site}")
            print("-" * 50)
            
            site_analysis = self.analyze_single_site_backend(site)
            analysis[site] = site_analysis
        
        # Cross-site analysis
        print(f"\n🔍 CROSS-SITE BACKEND ANALYSIS")
        print("-" * 50)
        
        cross_analysis = self.cross_site_backend_analysis(analysis)
        analysis['cross_site_analysis'] = cross_analysis
        
        # Generate conclusions about manipulation possibilities
        manipulation_analysis = self.analyze_manipulation_possibilities(analysis)
        analysis['manipulation_analysis'] = manipulation_analysis
        
        # Save detailed report
        self.save_backend_analysis(analysis)
        
        return analysis
    
    def analyze_single_site_backend(self, site_url):
        """Analyze backend systems of a single site"""
        
        backend_info = {
            'server_technology': {},
            'database_hints': [],
            'admin_interfaces': [],
            'update_mechanisms': [],
            'security_analysis': {},
            'manipulation_indicators': []
        }
        
        try:
            # 1. Server Technology Analysis
            print("   🖥️ Server Technology Analysis...")
            backend_info['server_technology'] = self.analyze_server_tech(site_url)
            
            # 2. Database Investigation
            print("   💾 Database Investigation...")
            backend_info['database_hints'] = self.investigate_database(site_url)
            
            # 3. Admin Interface Discovery
            print("   🔐 Admin Interface Discovery...")
            backend_info['admin_interfaces'] = self.discover_admin_interfaces(site_url)
            
            # 4. Update Mechanism Analysis
            print("   🔄 Update Mechanism Analysis...")
            backend_info['update_mechanisms'] = self.analyze_update_mechanisms(site_url)
            
            # 5. Security Analysis
            print("   🛡️ Security Analysis...")
            backend_info['security_analysis'] = self.analyze_security(site_url)
            
            # 6. Manipulation Indicators
            print("   ⚠️ Manipulation Indicators...")
            backend_info['manipulation_indicators'] = self.find_manipulation_indicators(site_url)
            
        except Exception as e:
            print(f"   ❌ Error analyzing {site_url}: {str(e)}")
            backend_info['error'] = str(e)
        
        return backend_info
    
    def analyze_server_tech(self, site_url):
        """Analyze server technology stack"""
        
        tech_info = {
            'server_software': 'unknown',
            'programming_language': 'unknown',
            'framework': 'unknown',
            'database_type': 'unknown',
            'hosting_provider': 'unknown',
            'cdn_usage': False,
            'evidence': []
        }
        
        try:
            response = self.session.get(site_url, timeout=10)
            
            # Server headers analysis
            headers = response.headers
            
            # Server software
            server = headers.get('server', '').lower()
            if 'apache' in server:
                tech_info['server_software'] = 'Apache'
            elif 'nginx' in server:
                tech_info['server_software'] = 'Nginx'
            elif 'cloudflare' in server:
                tech_info['server_software'] = 'Cloudflare'
                tech_info['cdn_usage'] = True
            
            # Programming language hints
            content = response.text.lower()
            if '.php' in content or 'php' in headers.get('x-powered-by', ''):
                tech_info['programming_language'] = 'PHP'
                tech_info['evidence'].append('PHP detected in content/headers')
            elif '.asp' in content:
                tech_info['programming_language'] = 'ASP.NET'
            elif 'django' in content or 'python' in headers.get('x-powered-by', ''):
                tech_info['programming_language'] = 'Python'
            
            # Framework detection
            if 'wordpress' in content:
                tech_info['framework'] = 'WordPress'
            elif 'codeigniter' in content:
                tech_info['framework'] = 'CodeIgniter'
            elif 'laravel' in content:
                tech_info['framework'] = 'Laravel'
            
            # CDN detection
            cdn_headers = ['cf-ray', 'x-cache', 'x-served-by']
            for header in cdn_headers:
                if header in headers:
                    tech_info['cdn_usage'] = True
                    tech_info['evidence'].append(f'CDN detected: {header}')
            
            print(f"     Server: {tech_info['server_software']}")
            print(f"     Language: {tech_info['programming_language']}")
            print(f"     CDN: {tech_info['cdn_usage']}")
            
        except Exception as e:
            tech_info['error'] = str(e)
        
        return tech_info
    
    def investigate_database(self, site_url):
        """Investigate database usage and type"""
        
        db_hints = []
        
        try:
            # Test for SQL injection to reveal database info
            test_params = [
                "?id='",
                "?id=1'",
                "?id=1 AND 1=1",
                "?id=1 UNION SELECT 1",
                "?game=test'"
            ]
            
            for param in test_params:
                try:
                    test_url = site_url + param
                    response = self.session.get(test_url, timeout=5)
                    content = response.text.lower()
                    
                    # Look for database error messages
                    db_errors = [
                        ('mysql', 'MySQL database'),
                        ('postgresql', 'PostgreSQL database'),
                        ('sqlite', 'SQLite database'),
                        ('oracle', 'Oracle database'),
                        ('sql server', 'SQL Server database'),
                        ('syntax error', 'SQL syntax error'),
                        ('table', 'Database table reference'),
                        ('column', 'Database column reference'),
                        ('query', 'Database query error')
                    ]
                    
                    for error_pattern, db_type in db_errors:
                        if error_pattern in content:
                            db_hints.append({
                                'type': db_type,
                                'evidence': f'Found "{error_pattern}" in response to {param}',
                                'confidence': 'medium'
                            })
                
                except:
                    continue
                
                time.sleep(0.3)
            
            # Look for database connection files
            db_files = [
                '/config.php',
                '/database.php',
                '/db.php',
                '/connection.php',
                '/settings.php'
            ]
            
            for file_path in db_files:
                try:
                    file_url = site_url.rstrip('/') + file_path
                    response = self.session.get(file_url, timeout=5)
                    
                    if response.status_code == 200:
                        content = response.text.lower()
                        if any(keyword in content for keyword in ['mysql', 'database', 'password', 'host']):
                            db_hints.append({
                                'type': 'Database configuration file',
                                'evidence': f'Accessible config file: {file_path}',
                                'confidence': 'high'
                            })
                
                except:
                    continue
        
        except Exception as e:
            db_hints.append({'error': str(e)})
        
        print(f"     Database hints found: {len(db_hints)}")
        return db_hints
    
    def discover_admin_interfaces(self, site_url):
        """Discover admin interfaces and control panels"""
        
        admin_interfaces = []
        
        # Common admin paths
        admin_paths = [
            '/admin', '/admin.php', '/admin/', '/administrator',
            '/manage', '/control', '/panel', '/dashboard',
            '/wp-admin', '/login', '/login.php', '/signin',
            '/backend', '/cms', '/control-panel',
            '/satta-admin', '/result-admin', '/game-admin'
        ]
        
        for path in admin_paths:
            try:
                admin_url = site_url.rstrip('/') + path
                response = self.session.head(admin_url, timeout=5)
                
                if response.status_code in [200, 301, 302, 401, 403]:
                    admin_interfaces.append({
                        'path': path,
                        'url': admin_url,
                        'status': response.status_code,
                        'accessible': response.status_code == 200,
                        'protected': response.status_code in [401, 403],
                        'redirect': response.status_code in [301, 302]
                    })
                    
                    if response.status_code == 200:
                        print(f"     ✅ Found accessible admin: {path}")
                    elif response.status_code in [401, 403]:
                        print(f"     🔒 Found protected admin: {path}")
            
            except:
                continue
            
            time.sleep(0.2)
        
        return admin_interfaces
    
    def analyze_update_mechanisms(self, site_url):
        """Analyze how results are updated"""
        
        update_mechanisms = {
            'method': 'unknown',
            'frequency': 'unknown',
            'automation': False,
            'manual_control': False,
            'evidence': []
        }
        
        try:
            response = self.session.get(site_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for auto-refresh
            refresh_meta = soup.find('meta', attrs={'http-equiv': 'refresh'})
            if refresh_meta:
                update_mechanisms['method'] = 'auto_refresh'
                update_mechanisms['automation'] = True
                update_mechanisms['evidence'].append(f"Auto-refresh: {refresh_meta.get('content')}")
            
            # Check for JavaScript polling
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    content = script.string.lower()
                    
                    if 'setinterval' in content:
                        update_mechanisms['method'] = 'javascript_polling'
                        update_mechanisms['automation'] = True
                        update_mechanisms['evidence'].append("JavaScript polling detected")
                    
                    if any(keyword in content for keyword in ['admin', 'update', 'result']):
                        update_mechanisms['manual_control'] = True
                        update_mechanisms['evidence'].append("Manual control keywords in JavaScript")
            
            # Check cache headers
            cache_control = response.headers.get('cache-control', '')
            if 'no-cache' in cache_control:
                update_mechanisms['evidence'].append("No-cache headers suggest real-time updates")
            
        except Exception as e:
            update_mechanisms['error'] = str(e)
        
        return update_mechanisms
    
    def analyze_security(self, site_url):
        """Analyze security measures and vulnerabilities"""
        
        security = {
            'https_enabled': False,
            'security_headers': [],
            'vulnerabilities': [],
            'protection_level': 'unknown'
        }
        
        try:
            # HTTPS check
            security['https_enabled'] = site_url.startswith('https://')
            
            response = self.session.get(site_url)
            headers = response.headers
            
            # Security headers check
            security_headers = [
                'x-frame-options',
                'x-content-type-options',
                'x-xss-protection',
                'strict-transport-security',
                'content-security-policy'
            ]
            
            for header in security_headers:
                if header in headers:
                    security['security_headers'].append(header)
            
            # Basic vulnerability tests
            vuln_tests = [
                ('XSS', "?test=<script>alert('xss')</script>"),
                ('SQL Injection', "?id=1'"),
                ('Directory Traversal', "?file=../../../etc/passwd")
            ]
            
            for vuln_type, test_param in vuln_tests:
                try:
                    test_url = site_url + test_param
                    vuln_response = self.session.get(test_url, timeout=5)
                    
                    if vuln_type == 'XSS' and '<script>' in vuln_response.text:
                        security['vulnerabilities'].append('Potential XSS vulnerability')
                    elif vuln_type == 'SQL Injection' and any(error in vuln_response.text.lower() for error in ['sql', 'mysql', 'error']):
                        security['vulnerabilities'].append('Potential SQL injection vulnerability')
                
                except:
                    continue
            
            # Determine protection level
            if len(security['security_headers']) >= 3 and not security['vulnerabilities']:
                security['protection_level'] = 'high'
            elif len(security['security_headers']) >= 1 and len(security['vulnerabilities']) <= 1:
                security['protection_level'] = 'medium'
            else:
                security['protection_level'] = 'low'
        
        except Exception as e:
            security['error'] = str(e)
        
        return security
    
    def find_manipulation_indicators(self, site_url):
        """Look for indicators of result manipulation"""
        
        indicators = []
        
        try:
            response = self.session.get(site_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            content = response.text.lower()
            
            # Look for admin controls in HTML
            if any(keyword in content for keyword in ['admin', 'control', 'manage', 'update result']):
                indicators.append({
                    'type': 'Admin controls detected',
                    'evidence': 'Admin-related keywords found in page content',
                    'risk_level': 'medium'
                })
            
            # Look for JavaScript result generation
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    script_content = script.string.lower()
                    
                    if 'math.random' in script_content:
                        indicators.append({
                            'type': 'Client-side random generation',
                            'evidence': 'Math.random() found in JavaScript',
                            'risk_level': 'high'
                        })
                    
                    if any(keyword in script_content for keyword in ['generate', 'create', 'set result']):
                        indicators.append({
                            'type': 'Result generation code',
                            'evidence': 'Result generation keywords in JavaScript',
                            'risk_level': 'high'
                        })
            
            # Check for hidden forms
            hidden_forms = soup.find_all('form', style=re.compile(r'display:\s*none', re.I))
            if hidden_forms:
                indicators.append({
                    'type': 'Hidden forms detected',
                    'evidence': f'Found {len(hidden_forms)} hidden forms',
                    'risk_level': 'medium'
                })
            
            # Check for suspicious parameters
            if '?' in site_url or 'admin' in site_url:
                indicators.append({
                    'type': 'Suspicious URL parameters',
                    'evidence': 'URL contains parameters that might control results',
                    'risk_level': 'low'
                })
        
        except Exception as e:
            indicators.append({'error': str(e)})
        
        print(f"     Manipulation indicators: {len(indicators)}")
        return indicators
    
    def cross_site_backend_analysis(self, analysis):
        """Analyze patterns across multiple sites"""
        
        cross_analysis = {
            'shared_technologies': [],
            'common_vulnerabilities': [],
            'similar_admin_systems': [],
            'centralized_backend': False,
            'evidence': []
        }
        
        # Extract technology info from all sites
        technologies = []
        admin_systems = []
        vulnerabilities = []
        
        for site, data in analysis.items():
            if isinstance(data, dict) and 'server_technology' in data:
                tech = data['server_technology']
                technologies.append(tech.get('programming_language', 'unknown'))
                
                if data.get('admin_interfaces'):
                    admin_systems.extend([admin['path'] for admin in data['admin_interfaces']])
                
                if data.get('security_analysis', {}).get('vulnerabilities'):
                    vulnerabilities.extend(data['security_analysis']['vulnerabilities'])
        
        # Find common patterns
        from collections import Counter
        
        tech_counts = Counter(technologies)
        common_tech = [tech for tech, count in tech_counts.items() if count > 1]
        cross_analysis['shared_technologies'] = common_tech
        
        admin_counts = Counter(admin_systems)
        common_admin = [path for path, count in admin_counts.items() if count > 1]
        cross_analysis['similar_admin_systems'] = common_admin
        
        vuln_counts = Counter(vulnerabilities)
        common_vulns = [vuln for vuln, count in vuln_counts.items() if count > 1]
        cross_analysis['common_vulnerabilities'] = common_vulns
        
        # Check for centralized backend evidence
        if len(common_tech) > 0 and len(common_admin) > 0:
            cross_analysis['centralized_backend'] = True
            cross_analysis['evidence'].append("Similar technologies and admin systems suggest centralized backend")
        
        return cross_analysis
    
    def analyze_manipulation_possibilities(self, analysis):
        """Analyze the possibilities and methods of result manipulation"""
        
        manipulation = {
            'manipulation_possible': False,
            'manipulation_methods': [],
            'risk_level': 'unknown',
            'evidence': [],
            'protection_measures': [],
            'recommendations': []
        }
        
        # Analyze all collected evidence
        total_indicators = 0
        high_risk_indicators = 0
        
        for site, data in analysis.items():
            if isinstance(data, dict) and 'manipulation_indicators' in data:
                indicators = data['manipulation_indicators']
                total_indicators += len(indicators)
                
                for indicator in indicators:
                    if indicator.get('risk_level') == 'high':
                        high_risk_indicators += 1
                        manipulation['manipulation_methods'].append(indicator['type'])
        
        # Determine manipulation possibility
        if high_risk_indicators > 0:
            manipulation['manipulation_possible'] = True
            manipulation['risk_level'] = 'high'
        elif total_indicators > 3:
            manipulation['manipulation_possible'] = True
            manipulation['risk_level'] = 'medium'
        else:
            manipulation['risk_level'] = 'low'
        
        # Generate evidence and recommendations
        if manipulation['manipulation_possible']:
            manipulation['evidence'].append(f"Found {high_risk_indicators} high-risk manipulation indicators")
            manipulation['evidence'].append(f"Total manipulation indicators: {total_indicators}")
            
            manipulation['recommendations'] = [
                "Implement cryptographic result verification",
                "Use blockchain or immutable ledger for result storage",
                "Implement multi-party result generation",
                "Add real-time monitoring and auditing",
                "Use external random number generation services"
            ]
        
        return manipulation
    
    def save_backend_analysis(self, analysis):
        """Save the backend analysis report"""
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/backend_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f"\n💾 Backend analysis saved to: {report_file}")
        
        # Print summary
        self.print_backend_summary(analysis)
    
    def print_backend_summary(self, analysis):
        """Print summary of backend analysis"""
        
        print(f"\n📋 BACKEND ANALYSIS SUMMARY")
        print("=" * 60)
        
        manipulation = analysis.get('manipulation_analysis', {})
        cross_analysis = analysis.get('cross_site_analysis', {})
        
        print(f"Manipulation Possible: {manipulation.get('manipulation_possible', 'Unknown')}")
        print(f"Risk Level: {manipulation.get('risk_level', 'Unknown')}")
        print(f"Centralized Backend: {cross_analysis.get('centralized_backend', 'Unknown')}")
        
        methods = manipulation.get('manipulation_methods', [])
        if methods:
            print(f"\nPossible Manipulation Methods:")
            for method in set(methods):  # Remove duplicates
                print(f"  • {method}")
        
        evidence = manipulation.get('evidence', [])
        if evidence:
            print(f"\nEvidence:")
            for item in evidence:
                print(f"  • {item}")

if __name__ == "__main__":
    analyzer = SattaBackendAnalyzer()
    analysis = analyzer.analyze_backend_systems()
