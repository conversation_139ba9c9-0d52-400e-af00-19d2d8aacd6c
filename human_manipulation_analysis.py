"""
Analysis to determine if humans are manually manipulating results vs algorithmic generation
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
from collections import Counter
from scipy import stats
import matplotlib.pyplot as plt

def analyze_human_vs_algorithmic():
    """Analyze whether results are human-manipulated or algorithmically generated"""

    print("🕵️ HUMAN MANIPULATION vs ALGORITHMIC ANALYSIS")
    print("=" * 70)
    print("Investigating: Are humans manually setting the numbers?")
    print("=" * 70)

    # Load data
    data_file = 'data/raw/extensive_historical_data.csv'
    if not os.path.exists(data_file):
        print("❌ No data file found")
        return

    df = pd.read_csv(data_file)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    print(f"✅ Loaded {len(df)} records for analysis")

    analysis_results = {}

    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()

            print(f"\n🔍 ANALYZING {game}")
            print("-" * 40)

            # Test 1: Human bias patterns
            human_bias_results = test_human_bias_patterns(game_data, game)

            # Test 2: Timing patterns (humans vs algorithms)
            timing_results = test_timing_patterns(df, game)

            # Test 3: Psychological number preferences
            psychological_results = test_psychological_preferences(game_data, game)

            # Test 4: Consistency patterns
            consistency_results = test_consistency_patterns(game_data, game)

            # Test 5: Response to external events
            external_response_results = test_external_event_response(df, game)

            # Test 6: Update mechanism analysis
            update_mechanism_results = analyze_update_mechanisms(df, game)

            analysis_results[game] = {
                'human_bias': human_bias_results,
                'timing_patterns': timing_results,
                'psychological_preferences': psychological_results,
                'consistency': consistency_results,
                'external_response': external_response_results,
                'update_mechanism': update_mechanism_results,
                'verdict': determine_manipulation_type(
                    human_bias_results, timing_results, psychological_results,
                    consistency_results, external_response_results, update_mechanism_results
                )
            }

    # Generate comprehensive report
    generate_manipulation_report(analysis_results)

    return analysis_results

def test_human_bias_patterns(data, game_name):
    """Test for patterns that indicate human bias"""

    print(f"   👤 Testing human bias patterns...")

    bias_indicators = {
        'round_number_bias': 0,
        'digit_preference_bias': 0,
        'avoidance_patterns': 0,
        'clustering_bias': 0,
        'total_bias_score': 0
    }

    # Test 1: Round number bias (humans prefer round numbers)
    round_numbers = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95]
    round_count = sum(1 for x in data if x in round_numbers)
    expected_round = len(data) * (len(round_numbers) / 100)

    if round_count > expected_round * 1.2:  # 20% more than expected
        bias_indicators['round_number_bias'] = (round_count - expected_round) / expected_round
        print(f"      🚨 Round number bias detected: {bias_indicators['round_number_bias']:.2f}")
    else:
        print(f"      ✅ No round number bias")

    # Test 2: Digit preference (humans prefer certain digits)
    digit_preferences = {}
    for num in data:
        for digit in str(int(num)):
            digit_preferences[digit] = digit_preferences.get(digit, 0) + 1

    if digit_preferences:
        digit_variance = np.var(list(digit_preferences.values()))
        expected_variance = len(data) * 0.1  # Expected for uniform distribution

        if digit_variance > expected_variance * 1.5:
            bias_indicators['digit_preference_bias'] = digit_variance / expected_variance
            print(f"      🚨 Digit preference bias detected: {bias_indicators['digit_preference_bias']:.2f}")
        else:
            print(f"      ✅ No digit preference bias")

    # Test 3: Avoidance patterns (humans avoid certain numbers)
    # Check for numbers that appear significantly less than expected
    value_counts = data.value_counts()
    expected_freq = len(data) / 100

    avoided_numbers = []
    for i in range(100):
        actual_freq = value_counts.get(i, 0)
        if actual_freq < expected_freq * 0.5:  # Less than half expected
            avoided_numbers.append(i)

    if len(avoided_numbers) > 10:  # More than 10% of numbers avoided
        bias_indicators['avoidance_patterns'] = len(avoided_numbers) / 100
        print(f"      🚨 Number avoidance detected: {len(avoided_numbers)} numbers avoided")
    else:
        print(f"      ✅ No significant number avoidance")

    # Test 4: Clustering bias (humans tend to cluster numbers)
    consecutive_pairs = 0
    for i in range(len(data) - 1):
        if abs(data.iloc[i] - data.iloc[i+1]) <= 5:  # Numbers within 5 of each other
            consecutive_pairs += 1

    expected_pairs = len(data) * 0.1  # Expected for random
    if consecutive_pairs > expected_pairs * 1.3:
        bias_indicators['clustering_bias'] = consecutive_pairs / expected_pairs
        print(f"      🚨 Clustering bias detected: {bias_indicators['clustering_bias']:.2f}")
    else:
        print(f"      ✅ No clustering bias")

    # Calculate total bias score
    bias_indicators['total_bias_score'] = sum([
        bias_indicators['round_number_bias'],
        bias_indicators['digit_preference_bias'],
        bias_indicators['avoidance_patterns'],
        bias_indicators['clustering_bias']
    ])

    return bias_indicators

def test_timing_patterns(df, game):
    """Test timing patterns that might indicate human vs algorithmic generation"""

    print(f"   ⏰ Testing timing patterns...")

    timing_results = {
        'update_regularity': 'UNKNOWN',
        'weekend_patterns': False,
        'holiday_patterns': False,
        'time_consistency': 'UNKNOWN',
        'human_schedule_correlation': 0
    }

    if 'date' in df.columns and game in df.columns:
        game_df = df[['date', game]].dropna()
        game_df['day_of_week'] = game_df['date'].dt.dayofweek
        game_df['hour'] = game_df['date'].dt.hour

        # Test update regularity
        date_diffs = game_df['date'].diff().dropna()
        if len(date_diffs) > 0:
            most_common_diff = date_diffs.mode().iloc[0] if len(date_diffs.mode()) > 0 else None

            if most_common_diff:
                regularity = (date_diffs == most_common_diff).sum() / len(date_diffs)
                if regularity > 0.8:
                    timing_results['update_regularity'] = 'HIGHLY_REGULAR'
                    print(f"      🤖 Highly regular updates: {regularity:.1%}")
                elif regularity > 0.6:
                    timing_results['update_regularity'] = 'SOMEWHAT_REGULAR'
                    print(f"      ⚡ Somewhat regular updates: {regularity:.1%}")
                else:
                    timing_results['update_regularity'] = 'IRREGULAR'
                    print(f"      👤 Irregular updates: {regularity:.1%}")

        # Test weekend patterns (humans less active on weekends)
        weekday_counts = game_df['day_of_week'].value_counts()
        weekend_count = weekday_counts.get(5, 0) + weekday_counts.get(6, 0)  # Sat + Sun
        weekday_count = sum(weekday_counts.get(i, 0) for i in range(5))  # Mon-Fri

        if len(game_df) > 0:
            weekend_ratio = weekend_count / len(game_df)
            expected_weekend_ratio = 2/7  # 2 days out of 7

            if weekend_ratio < expected_weekend_ratio * 0.7:  # Significantly fewer weekend updates
                timing_results['weekend_patterns'] = True
                timing_results['human_schedule_correlation'] += 0.3
                print(f"      👤 Weekend reduction detected: {weekend_ratio:.1%} vs expected {expected_weekend_ratio:.1%}")
            else:
                print(f"      🤖 No weekend reduction")

        # Test hour patterns (humans work during business hours)
        if 'hour' in game_df.columns and not game_df['hour'].isna().all():
            hour_counts = game_df['hour'].value_counts()
            business_hours = sum(hour_counts.get(i, 0) for i in range(9, 18))  # 9 AM - 6 PM
            total_hours = len(game_df)

            if total_hours > 0:
                business_ratio = business_hours / total_hours
                expected_business_ratio = 9/24  # 9 hours out of 24

                if business_ratio > expected_business_ratio * 1.5:
                    timing_results['time_consistency'] = 'BUSINESS_HOURS_BIAS'
                    timing_results['human_schedule_correlation'] += 0.4
                    print(f"      👤 Business hours bias: {business_ratio:.1%}")
                else:
                    timing_results['time_consistency'] = 'DISTRIBUTED'
                    print(f"      🤖 Evenly distributed timing")

    return timing_results

def test_psychological_preferences(data, game_name):
    """Test for psychological number preferences that humans exhibit"""

    print(f"   🧠 Testing psychological preferences...")

    psychological_results = {
        'lucky_number_bias': False,
        'unlucky_number_bias': False,
        'middle_number_preference': False,
        'extreme_avoidance': False,
        'pattern_seeking': False,
        'psychological_score': 0
    }

    # Test 1: Lucky numbers (7, 11, 21, 77, etc.)
    lucky_numbers = [7, 11, 21, 77, 88, 99]
    lucky_count = sum(1 for x in data if x in lucky_numbers)
    expected_lucky = len(data) * (len(lucky_numbers) / 100)

    if lucky_count > expected_lucky * 1.5:
        psychological_results['lucky_number_bias'] = True
        psychological_results['psychological_score'] += 1
        print(f"      🍀 Lucky number bias detected")
    else:
        print(f"      ✅ No lucky number bias")

    # Test 2: Unlucky numbers (13, 4, 666 doesn't apply here)
    unlucky_numbers = [13, 4]
    unlucky_count = sum(1 for x in data if x in unlucky_numbers)
    expected_unlucky = len(data) * (len(unlucky_numbers) / 100)

    if unlucky_count < expected_unlucky * 0.5:
        psychological_results['unlucky_number_bias'] = True
        psychological_results['psychological_score'] += 1
        print(f"      😰 Unlucky number avoidance detected")
    else:
        print(f"      ✅ No unlucky number avoidance")

    # Test 3: Middle number preference (humans prefer middle ranges)
    middle_range = [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]
    middle_count = sum(1 for x in data if x in middle_range)
    expected_middle = len(data) * (len(middle_range) / 100)

    if middle_count > expected_middle * 1.2:
        psychological_results['middle_number_preference'] = True
        psychological_results['psychological_score'] += 1
        print(f"      🎯 Middle number preference detected")
    else:
        print(f"      ✅ No middle number preference")

    # Test 4: Extreme avoidance (very low or very high numbers)
    extreme_low = [0, 1, 2, 3, 4]
    extreme_high = [95, 96, 97, 98, 99]
    extreme_count = sum(1 for x in data if x in extreme_low + extreme_high)
    expected_extreme = len(data) * (len(extreme_low + extreme_high) / 100)

    if extreme_count < expected_extreme * 0.6:
        psychological_results['extreme_avoidance'] = True
        psychological_results['psychological_score'] += 1
        print(f"      🚫 Extreme number avoidance detected")
    else:
        print(f"      ✅ No extreme number avoidance")

    # Test 5: Pattern seeking (humans try to create patterns)
    pattern_count = 0
    for i in range(len(data) - 2):
        # Check for simple arithmetic progressions
        if i + 2 < len(data):
            diff1 = data.iloc[i+1] - data.iloc[i]
            diff2 = data.iloc[i+2] - data.iloc[i+1]
            if diff1 == diff2 and diff1 != 0:
                pattern_count += 1

    pattern_ratio = pattern_count / max(1, len(data) - 2)
    if pattern_ratio > 0.05:  # More than 5% patterns
        psychological_results['pattern_seeking'] = True
        psychological_results['psychological_score'] += 1
        print(f"      🔄 Pattern seeking detected: {pattern_ratio:.1%}")
    else:
        print(f"      ✅ No excessive pattern seeking")

    return psychological_results

def test_consistency_patterns(data, game_name):
    """Test consistency patterns that distinguish human vs algorithmic generation"""

    print(f"   📊 Testing consistency patterns...")

    consistency_results = {
        'variance_consistency': 'UNKNOWN',
        'distribution_stability': 'UNKNOWN',
        'entropy_consistency': 'UNKNOWN',
        'algorithmic_signature': False
    }

    # Test variance consistency over time
    if len(data) >= 100:
        # Split data into chunks and calculate variance for each
        chunk_size = len(data) // 10
        variances = []

        for i in range(0, len(data) - chunk_size, chunk_size):
            chunk = data.iloc[i:i+chunk_size]
            if len(chunk) > 1:
                variances.append(np.var(chunk))

        if len(variances) > 1:
            variance_of_variances = np.var(variances)

            if variance_of_variances < 50:  # Very consistent variance
                consistency_results['variance_consistency'] = 'HIGHLY_CONSISTENT'
                consistency_results['algorithmic_signature'] = True
                print(f"      🤖 Highly consistent variance (algorithmic)")
            elif variance_of_variances < 200:
                consistency_results['variance_consistency'] = 'MODERATELY_CONSISTENT'
                print(f"      ⚡ Moderately consistent variance")
            else:
                consistency_results['variance_consistency'] = 'INCONSISTENT'
                print(f"      👤 Inconsistent variance (human-like)")

    # Test distribution stability
    if len(data) >= 200:
        # Compare first half vs second half distribution
        mid_point = len(data) // 2
        first_half = data.iloc[:mid_point]
        second_half = data.iloc[mid_point:]

        # Kolmogorov-Smirnov test
        ks_stat, ks_p = stats.ks_2samp(first_half, second_half)

        if ks_p > 0.1:  # Distributions are very similar
            consistency_results['distribution_stability'] = 'STABLE'
            consistency_results['algorithmic_signature'] = True
            print(f"      🤖 Stable distribution (algorithmic)")
        elif ks_p > 0.05:
            consistency_results['distribution_stability'] = 'SOMEWHAT_STABLE'
            print(f"      ⚡ Somewhat stable distribution")
        else:
            consistency_results['distribution_stability'] = 'UNSTABLE'
            print(f"      👤 Unstable distribution (human-like)")

    return consistency_results

def test_external_event_response(df, game):
    """Test if numbers respond to external events (human manipulation indicator)"""

    print(f"   🌍 Testing external event response...")

    external_results = {
        'date_correlation': False,
        'special_date_patterns': False,
        'event_response_detected': False
    }

    if 'date' in df.columns and game in df.columns:
        game_df = df[['date', game]].dropna()

        # Test for special date patterns
        game_df['day'] = game_df['date'].dt.day
        game_df['month'] = game_df['date'].dt.month

        # Check if numbers correlate with dates
        date_correlation = np.corrcoef(game_df['day'], game_df[game])[0, 1]

        if abs(date_correlation) > 0.1:
            external_results['date_correlation'] = True
            print(f"      🚨 Date correlation detected: {date_correlation:.3f}")
        else:
            print(f"      ✅ No date correlation")

        # Check for special dates (1st, 15th, last day of month)
        special_days = [1, 15, 28, 29, 30, 31]
        special_day_numbers = game_df[game_df['day'].isin(special_days)][game]
        regular_day_numbers = game_df[~game_df['day'].isin(special_days)][game]

        if len(special_day_numbers) > 0 and len(regular_day_numbers) > 0:
            # Test if special days have different number patterns
            ks_stat, ks_p = stats.ks_2samp(special_day_numbers, regular_day_numbers)

            if ks_p < 0.05:
                external_results['special_date_patterns'] = True
                external_results['event_response_detected'] = True
                print(f"      🚨 Special date patterns detected")
            else:
                print(f"      ✅ No special date patterns")

    return external_results

def analyze_update_mechanisms(df, game):
    """Analyze the update mechanism to determine if it's human or algorithmic"""

    print(f"   🔧 Analyzing update mechanisms...")

    update_results = {
        'update_precision': 'UNKNOWN',
        'batch_updates': False,
        'manual_intervention_signs': False,
        'mechanism_type': 'UNKNOWN'
    }

    if 'date' in df.columns and game in df.columns:
        game_df = df[['date', game]].dropna()

        # Check update timing precision
        if len(game_df) > 1:
            time_diffs = game_df['date'].diff().dropna()

            # Convert to hours for analysis
            time_diffs_hours = time_diffs.dt.total_seconds() / 3600

            # Check if updates happen at exact intervals
            rounded_diffs = time_diffs_hours.round()
            exact_interval_ratio = (time_diffs_hours == rounded_diffs).sum() / len(time_diffs_hours)

            if exact_interval_ratio > 0.8:
                update_results['update_precision'] = 'EXACT_INTERVALS'
                update_results['mechanism_type'] = 'ALGORITHMIC'
                print(f"      🤖 Exact interval updates (algorithmic)")
            elif exact_interval_ratio > 0.6:
                update_results['update_precision'] = 'MOSTLY_REGULAR'
                print(f"      ⚡ Mostly regular updates")
            else:
                update_results['update_precision'] = 'IRREGULAR'
                update_results['mechanism_type'] = 'MANUAL'
                print(f"      👤 Irregular updates (manual)")

        # Check for batch updates (multiple games updated simultaneously)
        # This would require cross-game analysis
        # For now, we'll check if updates cluster around certain times
        if 'hour' in game_df.columns:
            hour_counts = game_df['hour'].value_counts()
            if len(hour_counts) > 0:
                max_hour_count = hour_counts.max()
                total_updates = len(game_df)

                if max_hour_count > total_updates * 0.3:  # More than 30% in one hour
                    update_results['batch_updates'] = True
                    print(f"      📦 Batch updates detected")
                else:
                    print(f"      ✅ No batch update pattern")

    return update_results

def determine_manipulation_type(human_bias, timing, psychological, consistency, external, update_mechanism):
    """Determine the type of manipulation based on all tests"""

    human_indicators = 0
    algorithmic_indicators = 0

    # Count human indicators
    if human_bias['total_bias_score'] > 1:
        human_indicators += 2

    if timing['human_schedule_correlation'] > 0.5:
        human_indicators += 2

    if psychological['psychological_score'] > 2:
        human_indicators += 2

    if consistency['variance_consistency'] == 'INCONSISTENT':
        human_indicators += 1

    if external['event_response_detected']:
        human_indicators += 2

    if update_mechanism['mechanism_type'] == 'MANUAL':
        human_indicators += 2

    # Count algorithmic indicators
    if consistency['algorithmic_signature']:
        algorithmic_indicators += 2

    if timing['update_regularity'] == 'HIGHLY_REGULAR':
        algorithmic_indicators += 2

    if update_mechanism['mechanism_type'] == 'ALGORITHMIC':
        algorithmic_indicators += 2

    if human_bias['total_bias_score'] < 0.5:
        algorithmic_indicators += 1

    # Determine verdict
    if human_indicators >= algorithmic_indicators + 2:
        return {
            'type': 'HUMAN_MANIPULATION',
            'confidence': 'HIGH' if human_indicators > algorithmic_indicators + 3 else 'MEDIUM',
            'human_score': human_indicators,
            'algorithmic_score': algorithmic_indicators
        }
    elif algorithmic_indicators >= human_indicators + 2:
        return {
            'type': 'ALGORITHMIC_GENERATION',
            'confidence': 'HIGH' if algorithmic_indicators > human_indicators + 3 else 'MEDIUM',
            'human_score': human_indicators,
            'algorithmic_score': algorithmic_indicators
        }
    else:
        return {
            'type': 'MIXED_OR_UNCERTAIN',
            'confidence': 'LOW',
            'human_score': human_indicators,
            'algorithmic_score': algorithmic_indicators
        }

def generate_manipulation_report(analysis_results):
    """Generate comprehensive manipulation analysis report"""

    print(f"\n📊 MANIPULATION ANALYSIS REPORT")
    print("=" * 60)

    human_games = 0
    algorithmic_games = 0
    mixed_games = 0

    for game, results in analysis_results.items():
        verdict = results['verdict']

        print(f"\n🎲 {game} ANALYSIS:")
        print(f"   Type: {verdict['type']}")
        print(f"   Confidence: {verdict['confidence']}")
        print(f"   Human indicators: {verdict['human_score']}")
        print(f"   Algorithmic indicators: {verdict['algorithmic_score']}")

        # Count verdicts
        if verdict['type'] == 'HUMAN_MANIPULATION':
            human_games += 1
        elif verdict['type'] == 'ALGORITHMIC_GENERATION':
            algorithmic_games += 1
        else:
            mixed_games += 1

        # Show key indicators
        print(f"   Key findings:")

        if results['human_bias']['total_bias_score'] > 1:
            print(f"     🚨 Human bias detected (score: {results['human_bias']['total_bias_score']:.2f})")

        if results['timing_patterns']['human_schedule_correlation'] > 0.5:
            print(f"     🚨 Human schedule correlation: {results['timing_patterns']['human_schedule_correlation']:.2f}")

        if results['psychological_preferences']['psychological_score'] > 2:
            print(f"     🚨 Psychological patterns: {results['psychological_preferences']['psychological_score']}")

        if results['consistency']['algorithmic_signature']:
            print(f"     🤖 Algorithmic signature detected")

        if results['update_mechanism']['mechanism_type'] != 'UNKNOWN':
            print(f"     🔧 Update mechanism: {results['update_mechanism']['mechanism_type']}")

    # Overall conclusion
    print(f"\n🎯 OVERALL CONCLUSION:")
    print(f"   Human manipulation: {human_games}/4 games")
    print(f"   Algorithmic generation: {algorithmic_games}/4 games")
    print(f"   Mixed/Uncertain: {mixed_games}/4 games")

    if human_games >= 3:
        overall_verdict = "PRIMARILY_HUMAN_MANIPULATION"
        print(f"   🚨 VERDICT: Numbers are primarily set by humans")
        print(f"   📊 Implication: Patterns may be intentional or psychological")
        print(f"   ⚠️ Risk: Humans can change patterns unpredictably")
    elif algorithmic_games >= 3:
        overall_verdict = "PRIMARILY_ALGORITHMIC"
        print(f"   🤖 VERDICT: Numbers are primarily generated algorithmically")
        print(f"   📊 Implication: Patterns are due to algorithm weaknesses")
        print(f"   ✅ Advantage: Patterns should be more stable and predictable")
    else:
        overall_verdict = "MIXED_SYSTEM"
        print(f"   🔄 VERDICT: Mixed system or uncertain")
        print(f"   📊 Implication: Some games may be human, others algorithmic")
        print(f"   ⚠️ Risk: Inconsistent predictability across games")

    # Impact on prediction strategy
    print(f"\n💡 IMPACT ON PREDICTION STRATEGY:")

    if overall_verdict == "PRIMARILY_HUMAN_MANIPULATION":
        print(f"   🎯 Focus on psychological patterns and biases")
        print(f"   📈 Human patterns may be more exploitable short-term")
        print(f"   ⚠️ Higher risk of sudden pattern changes")
        print(f"   🔄 Require more frequent strategy adjustments")
    elif overall_verdict == "PRIMARILY_ALGORITHMIC":
        print(f"   🎯 Focus on mathematical and statistical patterns")
        print(f"   📈 Algorithmic patterns should be more stable")
        print(f"   ✅ Lower risk of sudden changes")
        print(f"   🔧 Can use more sophisticated mathematical models")
    else:
        print(f"   🎯 Use game-specific strategies")
        print(f"   📈 Treat each game differently")
        print(f"   ⚠️ Monitor each game independently")
        print(f"   🔄 Flexible approach required")

    # Save results
    save_manipulation_analysis(analysis_results, overall_verdict)

def save_manipulation_analysis(analysis_results, overall_verdict):
    """Save manipulation analysis results"""

    report = {
        'timestamp': datetime.now().isoformat(),
        'analysis_type': 'Human vs Algorithmic Manipulation Analysis',
        'overall_verdict': overall_verdict,
        'game_analysis': analysis_results,
        'methodology': [
            'Human bias pattern detection',
            'Timing pattern analysis',
            'Psychological preference testing',
            'Consistency pattern analysis',
            'External event response testing',
            'Update mechanism analysis'
        ]
    }

    os.makedirs('data', exist_ok=True)
    report_file = f'data/manipulation_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)

    print(f"\n💾 Manipulation analysis saved to: {report_file}")

if __name__ == "__main__":
    analyze_human_vs_algorithmic()