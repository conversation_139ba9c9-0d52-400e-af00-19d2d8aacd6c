{"timestamp": "2025-06-11T20:27:01.282707", "analysis_type": "deep_data_source_analysis", "analysis": {"site": "https://satta-king-fast.com", "data_update_mechanism": {"auto_refresh": false, "ajax_polling": false, "websockets": false, "server_push": false, "manual_update": true, "evidence": ["No automatic update mechanism found"]}, "javascript_analysis": {"external_scripts": ["https://www.googletagmanager.com/gtag/js?id=G-NB97W3B634", "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2790372650374424", "https://www.gstatic.com/firebasejs/6.5.0/firebase-app.js", "https://www.gstatic.com/firebasejs/6.5.0/firebase-messaging.js", "https://cdn.jsdelivr.net/npm/sweetalert2@8", "https://cdn.jsdelivr.net/npm/js-cookie@rc/dist/js.cookie.min.js", "/./onesignal-us-api.js?v3.4.8"], "inline_scripts_count": 1, "api_calls": [], "data_manipulation": [], "suspicious_patterns": []}, "form_analysis": {"total_forms": 1, "hidden_forms": [], "admin_forms": [], "data_forms": []}, "network_requests": {"external_domains": ["satta-king-fast.com", "www.gstatic.com", "pagead2.googlesyndication.com", "cdn.jsdelivr.net", "fonts.googleapis.com", "www.googletagmanager.com"], "api_like_urls": ["https://fonts.googleapis.com/css?family=Hind:400,600&display=swap"], "cdn_resources": ["https://www.gstatic.com/firebasejs/6.5.0/firebase-app.js", "https://www.gstatic.com/firebasejs/6.5.0/firebase-messaging.js", "https://cdn.jsdelivr.net/npm/sweetalert2@8", "https://cdn.jsdelivr.net/npm/js-cookie@rc/dist/js.cookie.min.js"], "tracking_scripts": ["https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2790372650374424"]}, "data_patterns": {"data_tables": 2, "result_patterns": [{"game": "GALI", "results": ["01"]}], "number_patterns": ["37", "92", "85", "13", "61", "09", "20", "80", "18", "74", "33", "68", "48", "36", "22", "63", "32", "75", "81", "11"], "timestamp_patterns": ["08:15 PM", "08:15 PM", "08:00 PM", "08:00 PM", "07:55 PM"]}, "server_analysis": {"server": "cloudflare", "cache_control": "no-cache, no-store, must-revalidate, no-transform", "last_modified": "Wed, 11 Jun 2025 14:56:37 GMT", "content_type": "text/html; charset=utf-8", "response_time": 0.276933, "cdn_detected": true, "dynamic_content": true}, "realtime_updates": {"tested": true, "updates_detected": true, "update_frequency": "unknown", "test_results": ["Content changed in 30 seconds"]}, "admin_interfaces": {"admin_paths_found": [], "login_forms": [], "protected_areas": []}, "database_analysis": {"sql_errors": [{"param": "'", "pattern": "table", "status": 200}, {"param": "\"", "pattern": "table", "status": 200}, {"param": "1=1", "pattern": "table", "status": 200}, {"param": "OR 1=1", "pattern": "table", "status": 200}, {"param": "UNION SELECT", "pattern": "table", "status": 200}], "database_hints": [], "connection_tests": []}, "conclusions": {"data_source_type": "database_driven", "update_mechanism": "unknown", "manipulation_risk": "unknown", "evidence": ["Dynamic content suggests database backend"], "recommendations": ["Monitor network traffic during result updates", "Investigate server-side technologies used", "Check for database connection strings in source code", "Test API endpoints for data access patterns"]}}}