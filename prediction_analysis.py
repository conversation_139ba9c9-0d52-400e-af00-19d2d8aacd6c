"""
Comprehensive analysis of ML vs Statistical predictions with accuracy comparison
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import config

def analyze_prediction_methods():
    """Compare different prediction methods and their accuracy"""
    
    print("🔍 PREDICTION METHODS ANALYSIS & COMPARISON")
    print("=" * 70)
    
    # Load ML backtest results
    ml_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('ml_backtest_report_')]
    if ml_files:
        latest_ml_file = max(ml_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
        with open(os.path.join(config.DATA_DIR, latest_ml_file), 'r') as f:
            ml_results = json.load(f)
    else:
        ml_results = None
    
    # Load statistical predictions
    stat_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('predictions_')]
    if stat_files:
        latest_stat_file = max(stat_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
        with open(os.path.join(config.DATA_DIR, latest_stat_file), 'r') as f:
            stat_results = json.load(f)
    else:
        stat_results = None
    
    print("\n📊 CURRENT PREDICTIONS COMPARISON")
    print("=" * 50)
    
    comparison = {}
    
    for game in config.GAMES:
        game_name = config.GAME_NAMES.get(game, game)
        comparison[game] = {'game_name': game_name}
        
        # ML Predictions
        if ml_results and game in ml_results.get('predictions', {}):
            ml_pred = ml_results['predictions'][game]
            comparison[game]['ml_prediction'] = ml_pred['prediction']
            comparison[game]['ml_accuracy'] = ml_pred['accuracy']
            comparison[game]['ml_model'] = ml_pred['model']
        
        # Statistical Predictions
        if stat_results and game in stat_results.get('predictions', {}):
            stat_pred = stat_results['predictions'][game]
            comparison[game]['stat_prediction'] = stat_pred['prediction']
            comparison[game]['stat_confidence'] = stat_pred['confidence'] * 100
        
        # Display comparison
        print(f"\n🎯 {game_name}:")
        if 'ml_prediction' in comparison[game]:
            print(f"   ML Model: {comparison[game]['ml_prediction']:02d} "
                  f"(Accuracy: {comparison[game]['ml_accuracy']:.1f}%, "
                  f"Model: {comparison[game]['ml_model']})")
        
        if 'stat_prediction' in comparison[game]:
            print(f"   Statistical: {comparison[game]['stat_prediction']:02d} "
                  f"(Confidence: {comparison[game]['stat_confidence']:.1f}%)")
        
        # Agreement analysis
        if 'ml_prediction' in comparison[game] and 'stat_prediction' in comparison[game]:
            diff = abs(comparison[game]['ml_prediction'] - comparison[game]['stat_prediction'])
            agreement = "High" if diff <= 5 else "Medium" if diff <= 15 else "Low"
            print(f"   Agreement: {agreement} (Difference: {diff})")
    
    return comparison, ml_results, stat_results

def analyze_ml_performance():
    """Detailed analysis of ML model performance"""
    
    print("\n\n🤖 ML MODEL PERFORMANCE ANALYSIS")
    print("=" * 50)
    
    # Load latest ML results
    ml_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('ml_backtest_report_')]
    if not ml_files:
        print("❌ No ML backtest results found")
        return
    
    latest_ml_file = max(ml_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
    with open(os.path.join(config.DATA_DIR, latest_ml_file), 'r') as f:
        ml_results = json.load(f)
    
    print(f"📈 ACCURACY METRICS BY GAME")
    print("-" * 40)
    
    for game, report in ml_results.get('backtest_reports', {}).items():
        game_name = config.GAME_NAMES.get(game, game)
        metrics = report['metrics']
        
        print(f"\n{game_name} ({report['best_model']}):")
        print(f"  MAE (Mean Absolute Error): {metrics['mae']:.2f}")
        print(f"  RMSE (Root Mean Square Error): {metrics['rmse']:.2f}")
        print(f"  Accuracy within ±5: {metrics['accuracy_5']:.1f}%")
        print(f"  Accuracy within ±10: {metrics['accuracy_10']:.1f}%")
        print(f"  Accuracy within ±15: {metrics['accuracy_15']:.1f}%")
        print(f"  Directional Accuracy: {metrics['directional_accuracy']:.1f}%")
        
        # Performance rating
        acc_10 = metrics['accuracy_10']
        if acc_10 >= 60:
            rating = "🟢 Excellent"
        elif acc_10 >= 45:
            rating = "🟡 Good"
        elif acc_10 >= 30:
            rating = "🟠 Fair"
        else:
            rating = "🔴 Poor"
        
        print(f"  Overall Rating: {rating}")
    
    # Model comparison
    print(f"\n📊 MODEL TYPE PERFORMANCE")
    print("-" * 30)
    
    model_performance = {}
    for game, report in ml_results.get('backtest_reports', {}).items():
        model_type = report['best_model']
        if model_type not in model_performance:
            model_performance[model_type] = []
        model_performance[model_type].append(report['metrics']['accuracy_10'])
    
    for model, accuracies in model_performance.items():
        avg_accuracy = np.mean(accuracies)
        print(f"  {model.title()}: {avg_accuracy:.1f}% (used in {len(accuracies)} games)")

def analyze_prediction_patterns():
    """Analyze patterns in predictions and actual results"""
    
    print("\n\n🔍 PREDICTION PATTERN ANALYSIS")
    print("=" * 50)
    
    # Load ML results
    ml_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('ml_backtest_report_')]
    if not ml_files:
        return
    
    latest_ml_file = max(ml_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
    with open(os.path.join(config.DATA_DIR, latest_ml_file), 'r') as f:
        ml_results = json.load(f)
    
    for game, report in ml_results.get('backtest_reports', {}).items():
        game_name = config.GAME_NAMES.get(game, game)
        predictions_data = report['predictions_vs_actual']
        
        if not predictions_data:
            continue
        
        print(f"\n📈 {game_name} Pattern Analysis:")
        
        # Extract data
        actual_values = [p['actual'] for p in predictions_data]
        predicted_values = [p['predicted'] for p in predictions_data]
        errors = [p['error'] for p in predictions_data]
        
        # Statistics
        print(f"  Actual Range: {min(actual_values)} - {max(actual_values)}")
        print(f"  Predicted Range: {min(predicted_values)} - {max(predicted_values)}")
        print(f"  Average Error: {np.mean(errors):.2f}")
        print(f"  Error Std Dev: {np.std(errors):.2f}")
        
        # Bias analysis
        avg_error = np.mean(errors)
        if abs(avg_error) < 2:
            bias = "Unbiased"
        elif avg_error > 2:
            bias = "Under-predicting"
        else:
            bias = "Over-predicting"
        print(f"  Prediction Bias: {bias}")
        
        # Accuracy by range
        low_range = [i for i, v in enumerate(actual_values) if v <= 33]
        mid_range = [i for i, v in enumerate(actual_values) if 34 <= v <= 66]
        high_range = [i for i, v in enumerate(actual_values) if v >= 67]
        
        for range_name, indices in [("Low (0-33)", low_range), ("Mid (34-66)", mid_range), ("High (67-99)", high_range)]:
            if indices:
                range_errors = [abs(errors[i]) for i in indices]
                range_accuracy = np.mean([e <= 10 for e in range_errors]) * 100
                print(f"  {range_name} Accuracy: {range_accuracy:.1f}%")

def generate_recommendations():
    """Generate recommendations for improving predictions"""
    
    print("\n\n💡 RECOMMENDATIONS FOR IMPROVEMENT")
    print("=" * 50)
    
    recommendations = [
        "🔄 Data Collection:",
        "  • Scrape more historical data (3+ years)",
        "  • Include external factors (holidays, events)",
        "  • Add cross-game correlation analysis",
        "",
        "🤖 Model Enhancement:",
        "  • Implement LSTM neural networks for sequence learning",
        "  • Use ensemble methods combining multiple models",
        "  • Add feature selection and engineering",
        "  • Implement hyperparameter tuning",
        "",
        "📊 Validation Improvements:",
        "  • Use walk-forward validation",
        "  • Implement cross-validation for time series",
        "  • Add confidence intervals to predictions",
        "  • Monitor model drift over time",
        "",
        "🎯 Prediction Strategy:",
        "  • Focus on directional accuracy (up/down trends)",
        "  • Provide prediction ranges instead of point estimates",
        "  • Combine multiple prediction horizons",
        "  • Use probabilistic predictions",
        "",
        "⚠️ Risk Management:",
        "  • Always include confidence scores",
        "  • Implement prediction uncertainty quantification",
        "  • Regular model retraining schedule",
        "  • Monitor prediction performance continuously"
    ]
    
    for rec in recommendations:
        print(rec)

def create_final_predictions():
    """Create final ensemble predictions combining ML and statistical methods"""
    
    print("\n\n🎯 FINAL ENSEMBLE PREDICTIONS")
    print("=" * 50)
    
    # Load both prediction types
    ml_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('ml_backtest_report_')]
    stat_files = [f for f in os.listdir(config.DATA_DIR) if f.startswith('predictions_')]
    
    if not ml_files or not stat_files:
        print("❌ Missing prediction files")
        return
    
    latest_ml_file = max(ml_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
    latest_stat_file = max(stat_files, key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
    
    with open(os.path.join(config.DATA_DIR, latest_ml_file), 'r') as f:
        ml_results = json.load(f)
    
    with open(os.path.join(config.DATA_DIR, latest_stat_file), 'r') as f:
        stat_results = json.load(f)
    
    final_predictions = {}
    
    print("🔮 ENSEMBLE PREDICTIONS (ML + Statistical):")
    print("-" * 45)
    
    for game in config.GAMES:
        game_name = config.GAME_NAMES.get(game, game)
        
        ml_pred = None
        stat_pred = None
        
        if game in ml_results.get('predictions', {}):
            ml_pred = ml_results['predictions'][game]['prediction']
            ml_accuracy = ml_results['predictions'][game]['accuracy']
        
        if game in stat_results.get('predictions', {}):
            stat_pred = stat_results['predictions'][game]['prediction']
            stat_confidence = stat_results['predictions'][game]['confidence'] * 100
        
        if ml_pred is not None and stat_pred is not None:
            # Weighted ensemble based on accuracy/confidence
            ml_weight = ml_accuracy / 100
            stat_weight = stat_confidence / 100
            
            total_weight = ml_weight + stat_weight
            if total_weight > 0:
                ensemble_pred = (ml_pred * ml_weight + stat_pred * stat_weight) / total_weight
                ensemble_pred = int(round(np.clip(ensemble_pred, 0, 99)))
                
                final_predictions[game] = {
                    'game_name': game_name,
                    'ml_prediction': ml_pred,
                    'stat_prediction': stat_pred,
                    'ensemble_prediction': ensemble_pred,
                    'ml_weight': ml_weight,
                    'stat_weight': stat_weight,
                    'confidence': (ml_accuracy + stat_confidence) / 2
                }
                
                print(f"\n{game_name}:")
                print(f"  ML: {ml_pred:02d} (Weight: {ml_weight:.2f})")
                print(f"  Statistical: {stat_pred:02d} (Weight: {stat_weight:.2f})")
                print(f"  🎯 ENSEMBLE: {ensemble_pred:02d} (Confidence: {final_predictions[game]['confidence']:.1f}%)")
        
        elif ml_pred is not None:
            final_predictions[game] = {
                'game_name': game_name,
                'ensemble_prediction': ml_pred,
                'confidence': ml_accuracy,
                'method': 'ML only'
            }
            print(f"\n{game_name}: {ml_pred:02d} (ML only, {ml_accuracy:.1f}%)")
        
        elif stat_pred is not None:
            final_predictions[game] = {
                'game_name': game_name,
                'ensemble_prediction': stat_pred,
                'confidence': stat_confidence,
                'method': 'Statistical only'
            }
            print(f"\n{game_name}: {stat_pred:02d} (Statistical only, {stat_confidence:.1f}%)")
    
    # Save final predictions
    final_report = {
        'timestamp': datetime.now().isoformat(),
        'method': 'Ensemble (ML + Statistical)',
        'predictions': final_predictions,
        'summary': {
            'total_games': len(final_predictions),
            'avg_confidence': np.mean([p['confidence'] for p in final_predictions.values()]),
            'methodology': 'Weighted ensemble based on individual method accuracy'
        }
    }
    
    report_file = os.path.join(config.DATA_DIR, f'final_ensemble_predictions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
    with open(report_file, 'w') as f:
        json.dump(final_report, f, indent=2)
    
    print(f"\n💾 Final predictions saved to: {report_file}")
    
    return final_predictions

def main():
    """Main analysis function"""
    
    print("🎯 COMPREHENSIVE PREDICTION ANALYSIS")
    print("=" * 70)
    print("Analyzing ML vs Statistical prediction methods...")
    
    # Run all analyses
    comparison, ml_results, stat_results = analyze_prediction_methods()
    
    if ml_results:
        analyze_ml_performance()
        analyze_prediction_patterns()
    
    generate_recommendations()
    
    # Create final ensemble predictions
    final_preds = create_final_predictions()
    
    print("\n" + "=" * 70)
    print("📋 ANALYSIS COMPLETE")
    print("=" * 70)
    print("✅ Compared ML vs Statistical methods")
    print("✅ Analyzed model performance and accuracy")
    print("✅ Generated improvement recommendations")
    print("✅ Created ensemble predictions")
    print("\n🎯 Use ensemble predictions for best accuracy!")

if __name__ == "__main__":
    main()
