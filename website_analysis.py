"""
Focused analysis of Satta King website structure and data sources
"""

import requests
import time
import json
import re
import os
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime

def analyze_website_structure():
    """Analyze the website structure and data sources"""
    
    print("🕵️ SATTA KING WEBSITE REVERSE ENGINEERING")
    print("=" * 60)
    
    base_url = "https://satta-king-fast.com"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    findings = {}
    
    # 1. Analyze main page
    print("\n🔍 ANALYZING MAIN PAGE")
    print("-" * 30)
    
    try:
        response = session.get(base_url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract all scripts
        scripts = []
        for script in soup.find_all('script'):
            if script.string:
                scripts.append(script.string)
            elif script.get('src'):
                scripts.append(f"External: {script.get('src')}")
        
        print(f"✅ Found {len(scripts)} scripts")
        
        # Look for data generation patterns
        all_script_content = ' '.join([s for s in scripts if not s.startswith('External:')])
        
        # Search for suspicious patterns
        suspicious_patterns = {
            'random_generation': re.findall(r'Math\.random|random\(|rand\(', all_script_content, re.IGNORECASE),
            'number_manipulation': re.findall(r'result|number|generate|satta', all_script_content, re.IGNORECASE),
            'admin_functions': re.findall(r'admin|secret|key|backdoor', all_script_content, re.IGNORECASE),
            'api_calls': re.findall(r'fetch|ajax|XMLHttpRequest', all_script_content, re.IGNORECASE)
        }
        
        findings['main_page'] = {
            'scripts_count': len(scripts),
            'suspicious_patterns': suspicious_patterns,
            'has_random_generation': len(suspicious_patterns['random_generation']) > 0,
            'has_admin_functions': len(suspicious_patterns['admin_functions']) > 0
        }
        
        for pattern_type, matches in suspicious_patterns.items():
            if matches:
                print(f"   🚨 {pattern_type}: {len(matches)} matches")
            else:
                print(f"   ✅ {pattern_type}: No matches")
        
    except Exception as e:
        print(f"❌ Error analyzing main page: {str(e)}")
    
    # 2. Analyze chart page
    print("\n🔍 ANALYZING CHART PAGE")
    print("-" * 30)
    
    try:
        chart_url = f"{base_url}/chart.php"
        response = session.get(chart_url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for data tables
        tables = soup.find_all('table')
        print(f"✅ Found {len(tables)} data tables")
        
        # Analyze data structure
        if tables:
            first_table = tables[0]
            rows = first_table.find_all('tr')
            print(f"   Table has {len(rows)} rows")
            
            # Extract sample data
            sample_data = []
            for row in rows[:10]:  # First 10 rows
                cells = row.find_all(['td', 'th'])
                if cells:
                    row_data = [cell.get_text().strip() for cell in cells]
                    sample_data.append(row_data)
            
            findings['chart_page'] = {
                'tables_count': len(tables),
                'sample_data': sample_data,
                'data_structure': 'tabular'
            }
            
            print(f"   Sample data structure: {sample_data[0] if sample_data else 'No data'}")
        
    except Exception as e:
        print(f"❌ Error analyzing chart page: {str(e)}")
    
    # 3. Test for hidden endpoints
    print("\n🔍 TESTING FOR HIDDEN ENDPOINTS")
    print("-" * 30)
    
    test_endpoints = [
        '/admin.php',
        '/api.php',
        '/generate.php',
        '/random.php',
        '/update.php',
        '/result.php',
        '/data.php',
        '/admin/',
        '/api/',
        '/backend/'
    ]
    
    found_endpoints = []
    
    for endpoint in test_endpoints:
        try:
            url = urljoin(base_url, endpoint)
            response = session.head(url, timeout=5)
            
            if response.status_code in [200, 301, 302, 403]:
                found_endpoints.append({
                    'url': url,
                    'status': response.status_code
                })
                print(f"   ✅ {endpoint}: Status {response.status_code}")
            
            time.sleep(0.5)
            
        except:
            continue
    
    findings['endpoints'] = found_endpoints
    
    if not found_endpoints:
        print("   ✅ No suspicious endpoints found")
    
    # 4. Analyze data patterns from our scraped data
    print("\n🔍 ANALYZING SCRAPED DATA PATTERNS")
    print("-" * 30)
    
    try:
        import pandas as pd
        import numpy as np
        
        data_file = 'data/raw/extensive_historical_data.csv'
        if os.path.exists(data_file):
            df = pd.read_csv(data_file)
            
            # Analyze each game for randomness
            games = ['DSWR', 'FRBD', 'GZBD', 'GALI']
            randomness_analysis = {}
            
            for game in games:
                if game in df.columns:
                    game_data = pd.to_numeric(df[game], errors='coerce').dropna()
                    
                    if len(game_data) > 100:
                        analysis = analyze_randomness(game_data, game)
                        randomness_analysis[game] = analysis
                        
                        print(f"   {game}:")
                        print(f"     Samples: {len(game_data)}")
                        print(f"     Randomness score: {analysis['randomness_score']:.1f}/100")
                        print(f"     Verdict: {analysis['verdict']}")
            
            findings['randomness_analysis'] = randomness_analysis
        else:
            print("   ❌ No scraped data found for analysis")
    
    except ImportError:
        print("   ❌ Pandas not available for data analysis")
    except Exception as e:
        print(f"   ❌ Error in data analysis: {str(e)}")
    
    # 5. Generate final assessment
    print("\n📊 FINAL ASSESSMENT")
    print("-" * 30)
    
    assessment = generate_assessment(findings)
    
    print(f"Data Source: {assessment['data_source']}")
    print(f"Number Generation: {assessment['number_generation']}")
    print(f"Manipulation Risk: {assessment['manipulation_risk']}")
    print(f"Randomness Level: {assessment['randomness_level']}")
    print(f"Overall Verdict: {assessment['verdict']}")
    
    # Save detailed report
    report = {
        'timestamp': datetime.now().isoformat(),
        'website': base_url,
        'findings': findings,
        'assessment': assessment
    }
    
    os.makedirs('data', exist_ok=True)
    report_file = f'data/website_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed report saved to: {report_file}")
    
    return assessment

def analyze_randomness(data, game_name):
    """Analyze if the data appears to be truly random"""
    import numpy as np
    
    analysis = {
        'game': game_name,
        'sample_size': len(data),
        'randomness_score': 0,
        'tests': {},
        'verdict': 'UNKNOWN'
    }
    
    # Test 1: Distribution uniformity
    expected_freq = len(data) / 100
    observed_freq = np.bincount(data.astype(int), minlength=100)
    
    # Calculate chi-square-like statistic
    chi_square = np.sum((observed_freq - expected_freq) ** 2 / expected_freq)
    uniform_score = max(0, 100 - (chi_square / 10))  # Normalize
    
    analysis['tests']['uniformity'] = {
        'score': uniform_score,
        'chi_square': chi_square
    }
    
    # Test 2: Autocorrelation
    if len(data) > 1:
        autocorr = np.corrcoef(data[:-1], data[1:])[0, 1]
        autocorr_score = max(0, 100 - abs(autocorr) * 1000)  # Penalize correlation
    else:
        autocorr_score = 50
    
    analysis['tests']['autocorrelation'] = {
        'score': autocorr_score,
        'correlation': autocorr if len(data) > 1 else 0
    }
    
    # Test 3: Consecutive differences
    if len(data) > 1:
        diffs = np.diff(data)
        diff_std = np.std(diffs)
        # Random data should have high variance in differences
        diff_score = min(100, diff_std * 2)
    else:
        diff_score = 50
    
    analysis['tests']['differences'] = {
        'score': diff_score,
        'std_dev': diff_std if len(data) > 1 else 0
    }
    
    # Test 4: Repeated patterns
    pattern_score = 100
    if len(data) > 10:
        # Look for repeated 3-number sequences
        sequences = []
        for i in range(len(data) - 2):
            seq = tuple(data.iloc[i:i+3] if hasattr(data, 'iloc') else data[i:i+3])
            sequences.append(seq)
        
        seq_counts = {}
        for seq in sequences:
            seq_counts[seq] = seq_counts.get(seq, 0) + 1
        
        max_repeats = max(seq_counts.values()) if seq_counts else 1
        if max_repeats > 3:
            pattern_score -= (max_repeats - 3) * 20
    
    analysis['tests']['patterns'] = {
        'score': max(0, pattern_score),
        'max_repeats': max_repeats if len(data) > 10 else 0
    }
    
    # Calculate overall randomness score
    scores = [test['score'] for test in analysis['tests'].values()]
    analysis['randomness_score'] = np.mean(scores)
    
    # Determine verdict
    if analysis['randomness_score'] >= 80:
        analysis['verdict'] = 'HIGHLY_RANDOM'
    elif analysis['randomness_score'] >= 60:
        analysis['verdict'] = 'MOSTLY_RANDOM'
    elif analysis['randomness_score'] >= 40:
        analysis['verdict'] = 'SOMEWHAT_RANDOM'
    else:
        analysis['verdict'] = 'NOT_RANDOM'
    
    return analysis

def generate_assessment(findings):
    """Generate overall assessment based on findings"""
    
    assessment = {
        'data_source': 'UNKNOWN',
        'number_generation': 'UNKNOWN',
        'manipulation_risk': 'UNKNOWN',
        'randomness_level': 'UNKNOWN',
        'verdict': 'UNKNOWN'
    }
    
    # Analyze main page findings
    main_page = findings.get('main_page', {})
    
    if main_page.get('has_random_generation'):
        assessment['number_generation'] = 'CLIENT_SIDE_RANDOM'
        assessment['manipulation_risk'] = 'HIGH'
    elif main_page.get('has_admin_functions'):
        assessment['number_generation'] = 'ADMIN_CONTROLLED'
        assessment['manipulation_risk'] = 'VERY_HIGH'
    else:
        assessment['number_generation'] = 'EXTERNAL_SOURCE'
        assessment['manipulation_risk'] = 'MEDIUM'
    
    # Analyze endpoints
    endpoints = findings.get('endpoints', [])
    admin_endpoints = [ep for ep in endpoints if 'admin' in ep['url'].lower()]
    
    if admin_endpoints:
        assessment['manipulation_risk'] = 'VERY_HIGH'
    
    # Analyze randomness
    randomness_data = findings.get('randomness_analysis', {})
    
    if randomness_data:
        avg_randomness = np.mean([data['randomness_score'] for data in randomness_data.values()])
        
        if avg_randomness >= 80:
            assessment['randomness_level'] = 'HIGH'
        elif avg_randomness >= 60:
            assessment['randomness_level'] = 'MEDIUM'
        else:
            assessment['randomness_level'] = 'LOW'
    
    # Data source assessment
    chart_page = findings.get('chart_page', {})
    if chart_page.get('data_structure') == 'tabular':
        assessment['data_source'] = 'DATABASE_DRIVEN'
    
    # Overall verdict
    risk_factors = 0
    
    if assessment['manipulation_risk'] in ['HIGH', 'VERY_HIGH']:
        risk_factors += 2
    if assessment['randomness_level'] == 'LOW':
        risk_factors += 2
    if admin_endpoints:
        risk_factors += 1
    
    if risk_factors >= 4:
        assessment['verdict'] = 'HIGHLY_SUSPICIOUS'
    elif risk_factors >= 2:
        assessment['verdict'] = 'SUSPICIOUS'
    else:
        assessment['verdict'] = 'APPEARS_LEGITIMATE'
    
    return assessment

def analyze_multiple_sites_api():
    """Analyze multiple satta sites to detect shared APIs or data sources"""

    print("🌐 MULTI-SITE API ANALYSIS")
    print("=" * 60)

    # List of popular satta sites to analyze
    sites = [
        "https://satta-king-fast.com",
        "https://sattaking.com",
        "https://sattakinglive.com",
        "https://sattaresult.net",
        "https://sattamatka.com",
        "https://sattaking143.com"
    ]

    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    results = {}

    for site in sites:
        print(f"\n🔍 Analyzing: {site}")
        print("-" * 40)

        try:
            # Get main page
            response = session.get(site, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')

            site_analysis = {
                'status': response.status_code,
                'accessible': True,
                'results_data': {},
                'api_endpoints': [],
                'shared_patterns': [],
                'network_requests': []
            }

            # Extract current results
            results_data = extract_current_results(soup, site)
            site_analysis['results_data'] = results_data

            # Look for API endpoints in scripts
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # Look for API URLs
                    api_patterns = re.findall(r'https?://[^\s"\']+(?:api|data|result|update)[^\s"\']*', script.string, re.IGNORECASE)
                    site_analysis['api_endpoints'].extend(api_patterns)

            # Check for common external domains
            external_links = soup.find_all('a', href=True)
            external_scripts = soup.find_all('script', src=True)

            for link in external_links + external_scripts:
                url = link.get('href') or link.get('src')
                if url and ('api' in url.lower() or 'data' in url.lower()):
                    site_analysis['network_requests'].append(url)

            results[site] = site_analysis
            print(f"   ✅ Status: {response.status_code}")
            print(f"   📊 Results found: {len(results_data)}")
            print(f"   🔗 API endpoints: {len(site_analysis['api_endpoints'])}")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results[site] = {
                'status': 'error',
                'accessible': False,
                'error': str(e)
            }

    # Compare results across sites
    print("\n🔍 CROSS-SITE COMPARISON")
    print("-" * 40)

    comparison = compare_site_results(results)

    # Look for shared infrastructure
    print("\n🔍 SHARED INFRASTRUCTURE ANALYSIS")
    print("-" * 40)

    infrastructure = analyze_shared_infrastructure(results)

    # Generate API analysis report
    api_report = {
        'timestamp': datetime.now().isoformat(),
        'sites_analyzed': len(sites),
        'accessible_sites': len([r for r in results.values() if r.get('accessible', False)]),
        'site_results': results,
        'comparison': comparison,
        'infrastructure': infrastructure,
        'conclusions': generate_api_conclusions(results, comparison, infrastructure)
    }

    # Save report
    os.makedirs('data', exist_ok=True)
    report_file = f'data/multi_site_api_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

    with open(report_file, 'w') as f:
        json.dump(api_report, f, indent=2, default=str)

    print(f"\n💾 API analysis report saved to: {report_file}")

    return api_report

def extract_current_results(soup, site_url):
    """Extract current results from a satta site"""
    results = {}

    try:
        # Look for result tables
        tables = soup.find_all('table')

        for table in tables:
            rows = table.find_all('tr')

            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    # Try to identify game name and result
                    game_text = cells[0].get_text().strip()

                    # Look for common game names
                    game_names = ['DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD', 'GALI']

                    for game in game_names:
                        if game.lower() in game_text.lower():
                            # Extract numeric result
                            for cell in cells[1:]:
                                cell_text = cell.get_text().strip()
                                if cell_text.isdigit() and len(cell_text) <= 2:
                                    results[game] = cell_text
                                    break

        # Also look for results in divs or spans
        result_elements = soup.find_all(['div', 'span'], class_=re.compile(r'result|number', re.I))

        for element in result_elements:
            text = element.get_text().strip()
            # Look for patterns like "GALI: 45" or "DSWR 23"
            matches = re.findall(r'(DSWR|DESAWAR|FRBD|FARIDABAD|GZBD|GHAZIABAD|GALI)[:\s]*(\d{1,2})', text, re.IGNORECASE)

            for game, result in matches:
                results[game.upper()] = result

    except Exception as e:
        print(f"   ⚠️ Error extracting results: {str(e)}")

    return results

def compare_site_results(site_results):
    """Compare results across different sites to detect shared data"""

    comparison = {
        'identical_results': {},
        'similar_patterns': [],
        'data_freshness': {},
        'shared_games': set()
    }

    accessible_sites = {site: data for site, data in site_results.items()
                       if data.get('accessible', False)}

    if len(accessible_sites) < 2:
        return comparison

    # Extract all games mentioned across sites
    all_games = set()
    for site_data in accessible_sites.values():
        all_games.update(site_data.get('results_data', {}).keys())

    comparison['shared_games'] = all_games

    # Check for identical results
    for game in all_games:
        game_results = {}

        for site, data in accessible_sites.items():
            result = data.get('results_data', {}).get(game)
            if result:
                game_results[site] = result

        if len(game_results) > 1:
            # Check if all results are identical
            unique_results = set(game_results.values())

            if len(unique_results) == 1:
                comparison['identical_results'][game] = {
                    'result': list(unique_results)[0],
                    'sites': list(game_results.keys()),
                    'count': len(game_results)
                }

    return comparison

def analyze_shared_infrastructure(site_results):
    """Analyze if sites share infrastructure, APIs, or hosting"""

    infrastructure = {
        'shared_apis': [],
        'common_domains': [],
        'similar_endpoints': [],
        'hosting_analysis': {}
    }

    # Collect all API endpoints
    all_apis = []
    api_by_site = {}

    for site, data in site_results.items():
        if data.get('accessible', False):
            apis = data.get('api_endpoints', [])
            all_apis.extend(apis)
            api_by_site[site] = apis

    # Find shared APIs
    from collections import Counter
    api_counter = Counter(all_apis)

    shared_apis = {api: count for api, count in api_counter.items() if count > 1}
    infrastructure['shared_apis'] = shared_apis

    # Extract domains from APIs
    domains = []
    for api in all_apis:
        try:
            from urllib.parse import urlparse
            domain = urlparse(api).netloc
            if domain:
                domains.append(domain)
        except:
            continue

    domain_counter = Counter(domains)
    common_domains = {domain: count for domain, count in domain_counter.items() if count > 1}
    infrastructure['common_domains'] = common_domains

    return infrastructure

def generate_api_conclusions(site_results, comparison, infrastructure):
    """Generate conclusions about API usage and data sharing"""

    conclusions = {
        'data_sharing_detected': False,
        'shared_api_likely': False,
        'evidence_strength': 'WEAK',
        'findings': [],
        'recommendations': []
    }

    # Check for identical results
    identical_count = len(comparison.get('identical_results', {}))

    if identical_count > 0:
        conclusions['data_sharing_detected'] = True
        conclusions['findings'].append(f"Found {identical_count} games with identical results across multiple sites")

        if identical_count >= 3:
            conclusions['evidence_strength'] = 'STRONG'
        elif identical_count >= 2:
            conclusions['evidence_strength'] = 'MODERATE'

    # Check for shared APIs
    shared_apis = infrastructure.get('shared_apis', {})

    if shared_apis:
        conclusions['shared_api_likely'] = True
        conclusions['findings'].append(f"Found {len(shared_apis)} shared API endpoints")

        if conclusions['evidence_strength'] == 'WEAK':
            conclusions['evidence_strength'] = 'MODERATE'

    # Check for common domains
    common_domains = infrastructure.get('common_domains', {})

    if common_domains:
        conclusions['findings'].append(f"Found {len(common_domains)} common domains used across sites")

    # Generate recommendations
    if conclusions['data_sharing_detected']:
        conclusions['recommendations'].append("Multiple sites showing identical results suggests centralized data source")
        conclusions['recommendations'].append("Investigate network traffic to identify actual API endpoints")
        conclusions['recommendations'].append("Monitor result timing across sites to confirm synchronization")

    if conclusions['shared_api_likely']:
        conclusions['recommendations'].append("Reverse engineer API calls to understand data flow")
        conclusions['recommendations'].append("Check if API requires authentication or is publicly accessible")

    return conclusions

if __name__ == "__main__":
    try:
        import numpy as np
    except ImportError:
        print("Installing numpy...")
        os.system("pip install numpy")
        import numpy as np

    print("Choose analysis type:")
    print("1. Single site structure analysis")
    print("2. Multi-site API analysis")
    print("3. Both analyses")

    choice = input("Enter choice (1-3): ").strip()

    if choice == "1":
        analyze_website_structure()
    elif choice == "2":
        analyze_multiple_sites_api()
    elif choice == "3":
        analyze_website_structure()
        print("\n" + "="*80 + "\n")
        analyze_multiple_sites_api()
    else:
        print("Invalid choice. Running both analyses...")
        analyze_website_structure()
        print("\n" + "="*80 + "\n")
        analyze_multiple_sites_api()
