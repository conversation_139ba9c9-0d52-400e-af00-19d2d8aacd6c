{"timestamp": "2025-06-11T18:48:44.966204", "website": "https://satta-king-fast.com", "analysis_type": "Deep Source Code Analysis", "analysis_results": {"source_analysis": {"inline_scripts_count": 1, "external_scripts_count": 7, "external_scripts": ["https://www.googletagmanager.com/gtag/js?id=G-NB97W3B634", "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2790372650374424", "https://www.gstatic.com/firebasejs/6.5.0/firebase-app.js", "https://www.gstatic.com/firebasejs/6.5.0/firebase-messaging.js", "https://cdn.jsdelivr.net/npm/sweetalert2@8", "https://cdn.jsdelivr.net/npm/js-cookie@rc/dist/js.cookie.min.js", "/./onesignal-us-api.js?v3.4.8"], "patterns_found": {"random_generation": {"regex": ["Math\\.random\\(\\)", "Math\\.floor\\(Math\\.random\\(\\)", "random\\s*\\(", "rand\\s*\\(", "generateRandom"], "found": false, "count": 0, "examples": []}, "number_manipulation": {"regex": ["result\\s*=\\s*\\d+", "number\\s*=\\s*\\d+", "satta.*=.*\\d+", "fix.*number", "set.*result"], "found": false, "count": 0, "examples": []}, "admin_functions": {"regex": ["admin", "secret", "backdoor", "override", "cheat", "manipulate"], "found": false, "count": 0, "examples": []}, "api_endpoints": {"regex": ["fetch\\s*\\(\\s*[\\'\"]([^\\'\"]+)[\\'\"]", "ajax.*url.*[\\'\"]([^\\'\"]+)[\\'\"]", "XMLHttpRequest.*open.*[\\'\"]([^\\'\"]+)[\\'\"]", "\\.get\\s*\\(\\s*[\\'\"]([^\\'\"]+)[\\'\"]", "\\.post\\s*\\(\\s*[\\'\"]([^\\'\"]+)[\\'\"]"], "found": false, "count": 0, "examples": []}, "data_sources": {"regex": ["var\\s+\\w+\\s*=\\s*\\[.*\\d+.*\\]", "const\\s+\\w+\\s*=\\s*\\[.*\\d+.*\\]", "data\\s*=\\s*\\{.*\\}", "results\\s*=\\s*\\[.*\\]"], "found": false, "count": 0, "examples": []}}, "total_js_lines": 6}, "chart_analysis": {"data_tables_count": 2, "js_functions_count": 1, "embedded_data_found": false, "data_generation_method": "SERVER_SIDE", "hardcoded_data": false, "dynamic_loading": false}, "infrastructure": {"server_info": "cloudflare", "cdn_detected": false, "security_score": 0, "hosting_provider": "UNKNOWN", "ssl_info": {}, "response_headers": {"Date": "Wed, 11 Jun 2025 13:19:07 GMT", "Content-Type": "text/html; charset=utf-8", "Connection": "keep-alive", "Server": "cloudflare", "Vary": "Accept-Encoding", "Content-Encoding": "gzip", "Cache-Control": "no-cache, no-store, must-revalidate, no-transform", "Pragma": "no-cache", "Expires": "0", "Age": "182", "Cf-Cache-Status": "HIT", "Last-Modified": "Wed, 11 Jun 2025 13:16:04 GMT", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=G92%2BknWsF9MFgOKJtfreaSzlBPZ%2FgIBEy9gngurE1Hmd%2B3N7vXYmwqhA9VTnuf2SUiDeQ2IDCYRSvWuqG7FBSaJLJmOwDx%2BJrHmr8ro6N0vLTnNPPrrcp31fqK2xJCg%3D\"}]}", "CF-RAY": "94e165f8789991b1-SIN", "alt-svc": "h3=\":443\"; ma=86400"}}, "data_flow": {"primary_source": "STATIC_FILES", "update_mechanism": "STATIC_OR_CACHED", "real_time": false, "data_freshness": "UNKNOWN", "caching_detected": false}}, "findings": {"generation_method": "SERVER_SIDE", "data_source": "STATIC_FILES", "manipulation_risk": "HIGH", "randomness_assessment": "DATA_APPEARS_RANDOM_BUT_SOURCE_UNCLEAR", "verdict": "HIGHLY_SUSPICIOUS - LIKELY MANIPULATED", "confidence_level": "HIGH"}, "methodology": "Reverse engineering, source code analysis, network inspection"}