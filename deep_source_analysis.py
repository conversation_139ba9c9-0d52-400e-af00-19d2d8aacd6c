"""
Deep source code analysis of Satta King website to understand number generation
"""

import requests
import re
import json
import os
from bs4 import BeautifulSoup
from datetime import datetime
import base64

def deep_source_analysis():
    """Perform deep analysis of website source code and infrastructure"""
    
    print("🔬 DEEP SOURCE CODE ANALYSIS - SATTA KING")
    print("=" * 60)
    
    base_url = "https://satta-king-fast.com"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    analysis_results = {}
    
    # 1. Analyze HTML source structure
    print("\n🔍 ANALYZING HTML SOURCE STRUCTURE")
    print("-" * 40)
    
    try:
        response = session.get(base_url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract all inline JavaScript
        inline_scripts = []
        for script in soup.find_all('script'):
            if script.string and script.string.strip():
                inline_scripts.append(script.string.strip())
        
        # Extract all external script sources
        external_scripts = []
        for script in soup.find_all('script', src=True):
            external_scripts.append(script['src'])
        
        print(f"✅ Found {len(inline_scripts)} inline scripts")
        print(f"✅ Found {len(external_scripts)} external scripts")
        
        # Analyze inline scripts for data generation
        all_inline_code = '\n'.join(inline_scripts)
        
        # Look for specific patterns
        patterns_found = analyze_javascript_patterns(all_inline_code)
        
        analysis_results['source_analysis'] = {
            'inline_scripts_count': len(inline_scripts),
            'external_scripts_count': len(external_scripts),
            'external_scripts': external_scripts,
            'patterns_found': patterns_found,
            'total_js_lines': len(all_inline_code.split('\n'))
        }
        
        # Display findings
        for pattern_type, details in patterns_found.items():
            if details['found']:
                print(f"   🚨 {pattern_type}: {details['count']} instances")
                if details['examples']:
                    print(f"      Examples: {details['examples'][:2]}")
            else:
                print(f"   ✅ {pattern_type}: Not found")
        
    except Exception as e:
        print(f"❌ Error in source analysis: {str(e)}")
    
    # 2. Analyze chart.php specifically
    print("\n🔍 ANALYZING CHART.PHP SOURCE")
    print("-" * 40)
    
    try:
        chart_response = session.get(f"{base_url}/chart.php")
        chart_soup = BeautifulSoup(chart_response.content, 'html.parser')
        
        # Extract data generation logic
        chart_analysis = analyze_chart_page_source(chart_soup, chart_response.text)
        analysis_results['chart_analysis'] = chart_analysis
        
        print(f"✅ Chart page analysis complete")
        print(f"   Data tables: {chart_analysis['data_tables_count']}")
        print(f"   JavaScript functions: {chart_analysis['js_functions_count']}")
        print(f"   Embedded data: {chart_analysis['embedded_data_found']}")
        
    except Exception as e:
        print(f"❌ Error analyzing chart.php: {str(e)}")
    
    # 3. Network infrastructure analysis
    print("\n🔍 ANALYZING NETWORK INFRASTRUCTURE")
    print("-" * 40)
    
    try:
        infrastructure_analysis = analyze_infrastructure(base_url, session)
        analysis_results['infrastructure'] = infrastructure_analysis
        
        print(f"✅ Infrastructure analysis complete")
        print(f"   Server technology: {infrastructure_analysis['server_info']}")
        print(f"   CDN usage: {infrastructure_analysis['cdn_detected']}")
        print(f"   Security headers: {infrastructure_analysis['security_score']}/100")
        
    except Exception as e:
        print(f"❌ Error in infrastructure analysis: {str(e)}")
    
    # 4. Data flow analysis
    print("\n🔍 ANALYZING DATA FLOW")
    print("-" * 40)
    
    try:
        data_flow = analyze_data_flow(base_url, session)
        analysis_results['data_flow'] = data_flow
        
        print(f"✅ Data flow analysis complete")
        print(f"   Data source: {data_flow['primary_source']}")
        print(f"   Update mechanism: {data_flow['update_mechanism']}")
        print(f"   Real-time updates: {data_flow['real_time']}")
        
    except Exception as e:
        print(f"❌ Error in data flow analysis: {str(e)}")
    
    # 5. Generate comprehensive findings
    print("\n📊 COMPREHENSIVE FINDINGS")
    print("-" * 40)
    
    findings = generate_comprehensive_findings(analysis_results)
    
    print(f"🎯 NUMBER GENERATION METHOD: {findings['generation_method']}")
    print(f"🎯 DATA SOURCE: {findings['data_source']}")
    print(f"🎯 MANIPULATION POSSIBILITY: {findings['manipulation_risk']}")
    print(f"🎯 RANDOMNESS ASSESSMENT: {findings['randomness_assessment']}")
    print(f"🎯 OVERALL VERDICT: {findings['verdict']}")
    
    # Save detailed report
    final_report = {
        'timestamp': datetime.now().isoformat(),
        'website': base_url,
        'analysis_type': 'Deep Source Code Analysis',
        'analysis_results': analysis_results,
        'findings': findings,
        'methodology': 'Reverse engineering, source code analysis, network inspection'
    }
    
    os.makedirs('data', exist_ok=True)
    report_file = f'data/deep_source_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(report_file, 'w') as f:
        json.dump(final_report, f, indent=2, default=str)
    
    print(f"\n💾 Comprehensive report saved to: {report_file}")
    
    return findings

def analyze_javascript_patterns(js_code):
    """Analyze JavaScript code for specific patterns"""
    
    patterns = {
        'random_generation': {
            'regex': [
                r'Math\.random\(\)',
                r'Math\.floor\(Math\.random\(\)',
                r'random\s*\(',
                r'rand\s*\(',
                r'generateRandom'
            ],
            'found': False,
            'count': 0,
            'examples': []
        },
        'number_manipulation': {
            'regex': [
                r'result\s*=\s*\d+',
                r'number\s*=\s*\d+',
                r'satta.*=.*\d+',
                r'fix.*number',
                r'set.*result'
            ],
            'found': False,
            'count': 0,
            'examples': []
        },
        'admin_functions': {
            'regex': [
                r'admin',
                r'secret',
                r'backdoor',
                r'override',
                r'cheat',
                r'manipulate'
            ],
            'found': False,
            'count': 0,
            'examples': []
        },
        'api_endpoints': {
            'regex': [
                r'fetch\s*\(\s*[\'"]([^\'"]+)[\'"]',
                r'ajax.*url.*[\'"]([^\'"]+)[\'"]',
                r'XMLHttpRequest.*open.*[\'"]([^\'"]+)[\'"]',
                r'\.get\s*\(\s*[\'"]([^\'"]+)[\'"]',
                r'\.post\s*\(\s*[\'"]([^\'"]+)[\'"]'
            ],
            'found': False,
            'count': 0,
            'examples': []
        },
        'data_sources': {
            'regex': [
                r'var\s+\w+\s*=\s*\[.*\d+.*\]',
                r'const\s+\w+\s*=\s*\[.*\d+.*\]',
                r'data\s*=\s*\{.*\}',
                r'results\s*=\s*\[.*\]'
            ],
            'found': False,
            'count': 0,
            'examples': []
        }
    }
    
    for pattern_name, pattern_info in patterns.items():
        all_matches = []
        
        for regex in pattern_info['regex']:
            matches = re.findall(regex, js_code, re.IGNORECASE | re.MULTILINE)
            if matches:
                all_matches.extend(matches)
        
        if all_matches:
            pattern_info['found'] = True
            pattern_info['count'] = len(all_matches)
            pattern_info['examples'] = all_matches[:5]  # First 5 examples
    
    return patterns

def analyze_chart_page_source(soup, raw_html):
    """Analyze the chart page source code specifically"""
    
    analysis = {
        'data_tables_count': 0,
        'js_functions_count': 0,
        'embedded_data_found': False,
        'data_generation_method': 'UNKNOWN',
        'hardcoded_data': False,
        'dynamic_loading': False
    }
    
    # Count data tables
    tables = soup.find_all('table')
    analysis['data_tables_count'] = len(tables)
    
    # Extract JavaScript functions
    js_functions = re.findall(r'function\s+(\w+)\s*\(', raw_html, re.IGNORECASE)
    analysis['js_functions_count'] = len(js_functions)
    
    # Look for embedded data
    json_data_pattern = r'var\s+\w+\s*=\s*(\{.*?\}|\[.*?\]);'
    json_matches = re.findall(json_data_pattern, raw_html, re.DOTALL)
    
    if json_matches:
        analysis['embedded_data_found'] = True
        
        # Try to parse the JSON to see if it contains numbers
        for match in json_matches:
            try:
                data = json.loads(match)
                if isinstance(data, (list, dict)):
                    # Check if it contains number-like data
                    str_data = str(data)
                    if re.search(r'\b\d{1,2}\b', str_data):
                        analysis['hardcoded_data'] = True
                        break
            except:
                continue
    
    # Look for AJAX/dynamic loading
    ajax_patterns = [
        r'fetch\s*\(',
        r'XMLHttpRequest',
        r'\.ajax\s*\(',
        r'\.get\s*\(',
        r'\.post\s*\('
    ]
    
    for pattern in ajax_patterns:
        if re.search(pattern, raw_html, re.IGNORECASE):
            analysis['dynamic_loading'] = True
            break
    
    # Determine data generation method
    if analysis['hardcoded_data']:
        analysis['data_generation_method'] = 'HARDCODED'
    elif analysis['dynamic_loading']:
        analysis['data_generation_method'] = 'DYNAMIC_AJAX'
    elif analysis['data_tables_count'] > 0:
        analysis['data_generation_method'] = 'SERVER_SIDE'
    
    return analysis

def analyze_infrastructure(base_url, session):
    """Analyze the website's infrastructure"""
    
    infrastructure = {
        'server_info': 'UNKNOWN',
        'cdn_detected': False,
        'security_score': 0,
        'hosting_provider': 'UNKNOWN',
        'ssl_info': {},
        'response_headers': {}
    }
    
    try:
        response = session.head(base_url)
        headers = dict(response.headers)
        infrastructure['response_headers'] = headers
        
        # Detect server technology
        server_header = headers.get('Server', '').lower()
        if 'apache' in server_header:
            infrastructure['server_info'] = 'Apache'
        elif 'nginx' in server_header:
            infrastructure['server_info'] = 'Nginx'
        elif 'iis' in server_header:
            infrastructure['server_info'] = 'IIS'
        else:
            infrastructure['server_info'] = server_header or 'Hidden'
        
        # Check for CDN
        cdn_headers = ['cf-ray', 'x-cache', 'x-served-by', 'x-amz-cf-id']
        for header in cdn_headers:
            if header in headers:
                infrastructure['cdn_detected'] = True
                break
        
        # Security score
        security_headers = [
            'strict-transport-security',
            'x-frame-options',
            'x-content-type-options',
            'x-xss-protection',
            'content-security-policy'
        ]
        
        security_score = 0
        for header in security_headers:
            if header in headers:
                security_score += 20
        
        infrastructure['security_score'] = security_score
        
    except Exception as e:
        print(f"   ⚠️ Infrastructure analysis error: {str(e)}")
    
    return infrastructure

def analyze_data_flow(base_url, session):
    """Analyze how data flows through the website"""
    
    data_flow = {
        'primary_source': 'UNKNOWN',
        'update_mechanism': 'UNKNOWN',
        'real_time': False,
        'data_freshness': 'UNKNOWN',
        'caching_detected': False
    }
    
    try:
        # Test multiple requests to see if data changes
        responses = []
        for i in range(3):
            response = session.get(f"{base_url}/chart.php")
            responses.append({
                'timestamp': datetime.now(),
                'content_hash': hash(response.content),
                'headers': dict(response.headers)
            })
            
            if i < 2:  # Don't sleep after last request
                import time
                time.sleep(2)
        
        # Check if content changes
        content_hashes = [r['content_hash'] for r in responses]
        if len(set(content_hashes)) > 1:
            data_flow['real_time'] = True
            data_flow['update_mechanism'] = 'REAL_TIME'
        else:
            data_flow['update_mechanism'] = 'STATIC_OR_CACHED'
        
        # Check caching headers
        cache_headers = ['cache-control', 'expires', 'etag', 'last-modified']
        first_response_headers = responses[0]['headers']
        
        for header in cache_headers:
            if header in first_response_headers:
                data_flow['caching_detected'] = True
                break
        
        # Determine primary source
        if data_flow['real_time']:
            data_flow['primary_source'] = 'LIVE_DATABASE'
        elif data_flow['caching_detected']:
            data_flow['primary_source'] = 'CACHED_DATABASE'
        else:
            data_flow['primary_source'] = 'STATIC_FILES'
        
    except Exception as e:
        print(f"   ⚠️ Data flow analysis error: {str(e)}")
    
    return data_flow

def generate_comprehensive_findings(analysis_results):
    """Generate comprehensive findings from all analyses"""
    
    findings = {
        'generation_method': 'UNKNOWN',
        'data_source': 'UNKNOWN',
        'manipulation_risk': 'UNKNOWN',
        'randomness_assessment': 'UNKNOWN',
        'verdict': 'UNKNOWN',
        'confidence_level': 'LOW'
    }
    
    # Analyze source code findings
    source_analysis = analysis_results.get('source_analysis', {})
    patterns = source_analysis.get('patterns_found', {})
    
    # Check for random generation in client-side code
    if patterns.get('random_generation', {}).get('found'):
        findings['generation_method'] = 'CLIENT_SIDE_RANDOM'
        findings['manipulation_risk'] = 'HIGH'
    elif patterns.get('admin_functions', {}).get('found'):
        findings['generation_method'] = 'ADMIN_CONTROLLED'
        findings['manipulation_risk'] = 'VERY_HIGH'
    else:
        findings['generation_method'] = 'SERVER_SIDE'
        findings['manipulation_risk'] = 'LOW_TO_MEDIUM'
    
    # Analyze chart page
    chart_analysis = analysis_results.get('chart_analysis', {})
    
    if chart_analysis.get('hardcoded_data'):
        findings['data_source'] = 'HARDCODED_VALUES'
        findings['manipulation_risk'] = 'VERY_HIGH'
    elif chart_analysis.get('dynamic_loading'):
        findings['data_source'] = 'DYNAMIC_API'
        findings['manipulation_risk'] = 'MEDIUM'
    else:
        findings['data_source'] = 'DATABASE_DRIVEN'
        findings['manipulation_risk'] = 'LOW'
    
    # Analyze data flow
    data_flow = analysis_results.get('data_flow', {})
    
    if data_flow.get('real_time'):
        findings['data_source'] = 'LIVE_SYSTEM'
    elif data_flow.get('primary_source') == 'STATIC_FILES':
        findings['data_source'] = 'STATIC_FILES'
        findings['manipulation_risk'] = 'HIGH'
    
    # Overall assessment
    risk_factors = 0
    
    if findings['manipulation_risk'] in ['HIGH', 'VERY_HIGH']:
        risk_factors += 2
    if findings['data_source'] in ['HARDCODED_VALUES', 'STATIC_FILES']:
        risk_factors += 2
    if patterns.get('admin_functions', {}).get('found'):
        risk_factors += 1
    
    # Determine verdict
    if risk_factors >= 4:
        findings['verdict'] = 'HIGHLY_SUSPICIOUS - LIKELY MANIPULATED'
        findings['confidence_level'] = 'HIGH'
    elif risk_factors >= 2:
        findings['verdict'] = 'SUSPICIOUS - POSSIBLE MANIPULATION'
        findings['confidence_level'] = 'MEDIUM'
    else:
        findings['verdict'] = 'APPEARS_LEGITIMATE - NO_OBVIOUS_MANIPULATION'
        findings['confidence_level'] = 'MEDIUM'
    
    # Randomness assessment based on previous analysis
    findings['randomness_assessment'] = 'DATA_APPEARS_RANDOM_BUT_SOURCE_UNCLEAR'
    
    return findings

if __name__ == "__main__":
    deep_source_analysis()
