"""
Streamlit web interface for Satta King prediction bot
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os

from predictor import SattaPredictor
from utils import calculate_statistics, find_patterns
import config

# Page configuration
st.set_page_config(
    page_title="Satta King Prediction Bot",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    font-size: 3rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.prediction-card {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
.confidence-high { color: #28a745; }
.confidence-medium { color: #ffc107; }
.confidence-low { color: #dc3545; }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load and cache data"""
    try:
        predictor = SattaPredictor()
        predictor.load_and_prepare_data()
        return predictor.data, predictor
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        return None, None

def main():
    st.markdown('<h1 class="main-header">🎯 Satta King Prediction Bot</h1>', unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox("Choose a page", [
        "🏠 Home",
        "🔮 Predictions",
        "📊 Analysis",
        "📈 Trends",
        "⚙️ Settings"
    ])
    
    # Load data
    data, predictor = load_data()
    
    if data is None:
        st.error("Failed to load data. Please check if data has been scraped and processed.")
        return
    
    if page == "🏠 Home":
        show_home_page(data)
    elif page == "🔮 Predictions":
        show_predictions_page(predictor)
    elif page == "📊 Analysis":
        show_analysis_page(data)
    elif page == "📈 Trends":
        show_trends_page(data)
    elif page == "⚙️ Settings":
        show_settings_page()

def show_home_page(data):
    st.header("Welcome to Satta King Prediction Bot")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Records", len(data))
    
    with col2:
        if 'date' in data.columns:
            date_range = (data['date'].max() - data['date'].min()).days
            st.metric("Days of Data", date_range)
    
    with col3:
        available_games = [game for game in config.GAMES if game in data.columns]
        st.metric("Available Games", len(available_games))
    
    st.subheader("Recent Data Overview")
    
    # Show recent data for each game
    recent_data = data.tail(10)
    if 'date' in recent_data.columns:
        display_cols = ['date'] + [game for game in config.GAMES if game in recent_data.columns]
        st.dataframe(recent_data[display_cols])
    
    st.subheader("Quick Stats")
    
    for game in config.GAMES:
        if game in data.columns:
            game_name = config.GAME_NAMES.get(game, game)
            recent_values = data[game].dropna().tail(30)
            
            if len(recent_values) > 0:
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.write(f"**{game_name}**")
                with col2:
                    st.write(f"Last: {int(recent_values.iloc[-1])}")
                with col3:
                    st.write(f"Avg: {recent_values.mean():.1f}")
                with col4:
                    st.write(f"Range: {int(recent_values.min())}-{int(recent_values.max())}")

def show_predictions_page(predictor):
    st.header("🔮 Number Predictions")
    
    col1, col2 = st.columns([2, 1])
    
    with col2:
        st.subheader("Settings")
        
        selected_games = st.multiselect(
            "Select Games",
            options=config.GAMES,
            default=config.GAMES,
            format_func=lambda x: config.GAME_NAMES.get(x, x)
        )
        
        method = st.selectbox(
            "Prediction Method",
            options=['ensemble', 'traditional', 'lstm'],
            index=0
        )
        
        if st.button("Generate Predictions", type="primary"):
            with st.spinner("Generating predictions..."):
                try:
                    predictions = predictor.predict_next_numbers(selected_games, method)
                    st.session_state.predictions = predictions
                except Exception as e:
                    st.error(f"Error generating predictions: {str(e)}")
    
    with col1:
        st.subheader("Predictions")
        
        if 'predictions' in st.session_state and st.session_state.predictions:
            for game, pred_info in st.session_state.predictions.items():
                game_name = config.GAME_NAMES.get(game, game)
                prediction = pred_info['prediction']
                confidence = pred_info['confidence']
                
                # Determine confidence color
                if confidence >= 0.7:
                    conf_class = "confidence-high"
                elif confidence >= 0.5:
                    conf_class = "confidence-medium"
                else:
                    conf_class = "confidence-low"
                
                st.markdown(f"""
                <div class="prediction-card">
                    <h3>{game_name}</h3>
                    <h2 style="color: #1f77b4;">{prediction:02d}</h2>
                    <p class="{conf_class}">Confidence: {confidence:.1%}</p>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("Click 'Generate Predictions' to see predictions for selected games.")

def show_analysis_page(data):
    st.header("📊 Data Analysis")
    
    game = st.selectbox(
        "Select Game for Analysis",
        options=config.GAMES,
        format_func=lambda x: config.GAME_NAMES.get(x, x)
    )
    
    days = st.slider("Analysis Period (days)", 7, 365, 30)
    
    if game in data.columns:
        # Statistics
        stats = calculate_statistics(data, game, days)
        
        if stats:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Basic Statistics")
                st.write(f"**Count:** {stats['count']}")
                st.write(f"**Mean:** {stats['mean']:.2f}")
                st.write(f"**Median:** {stats['median']:.2f}")
                st.write(f"**Std Dev:** {stats['std']:.2f}")
                st.write(f"**Range:** {stats['min']} - {stats['max']}")
            
            with col2:
                st.subheader("Distribution")
                st.write(f"**Even Numbers:** {stats['even_percentage']:.1f}%")
                st.write(f"**Avg Digit Sum:** {stats['avg_digit_sum']:.1f}")
                st.write(f"**Volatility:** {stats['volatility']:.2f}")
                st.write(f"**Skewness:** {stats['skewness']:.2f}")
            
            # Most frequent numbers
            st.subheader("Most Frequent Numbers")
            freq_data = pd.DataFrame(list(stats['most_frequent'].items()), 
                                   columns=['Number', 'Frequency'])
            st.bar_chart(freq_data.set_index('Number'))
            
            # Patterns
            patterns = find_patterns(data, game, pattern_length=3)
            if patterns:
                st.subheader("Common Patterns (3-number sequences)")
                pattern_df = pd.DataFrame(patterns[:10], columns=['Pattern', 'Frequency'])
                pattern_df['Pattern'] = pattern_df['Pattern'].apply(lambda x: ' → '.join(map(str, x)))
                st.dataframe(pattern_df)

def show_trends_page(data):
    st.header("📈 Trends and Visualizations")
    
    game = st.selectbox(
        "Select Game",
        options=config.GAMES,
        format_func=lambda x: config.GAME_NAMES.get(x, x),
        key="trends_game"
    )
    
    days = st.slider("Time Period (days)", 30, 365, 90, key="trends_days")
    
    if game in data.columns:
        recent_data = data[['date', game]].dropna().tail(days)
        
        if len(recent_data) > 0:
            # Line chart
            st.subheader(f"{config.GAME_NAMES.get(game, game)} - Time Series")
            fig = px.line(recent_data, x='date', y=game, 
                         title=f"{config.GAME_NAMES.get(game, game)} Over Time")
            fig.update_traces(line=dict(width=2))
            st.plotly_chart(fig, use_container_width=True)
            
            # Distribution histogram
            st.subheader("Number Distribution")
            fig = px.histogram(recent_data, x=game, nbins=20,
                             title=f"Distribution of {config.GAME_NAMES.get(game, game)}")
            st.plotly_chart(fig, use_container_width=True)
            
            # Box plot by day of week
            if len(recent_data) > 7:
                recent_data['day_of_week'] = recent_data['date'].dt.day_name()
                st.subheader("Distribution by Day of Week")
                fig = px.box(recent_data, x='day_of_week', y=game,
                           title=f"{config.GAME_NAMES.get(game, game)} by Day of Week")
                st.plotly_chart(fig, use_container_width=True)

def show_settings_page():
    st.header("⚙️ Settings")
    
    st.subheader("Data Management")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Scrape New Data"):
            with st.spinner("Scraping data..."):
                # This would trigger the scraping process
                st.info("Data scraping would be triggered here")
    
    with col2:
        if st.button("Retrain Models"):
            with st.spinner("Retraining models..."):
                # This would trigger model retraining
                st.info("Model retraining would be triggered here")
    
    st.subheader("Configuration")
    
    st.write("**Available Games:**")
    for game in config.GAMES:
        st.write(f"- {config.GAME_NAMES.get(game, game)} ({game})")
    
    st.write(f"**Data Directory:** {config.DATA_DIR}")
    st.write(f"**Models Directory:** {config.MODELS_DIR}")
    st.write(f"**Sequence Length:** {config.SEQUENCE_LENGTH}")

if __name__ == "__main__":
    main()
