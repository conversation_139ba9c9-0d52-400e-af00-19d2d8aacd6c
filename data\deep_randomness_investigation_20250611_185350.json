{"timestamp": "2025-06-11T18:53:50.951105", "investigation_type": "Deep Randomness Logic Investigation", "findings": {"hidden_patterns": {"DSWR": {"game": "DSWR", "cycles_detected": [], "math_sequences": [{"type": "arithmetic", "start_index": 180, "length": 3, "difference": "41", "sequence": [6, 47, 88]}, {"type": "arithmetic", "start_index": 206, "length": 3, "difference": "36", "sequence": [19, 55, 91]}, {"type": "arithmetic", "start_index": 1341, "length": 3, "difference": "-41", "sequence": [84, 43, 2]}, {"type": "arithmetic", "start_index": 1693, "length": 3, "difference": "-5", "sequence": [73, 68, 63]}, {"type": "arithmetic", "start_index": 1744, "length": 3, "difference": "-37", "sequence": [80, 43, 6]}, {"type": "arithmetic", "start_index": 1988, "length": 3, "difference": "-5", "sequence": [14, 9, 4]}, {"type": "geometric", "start_index": 195, "length": 3, "ratio": 2.5833333333333335, "sequence": [12, 31, 80]}, {"type": "geometric", "start_index": 1365, "length": 3, "ratio": 0.4189189189189189, "sequence": [74, 31, 13]}, {"type": "geometric", "start_index": 1693, "length": 3, "ratio": 0.9315068493150684, "sequence": [73, 68, 63]}, {"type": "geometric", "start_index": 1863, "length": 3, "ratio": 0.7906976744186046, "sequence": [86, 68, 54]}], "predictable_score": 66.66666666666666, "hidden_logic": "STRONG_PATTERNS_DETECTED", "potential_loopholes": [], "date_patterns": {"significant": true, "day_of_week_bias": {}, "month_bias": {"9": {"mean": 41.446666666666665, "deviation": -5.764544544544549}}, "seasonal_patterns": {}, "holiday_effects": {}}, "cross_correlations": {"strong_correlation": false, "correlations": {}, "lead_lag_relationships": {}, "sum_patterns": {}}, "modular_patterns": {"patterns_found": true, "modular_sequences": [], "remainder_patterns": {"13": {"chi2_stat": 31.496496496496498, "distribution": {"8": 183, "2": 177, "5": 175, "6": 172, "4": 158, "3": 156, "1": 156, "7": 153, "9": 145, "11": 145, "0": 134, "12": 128, "10": 116}, "bias_detected": true}}}, "number_theory": {"patterns_found": true, "fibonacci_bias": false, "prime_bias": false, "perfect_square_bias": false, "digit_sum_patterns": {"6": 164, "9": 213, "8": 160, "11": 162, "10": 186, "0": 7, "17": 31, "16": 46, "7": 169, "1": 36, "18": 26}}}, "FRBD": {"game": "FRBD", "cycles_detected": [], "math_sequences": [{"type": "arithmetic", "start_index": 107, "length": 3, "difference": 33.0, "sequence": [2.0, 35.0, 68.0]}, {"type": "arithmetic", "start_index": 173, "length": 3, "difference": 29.0, "sequence": [2.0, 31.0, 60.0]}, {"type": "arithmetic", "start_index": 625, "length": 3, "difference": -26.0, "sequence": [52.0, 26.0, 0.0]}, {"type": "arithmetic", "start_index": 689, "length": 3, "difference": -10.0, "sequence": [91.0, 81.0, 71.0]}, {"type": "arithmetic", "start_index": 915, "length": 3, "difference": -19.0, "sequence": [68.0, 49.0, 30.0]}, {"type": "arithmetic", "start_index": 917, "length": 3, "difference": 20.0, "sequence": [30.0, 50.0, 70.0]}, {"type": "arithmetic", "start_index": 1134, "length": 3, "difference": -9.0, "sequence": [80.0, 71.0, 62.0]}, {"type": "arithmetic", "start_index": 1209, "length": 3, "difference": 13.0, "sequence": [4.0, 17.0, 30.0]}, {"type": "arithmetic", "start_index": 1491, "length": 3, "difference": 20.0, "sequence": [50.0, 70.0, 90.0]}, {"type": "arithmetic", "start_index": 1781, "length": 3, "difference": 12.0, "sequence": [4.0, 16.0, 28.0]}], "predictable_score": 50.0, "hidden_logic": "WEAK_PATTERNS_DETECTED", "potential_loopholes": [], "date_patterns": {"significant": false, "day_of_week_bias": {}, "month_bias": {}, "seasonal_patterns": {}, "holiday_effects": {}}, "cross_correlations": {"strong_correlation": false, "correlations": {}, "lead_lag_relationships": {}, "sum_patterns": {}}, "modular_patterns": {"patterns_found": true, "modular_sequences": [], "remainder_patterns": {"2": {"chi2_stat": 7.089613034623218, "distribution": {"0.0": 1041, "1.0": 923}, "bias_detected": true}}}, "number_theory": {"patterns_found": true, "fibonacci_bias": false, "prime_bias": false, "perfect_square_bias": false, "digit_sum_patterns": {"8": 187, "10": 176, "17": 40, "16": 45, "9": 184, "7": 176, "1": 48, "18": 18, "0": 17}}}, "GZBD": {"game": "GZBD", "cycles_detected": [], "math_sequences": [{"type": "arithmetic", "start_index": 89, "length": 3, "difference": -6.0, "sequence": [68.0, 62.0, 56.0]}, {"type": "arithmetic", "start_index": 612, "length": 3, "difference": -2.0, "sequence": [80.0, 78.0, 76.0]}, {"type": "arithmetic", "start_index": 623, "length": 3, "difference": -24.0, "sequence": [57.0, 33.0, 9.0]}, {"type": "arithmetic", "start_index": 655, "length": 3, "difference": 13.0, "sequence": [44.0, 57.0, 70.0]}, {"type": "arithmetic", "start_index": 668, "length": 3, "difference": -8.0, "sequence": [22.0, 14.0, 6.0]}, {"type": "arithmetic", "start_index": 962, "length": 3, "difference": 33.0, "sequence": [0.0, 33.0, 66.0]}, {"type": "arithmetic", "start_index": 1204, "length": 3, "difference": -14.0, "sequence": [49.0, 35.0, 21.0]}, {"type": "arithmetic", "start_index": 1501, "length": 3, "difference": 15.0, "sequence": [17.0, 32.0, 47.0]}, {"type": "arithmetic", "start_index": 1560, "length": 3, "difference": 37.0, "sequence": [17.0, 54.0, 91.0]}, {"type": "arithmetic", "start_index": 1581, "length": 3, "difference": -38.0, "sequence": [84.0, 46.0, 8.0]}], "predictable_score": 50.0, "hidden_logic": "WEAK_PATTERNS_DETECTED", "potential_loopholes": [], "date_patterns": {"significant": false, "day_of_week_bias": {}, "month_bias": {}, "seasonal_patterns": {}, "holiday_effects": {}}, "cross_correlations": {"strong_correlation": false, "correlations": {}, "lead_lag_relationships": {}, "sum_patterns": {}}, "modular_patterns": {"patterns_found": true, "modular_sequences": [], "remainder_patterns": {"13": {"chi2_stat": 33.0158567774936, "distribution": {"3.0": 190, "4.0": 173, "1.0": 164, "7.0": 162, "6.0": 160, "2.0": 151, "5.0": 150, "0.0": 147, "11.0": 145, "8.0": 139, "9.0": 138, "10.0": 123, "12.0": 113}, "bias_detected": true}}}, "number_theory": {"patterns_found": true, "fibonacci_bias": false, "prime_bias": false, "perfect_square_bias": false, "digit_sum_patterns": {"9": 193, "0": 15, "8": 186, "17": 32, "18": 18, "10": 158, "7": 166, "16": 45, "1": 39}}}, "GALI": {"game": "GALI", "cycles_detected": [], "math_sequences": [{"type": "arithmetic", "start_index": 218, "length": 3, "difference": -8.0, "sequence": [82.0, 74.0, 66.0]}, {"type": "arithmetic", "start_index": 537, "length": 3, "difference": -25.0, "sequence": [52.0, 27.0, 2.0]}, {"type": "arithmetic", "start_index": 694, "length": 3, "difference": 20.0, "sequence": [34.0, 54.0, 74.0]}, {"type": "arithmetic", "start_index": 925, "length": 3, "difference": -7.0, "sequence": [79.0, 72.0, 65.0]}, {"type": "arithmetic", "start_index": 951, "length": 3, "difference": 7.0, "sequence": [59.0, 66.0, 73.0]}, {"type": "arithmetic", "start_index": 1018, "length": 3, "difference": 31.0, "sequence": [9.0, 40.0, 71.0]}, {"type": "arithmetic", "start_index": 1021, "length": 3, "difference": -13.0, "sequence": [71.0, 58.0, 45.0]}], "predictable_score": 50.0, "hidden_logic": "WEAK_PATTERNS_DETECTED", "potential_loopholes": [], "date_patterns": {"significant": false, "day_of_week_bias": {}, "month_bias": {}, "seasonal_patterns": {}, "holiday_effects": {}}, "cross_correlations": {"strong_correlation": false, "correlations": {}, "lead_lag_relationships": {}, "sum_patterns": {}}, "modular_patterns": {"patterns_found": true, "modular_sequences": [], "remainder_patterns": {"13": {"chi2_stat": 27.041071428571428, "distribution": {"0.0": 102, "3.0": 102, "6.0": 99, "1.0": 97, "2.0": 94, "4.0": 92, "5.0": 91, "7.0": 87, "12.0": 79, "8.0": 78, "10.0": 74, "11.0": 67, "9.0": 58}, "bias_detected": true}}}, "number_theory": {"patterns_found": true, "fibonacci_bias": false, "prime_bias": false, "perfect_square_bias": false, "digit_sum_patterns": {"8": 93, "17": 20, "9": 110, "10": 114, "1": 24, "18": 8, "0": 8}}}}, "time_patterns": {"update_frequency": "1 days 00:00:00", "update_timing": "UNKNOWN", "time_correlation": false}, "external_correlations": {"stock_market": false, "weather": false, "calendar_events": false, "other_lotteries": false}, "update_analysis": {"update_method": "STATIC_OR_CACHED", "update_source": "UNKNOWN", "predictable_timing": false, "manual_vs_automatic": "UNKNOWN"}, "prediction_loopholes": {"pattern_based": [], "timing_based": [], "correlation_based": [], "mathematical_based": [], "overall_exploitability": "NONE"}}, "final_assessment": {"randomness_logic": "ALGORITHMIC_WITH_PATTERNS", "legitimacy": "QUESTIONABLE", "prediction_potential": "HIGH", "exploitable_patterns": [], "confidence_level": "LOW", "recommendations": ["Further investigation warranted", "Monitor patterns over time", "Test predictions on small scale"]}}