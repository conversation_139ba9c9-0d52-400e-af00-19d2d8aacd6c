"""
Generate current predictions for Satta King games
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import os
from scraper import SattaKingScraper
import config

def get_latest_predictions():
    """Get the latest predictions based on current data"""
    print("🎯 SATTA KING PREDICTIONS")
    print("=" * 50)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Scrape the most recent data
    scraper = SattaKingScraper()
    
    print("📡 Fetching latest data...")
    recent_data = []
    
    # Get current month and previous month
    current_date = datetime.now()
    current_month = current_date.strftime('%B')
    current_year = current_date.year
    
    # Try current month first
    print(f"Scraping {current_month} {current_year}...")
    data = scraper.get_month_data(current_month, current_year)
    if data:
        recent_data.extend(data)
        print(f"✓ Found {len(data)} records for {current_month}")
    
    # Get previous month for more data
    if current_date.month == 1:
        prev_month = "December"
        prev_year = current_year - 1
    else:
        prev_months = ["January", "February", "March", "April", "May", "June",
                      "July", "August", "September", "October", "November", "December"]
        prev_month = prev_months[current_date.month - 2]
        prev_year = current_year
    
    print(f"Scraping {prev_month} {prev_year}...")
    data = scraper.get_month_data(prev_month, prev_year)
    if data:
        recent_data.extend(data)
        print(f"✓ Found {len(data)} records for {prev_month}")
    
    if not recent_data:
        print("❌ No recent data found. Using sample data for demonstration.")
        return generate_sample_predictions()
    
    # Convert to DataFrame and clean
    df = pd.DataFrame(recent_data)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    # Convert game columns to numeric
    for game in config.GAMES:
        if game in df.columns:
            df[game] = pd.to_numeric(df[game], errors='coerce')
    
    print(f"\n📊 Data Summary:")
    print(f"Total records: {len(df)}")
    print(f"Date range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}")
    
    # Generate predictions
    print(f"\n🔮 GENERATING PREDICTIONS...")
    print("-" * 40)
    
    predictions = {}
    
    for game in config.GAMES:
        if game in df.columns:
            game_data = df[game].dropna()
            
            if len(game_data) >= 5:
                game_name = config.GAME_NAMES.get(game, game)
                
                # Get recent values (last 15 for better analysis)
                recent_values = game_data.tail(15).values
                
                # Multiple prediction methods
                methods = {}
                
                # 1. Simple average
                methods['average'] = int(np.mean(recent_values))
                
                # 2. Weighted average (more weight to recent values)
                weights = np.linspace(0.5, 1.0, len(recent_values))
                methods['weighted_avg'] = int(np.average(recent_values, weights=weights))
                
                # 3. Median
                methods['median'] = int(np.median(recent_values))
                
                # 4. Mode (most frequent)
                from scipy import stats
                mode_result = stats.mode(recent_values, keepdims=True)
                methods['mode'] = int(mode_result.mode[0])
                
                # 5. Trend-based prediction
                if len(recent_values) >= 5:
                    # Linear trend
                    x = np.arange(len(recent_values))
                    slope, intercept = np.polyfit(x, recent_values, 1)
                    trend_pred = int(intercept + slope * len(recent_values))
                    methods['trend'] = max(0, min(99, trend_pred))
                else:
                    methods['trend'] = methods['average']
                
                # 6. Pattern-based (look for cycles)
                if len(recent_values) >= 10:
                    # Check for weekly patterns (every 7 days)
                    if len(recent_values) >= 14:
                        week_ago = recent_values[-7] if len(recent_values) >= 7 else recent_values[-1]
                        two_weeks_ago = recent_values[-14] if len(recent_values) >= 14 else recent_values[-1]
                        methods['weekly_pattern'] = int((week_ago + two_weeks_ago) / 2)
                    else:
                        methods['weekly_pattern'] = methods['average']
                else:
                    methods['weekly_pattern'] = methods['average']
                
                # 7. Range-based prediction (avoid recent numbers)
                recent_set = set(recent_values[-5:])  # Last 5 numbers
                all_numbers = set(range(100))
                available_numbers = all_numbers - recent_set
                if available_numbers:
                    # Pick a number close to the average but not recently used
                    avg = methods['average']
                    closest = min(available_numbers, key=lambda x: abs(x - avg))
                    methods['range_based'] = closest
                else:
                    methods['range_based'] = methods['average']
                
                # Ensemble prediction (weighted combination)
                ensemble_weights = {
                    'weighted_avg': 0.25,
                    'trend': 0.20,
                    'median': 0.15,
                    'mode': 0.15,
                    'weekly_pattern': 0.15,
                    'range_based': 0.10
                }
                
                ensemble_pred = sum(methods[method] * weight 
                                  for method, weight in ensemble_weights.items())
                ensemble_pred = int(round(ensemble_pred))
                ensemble_pred = max(0, min(99, ensemble_pred))
                
                # Calculate confidence based on consistency of methods
                method_values = list(methods.values())
                std_dev = np.std(method_values)
                confidence = max(0.5, min(0.95, 1.0 - (std_dev / 50)))  # Normalize std dev
                
                predictions[game] = {
                    'game_name': game_name,
                    'prediction': ensemble_pred,
                    'confidence': confidence,
                    'methods': methods,
                    'recent_values': recent_values.tolist(),
                    'last_value': int(recent_values[-1]),
                    'analysis': {
                        'trend': 'increasing' if methods['trend'] > methods['average'] else 'decreasing',
                        'volatility': 'high' if std_dev > 20 else 'medium' if std_dev > 10 else 'low',
                        'recent_range': f"{int(recent_values.min())}-{int(recent_values.max())}"
                    }
                }
                
                # Display prediction
                print(f"\n🎲 {game_name.upper()}:")
                print(f"   Prediction: {ensemble_pred:02d}")
                print(f"   Confidence: {confidence:.1%}")
                print(f"   Last Value: {int(recent_values[-1]):02d}")
                print(f"   Recent: {[int(x) for x in recent_values[-5:]]}")
                print(f"   Trend: {predictions[game]['analysis']['trend']}")
                print(f"   Methods: Avg={methods['average']}, Trend={methods['trend']}, Mode={methods['mode']}")
    
    # Save predictions
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        "timestamp": datetime.now().isoformat(),
        "predictions": predictions,
        "data_summary": {
            "total_records": len(df),
            "latest_date": df['date'].max().isoformat(),
            "games_analyzed": len(predictions)
        },
        "disclaimer": "These are statistical predictions for educational purposes only. Gambling involves risk."
    }
    
    # Save to file
    os.makedirs(config.DATA_DIR, exist_ok=True)
    report_file = os.path.join(config.DATA_DIR, f'predictions_{timestamp}.json')
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Report saved to: {report_file}")
    
    return predictions

def generate_sample_predictions():
    """Generate sample predictions when no data is available"""
    print("📊 Generating sample predictions...")
    
    # Sample recent data for demonstration
    sample_data = {
        'DSWR': [24, 59, 96, 32, 79, 48, 19, 93, 9, 26, 45, 67, 12, 88, 34],
        'FRBD': [83, 15, 27, 2, 28, 1, 71, 94, 3, 79, 56, 23, 89, 41, 65],
        'GZBD': [7, 60, 17, 4, 9, 84, 74, 43, 53, 18, 92, 36, 77, 14, 58],
        'GALI': [74, 92, 6, 48, 50, 30, 3, 7, 19, 85, 42, 68, 25, 91, 37]
    }
    
    predictions = {}
    
    for game, values in sample_data.items():
        game_name = config.GAME_NAMES.get(game, game)
        
        # Apply same prediction logic
        avg_pred = int(np.mean(values))
        trend = np.mean(np.diff(values[-5:]))
        trend_pred = int(values[-1] + trend)
        trend_pred = max(0, min(99, trend_pred))
        
        ensemble_pred = int((avg_pred + trend_pred + int(np.median(values))) / 3)
        ensemble_pred = max(0, min(99, ensemble_pred))
        
        predictions[game] = {
            'game_name': game_name,
            'prediction': ensemble_pred,
            'confidence': np.random.uniform(0.65, 0.85),
            'recent_values': values,
            'last_value': values[-1]
        }
        
        print(f"\n🎲 {game_name.upper()}:")
        print(f"   Prediction: {ensemble_pred:02d}")
        print(f"   Confidence: {predictions[game]['confidence']:.1%}")
        print(f"   Last Value: {values[-1]:02d}")
        print(f"   Recent: {values[-5:]}")
    
    return predictions

def main():
    """Main prediction function"""
    print("🚀 SATTA KING PREDICTION BOT")
    print("=" * 60)
    print("Generating predictions based on latest data...")
    print()
    
    try:
        predictions = get_latest_predictions()
        
        if predictions:
            print("\n" + "=" * 50)
            print("📋 PREDICTION SUMMARY")
            print("=" * 50)
            
            for game, pred_info in predictions.items():
                confidence_emoji = "🟢" if pred_info['confidence'] > 0.7 else "🟡" if pred_info['confidence'] > 0.6 else "🔴"
                print(f"{confidence_emoji} {pred_info['game_name']}: {pred_info['prediction']:02d} ({pred_info['confidence']:.1%})")
            
            print("\n⚠️  IMPORTANT DISCLAIMER:")
            print("These predictions are for educational purposes only.")
            print("Gambling involves risk. Please gamble responsibly.")
            print("Check local laws regarding gambling in your area.")
            
        else:
            print("❌ Could not generate predictions")
            
    except Exception as e:
        print(f"❌ Error generating predictions: {str(e)}")
        print("Falling back to sample predictions...")
        generate_sample_predictions()

if __name__ == "__main__":
    main()
