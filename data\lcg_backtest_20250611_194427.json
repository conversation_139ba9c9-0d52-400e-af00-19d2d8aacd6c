{"timestamp": "2025-06-11T19:44:27.872215", "test_type": "LCG Strategy Backtest", "test_period": "20 predictions per game", "summary": {"avg_exact": 1.25, "avg_within_5": 10.0, "avg_within_10": 20.0, "verdict": "❌ INEFFECTIVE", "recommendation": "DO NOT USE - No edge detected"}, "detailed_results": {"DSWR": {"total_predictions": 20, "exact_matches": 0, "within_5_matches": 1, "within_10_matches": 3, "exact_accuracy": 0.0, "within_5_accuracy": 5.0, "within_10_accuracy": 15.0, "average_error": 36.3, "performance": "❌ POOR", "predictions": [30, 42, 11, 46, 36, 60, 30, 83, 4, 17, 36, 65, 36, 7, 29, 36, 38, 94, 13, 68], "actuals": [83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "lcg_params": {"a": 1103515245, "c": 1013904223, "m": 4294967296, "correlation": 0.3469}}, "FRBD": {"total_predictions": 20, "exact_matches": 1, "within_5_matches": 3, "within_10_matches": 4, "exact_accuracy": 5.0, "within_5_accuracy": 15.0, "within_10_accuracy": 20.0, "average_error": 38.3, "performance": "🥉 FAIR", "predictions": [99, 24, 97, 94, 69, 18, 76, 3, 83, 9, 42, 20, 34, 38, 63, 54, 35, 59, 6, 8], "actuals": [11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "lcg_params": {"a": 48271, "c": 0, "m": 101, "correlation": 0.3627}}, "GZBD": {"total_predictions": 20, "exact_matches": 0, "within_5_matches": 2, "within_10_matches": 4, "exact_accuracy": 0.0, "within_5_accuracy": 10.0, "within_10_accuracy": 20.0, "average_error": 33.95, "performance": "❌ POOR", "predictions": [7, 54, 98, 90, 50, 76, 33, 33, 47, 7, 73, 50, 94, 63, 73, 81, 55, 38, 8, 37], "actuals": [91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "lcg_params": {"a": 1664525, "c": 1013904223, "m": 256, "correlation": 0.3958}}, "GALI": {"total_predictions": 20, "exact_matches": 0, "within_5_matches": 2, "within_10_matches": 5, "exact_accuracy": 0.0, "within_5_accuracy": 10.0, "within_10_accuracy": 25.0, "average_error": 24.5, "performance": "❌ POOR", "predictions": [49, 49, 49, 50, 50, 50, 50, 50, 50, 50, 50, 49, 50, 49, 48, 49, 49, 49, 49, 50], "actuals": [12, 64, 46, 31, 71, 22, 84, 28, 33, 74, 92, 47, 57, 87, 39, 94, 43, 92, 15, 6], "lcg_params": {"method": "pattern_based"}}}}