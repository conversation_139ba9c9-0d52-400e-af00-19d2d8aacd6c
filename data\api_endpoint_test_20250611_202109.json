{"timestamp": "2025-06-11T20:21:09.961881", "endpoints_tested": 12, "successful_responses": 12, "json_endpoints": 0, "result_endpoints": 0, "detailed_results": {"https://sattaking.com/api/results": {"url": "https://sattaking.com/api/results", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 112592, "response_time": 0.57964, "headers": {"Date": "Wed, 11 Jun 2025 14:51:09 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=2TUrOs4xOVS8ZvstYSMWttngTBTKsURJXkLxZVR4updPI3jPDY%2BaXXLMJj%2BoFabdLRjEI%2FRUrO%2FgwPVKd21ymOGX8EfgTgw5kGUUJsd48bWCGu5y4dtHHaE%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:09 GMT", "CF-RAY": "94e1ecc53b4f406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Home - Satta King</title>\n    <meta name=\"description\" content=\"Satta King home page\"/>\n    <meta name=\"keywords\" content=\"Satta King home page\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Home - Satta King\"/>\n    <meta property=\"og:description\" content=\"Satta King home page\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Home - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Satta King home page\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattakin", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/api/data": {"url": "https://sattaking.com/api/data", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 112605, "response_time": 0.607302, "headers": {"Date": "Wed, 11 Jun 2025 14:51:11 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=pmdFAJF3JzBCx6%2FCpVQFf%2B5vojcixteNm1BIOL4rHHzjWhv8%2BaJ7vufaL48InlouWHTI19cqAX58kAGNC4%2F0GPoEOJKn%2BDCSxvYfoJugeVInPcSSqNvv0ME%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:11 GMT", "CF-RAY": "94e1ecd3da12406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Home - Satta King</title>\n    <meta name=\"description\" content=\"Satta King home page\"/>\n    <meta name=\"keywords\" content=\"Satta King home page\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Home - Satta King\"/>\n    <meta property=\"og:description\" content=\"Satta King home page\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Home - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Satta King home page\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattakin", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/api/latest": {"url": "https://sattaking.com/api/latest", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 112514, "response_time": 0.559348, "headers": {"Date": "Wed, 11 Jun 2025 14:51:14 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=b1bvK%2FWZ8W8tgySMh10a0diE1RRdC9qdEx95o%2FiVNWDVDkyAwU%2BpiHlo%2FpcGjWKGoFJfKd9U%2BNj5J7oA4ZPM1KqG7S1pgjEnJSt%2F0oSsWSac9OwDppVeMw0%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:14 GMT", "CF-RAY": "94e1ece4da10406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Home - Satta King</title>\n    <meta name=\"description\" content=\"Satta King home page\"/>\n    <meta name=\"keywords\" content=\"Satta King home page\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Home - Satta King\"/>\n    <meta property=\"og:description\" content=\"Satta King home page\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Home - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Satta King home page\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattakin", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/data.php": {"url": "https://sattaking.com/data.php", "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 55594, "response_time": 0.758918, "headers": {"Date": "Wed, 11 Jun 2025 14:51:16 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=HBThp6Ck2xq1HjJXj06mzdvCHSCUQJoeAg9VH9V8KkfIq3mtKQN31D9n%2FrOIYE%2BdQyQ6NECq9fJxlWkVc1ELsBE2CH4kEwUBgTJmLjW9iFBqSTKakRzoBPs%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:16 GMT", "CF-RAY": "94e1ecf0fe21406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Error 404 - Satta King</title>\n    <meta name=\"description\" content=\"Error 404\"/>\n    <meta name=\"keywords\" content=\"error,404\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Error 404 - Satta King\"/>\n    <meta property=\"og:description\" content=\"Error 404\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Error 404 - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Error 404\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/data.php\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattaking.com/assets/css/styl", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/result.php": {"url": "https://sattaking.com/result.php", "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 55594, "response_time": 0.580415, "headers": {"Date": "Wed, 11 Jun 2025 14:51:18 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=LNXqefTJ1zNcVCNWfunVMxH2AlQnIMFiijKjVEmYyUeeIhale%2FQQKRJpOU3Orihsbpl3vyaVYNE%2FVpWWufVDhR8nWz595Lo2wuGrgOAen%2BNVkvyf2woHcjk%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:18 GMT", "CF-RAY": "94e1ecfe39d3406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Error 404 - Satta King</title>\n    <meta name=\"description\" content=\"Error 404\"/>\n    <meta name=\"keywords\" content=\"error,404\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Error 404 - Satta King\"/>\n    <meta property=\"og:description\" content=\"Error 404\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Error 404 - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Error 404\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/result.php\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattaking.com/assets/css/st", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/api.php": {"url": "https://sattaking.com/api.php", "status_code": 404, "content_type": "text/html; charset=UTF-8", "content_length": 55493, "response_time": 0.56697, "headers": {"Date": "Wed, 11 Jun 2025 14:51:20 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=BQJY4%2F54S5SmXGbEvvOHlt54nya66o58t8A4OZPy2D5rFLT2wiR%2BDbH%2BuF1tzik%2BDjqhlx1%2ByQqjtKfIyZgKj7p1xbHTML1p%2BjN%2F2a2PJt2Ao4zQ0iRE1Zg%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:20 GMT", "CF-RAY": "94e1ed0b7c1d406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Error 404 - Satta King</title>\n    <meta name=\"description\" content=\"Error 404\"/>\n    <meta name=\"keywords\" content=\"error,404\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Error 404 - Satta King\"/>\n    <meta property=\"og:description\" content=\"Error 404\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Error 404 - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Error 404\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/api.php\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattaking.com/assets/css/style", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/json/results": {"url": "https://sattaking.com/json/results", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 112598, "response_time": 0.574619, "headers": {"Date": "Wed, 11 Jun 2025 14:51:23 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=v1C9RbEcpNcnz%2BeflTa0oRqOBsjKQrLlJoISnRUQIyXOQLjCiHbRcWRxgjAW3SaU%2FlyLnGewu5AjghS63GBqhn%2BsUBzQj0XRnBQZCDTVgrd2kVrm%2BTBj9DM%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:22 GMT", "CF-RAY": "94e1ed1a4a17406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Home - Satta King</title>\n    <meta name=\"description\" content=\"Satta King home page\"/>\n    <meta name=\"keywords\" content=\"Satta King home page\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Home - Satta King\"/>\n    <meta property=\"og:description\" content=\"Satta King home page\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Home - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Satta King home page\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattakin", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattaking.com/ajax/data": {"url": "https://sattaking.com/ajax/data", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 112608, "response_time": 0.604332, "headers": {"Date": "Wed, 11 Jun 2025 14:51:26 GMT", "Content-Type": "text/html; charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "cloudflare", "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}", "Expires": "Thu, 19 Nov 1981 08:52:00 GMT", "Cache-Control": "no-store, no-cache, must-revalidate", "Pragma": "no-cache", "Cf-Cache-Status": "DYNAMIC", "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=MHrfuVKvDLWdy9ucef9ZjPaA%2BUZUFvtwORejF2Ne4Iu9ogutl6poDPPeSeVJvSKZzqH2gtBmlRduwppCMpOHvPs1Hf9AZKE0CdMexNnpQtpgnynFtGqYJeI%3D\"}]}", "Content-Encoding": "zstd", "Set-Cookie": "622c2e1c76c28_csrf_cookie=03757df2db09fe6860f2901c0eb1377e; Path=/; Max-Age=7200; Expires=Wed, 11 Jun 2025 16:51:26 GMT", "CF-RAY": "94e1ed2fedeb406e-SIN", "alt-svc": "h3=\":443\"; ma=86400"}, "content_preview": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    <title>Home - Satta King</title>\n    <meta name=\"description\" content=\"Satta King home page\"/>\n    <meta name=\"keywords\" content=\"Satta King home page\"/>\n    <meta name=\"author\" content=\"Codingest\"/>\n    <meta property=\"og:locale\" content=\"en_US\"/>\n    <meta property=\"og:site_name\" content=\"Satta King\"/>\n    <meta property=\"og:image\" content=\"https://sattaking.com/assets/img/logo.svg\"/>\n    <meta property=\"og:image:width\" content=\"240\"/>\n    <meta property=\"og:image:height\" content=\"90\"/>\n    <meta property=\"og:type\" content=\"website\"/>\n    <meta property=\"og:title\" content=\"Home - Satta King\"/>\n    <meta property=\"og:description\" content=\"Satta King home page\"/>\n    <meta property=\"og:url\" content=\"https://sattaking.com/\"/>\n    <meta property=\"fb:app_id\" content=\"\"/>\n    <meta name=\"twitter:card\" content=\"summary_large_image\"/>\n    <meta name=\"twitter:site\" content=\"@Satta King\"/>\n    <meta name=\"twitter:title\" content=\"Home - Satta King\"/>\n    <meta name=\"twitter:description\" content=\"Satta King home page\"/>\n    <link rel=\"canonical\" href=\"https://sattaking.com/\"/>\n    <link rel=\"shortcut icon\" type=\"image/png\" href=\"https://sattaking.com/uploads/logo/logo_622c326e1ad3c.png\"/>\n    <link href=\"https://sattaking.com/assets/vendor/font-icons/css/font-icon.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">    <link href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap&subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese\" rel=\"stylesheet\">        <link href=\"https://sattaking.com/assets/vendor/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\"/>\n    <link href=\"https://sattakin", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattamatka.com/api/results": {"url": "https://sattamatka.com/api/results", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 1074, "response_time": 0.836591, "headers": {"date": "Wed, 11 Jun 2025 14:51:30 GMT", "server": "Apache", "set-cookie": "__tad=1749653490.6514482; expires=Sat, 09-Jun-2035 14:51:30 GMT; Max-Age=315360000", "vary": "Accept-Encoding", "content-encoding": "gzip", "content-length": "576", "content-type": "text/html; charset=UTF-8", "connection": "close"}, "content_preview": "<html>\n<head>\n<title>sattamatka.com</title>\n<script type=\"text/javascript\" src=\"/js/fingerprint/iife.min.js\"></script>\n<script type=\"text/javascript\">\nvar redirect_link = 'http://sattamatka.com/api/results?';\n\n// Set a timeout of 300 microseconds to execute a redirect if the fingerprint promise fails for some reason\nfunction fallbackRedirect() {\n\twindow.location.replace(redirect_link+'fp=-7');\n}\n\ntry {\n\tconst rdrTimeout = setTimeout(fallbackRedirect, 300);\n\tvar fpPromise = FingerprintJS.load({monitoring: false});\n\tfpPromise\n\t\t.then(fp => fp.get())\n\t\t.then(\n\t\t\tresult => { \n\t\t\t\tvar fprt = 'fp='+result.visitorId;\n\t\t\t\tclearTimeout(rdrTimeout);\n\t\t\t\twindow.location.replace(redirect_link+fprt);\n\t\t});\n} catch(err) {\n\tfallbackRedirect();\n}\n\n</script>\n<style> body { background:#101c36 } </style>\n</head>\n<body bgcolor=\"#ffffff\" text=\"#000000\">\n<div style='display: none;'><a href='http://sattamatka.com/api/results?fp=-3'>Click here to enter</a></div>\n<noscript><meta http-equiv=\"refresh\" content=\"0; URL=http://sattamatka.com/api/results?fp=-5\"></noscript>\n</body>\n</html>", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattamatka.com/api/data": {"url": "https://sattamatka.com/api/data", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 1065, "response_time": 0.911844, "headers": {"date": "Wed, 11 Jun 2025 14:51:31 GMT", "server": "Apache", "vary": "Accept-Encoding", "content-encoding": "gzip", "content-length": "575", "content-type": "text/html; charset=UTF-8", "connection": "close"}, "content_preview": "<html>\n<head>\n<title>sattamatka.com</title>\n<script type=\"text/javascript\" src=\"/js/fingerprint/iife.min.js\"></script>\n<script type=\"text/javascript\">\nvar redirect_link = 'http://sattamatka.com/api/data?';\n\n// Set a timeout of 300 microseconds to execute a redirect if the fingerprint promise fails for some reason\nfunction fallbackRedirect() {\n\twindow.location.replace(redirect_link+'fp=-7');\n}\n\ntry {\n\tconst rdrTimeout = setTimeout(fallbackRedirect, 300);\n\tvar fpPromise = FingerprintJS.load({monitoring: false});\n\tfpPromise\n\t\t.then(fp => fp.get())\n\t\t.then(\n\t\t\tresult => { \n\t\t\t\tvar fprt = 'fp='+result.visitorId;\n\t\t\t\tclearTimeout(rdrTimeout);\n\t\t\t\twindow.location.replace(redirect_link+fprt);\n\t\t});\n} catch(err) {\n\tfallbackRedirect();\n}\n\n</script>\n<style> body { background:#101c36 } </style>\n</head>\n<body bgcolor=\"#ffffff\" text=\"#000000\">\n<div style='display: none;'><a href='http://sattamatka.com/api/data?fp=-3'>Click here to enter</a></div>\n<noscript><meta http-equiv=\"refresh\" content=\"0; URL=http://sattamatka.com/api/data?fp=-5\"></noscript>\n</body>\n</html>", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattamatka.com/data.php": {"url": "https://sattamatka.com/data.php", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 1065, "response_time": 0.83124, "headers": {"date": "Wed, 11 Jun 2025 14:51:33 GMT", "server": "Apache", "set-cookie": "__tad=1749653493.3719771; expires=Sat, 09-Jun-2035 14:51:33 GMT; Max-Age=315360000", "vary": "Accept-Encoding", "content-encoding": "gzip", "content-length": "576", "content-type": "text/html; charset=UTF-8", "connection": "close"}, "content_preview": "<html>\n<head>\n<title>sattamatka.com</title>\n<script type=\"text/javascript\" src=\"/js/fingerprint/iife.min.js\"></script>\n<script type=\"text/javascript\">\nvar redirect_link = 'http://sattamatka.com/data.php?';\n\n// Set a timeout of 300 microseconds to execute a redirect if the fingerprint promise fails for some reason\nfunction fallbackRedirect() {\n\twindow.location.replace(redirect_link+'fp=-7');\n}\n\ntry {\n\tconst rdrTimeout = setTimeout(fallbackRedirect, 300);\n\tvar fpPromise = FingerprintJS.load({monitoring: false});\n\tfpPromise\n\t\t.then(fp => fp.get())\n\t\t.then(\n\t\t\tresult => { \n\t\t\t\tvar fprt = 'fp='+result.visitorId;\n\t\t\t\tclearTimeout(rdrTimeout);\n\t\t\t\twindow.location.replace(redirect_link+fprt);\n\t\t});\n} catch(err) {\n\tfallbackRedirect();\n}\n\n</script>\n<style> body { background:#101c36 } </style>\n</head>\n<body bgcolor=\"#ffffff\" text=\"#000000\">\n<div style='display: none;'><a href='http://sattamatka.com/data.php?fp=-3'>Click here to enter</a></div>\n<noscript><meta http-equiv=\"refresh\" content=\"0; URL=http://sattamatka.com/data.php?fp=-5\"></noscript>\n</body>\n</html>", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}, "https://sattamatka.com/result.php": {"url": "https://sattamatka.com/result.php", "status_code": 200, "content_type": "text/html; charset=UTF-8", "content_length": 1071, "response_time": 1.081045, "headers": {"date": "Wed, 11 Jun 2025 14:51:35 GMT", "server": "Apache", "vary": "Accept-Encoding", "content-encoding": "gzip", "content-length": "576", "content-type": "text/html; charset=UTF-8", "connection": "close"}, "content_preview": "<html>\n<head>\n<title>sattamatka.com</title>\n<script type=\"text/javascript\" src=\"/js/fingerprint/iife.min.js\"></script>\n<script type=\"text/javascript\">\nvar redirect_link = 'http://sattamatka.com/result.php?';\n\n// Set a timeout of 300 microseconds to execute a redirect if the fingerprint promise fails for some reason\nfunction fallbackRedirect() {\n\twindow.location.replace(redirect_link+'fp=-7');\n}\n\ntry {\n\tconst rdrTimeout = setTimeout(fallbackRedirect, 300);\n\tvar fpPromise = FingerprintJS.load({monitoring: false});\n\tfpPromise\n\t\t.then(fp => fp.get())\n\t\t.then(\n\t\t\tresult => { \n\t\t\t\tvar fprt = 'fp='+result.visitorId;\n\t\t\t\tclearTimeout(rdrTimeout);\n\t\t\t\twindow.location.replace(redirect_link+fprt);\n\t\t});\n} catch(err) {\n\tfallbackRedirect();\n}\n\n</script>\n<style> body { background:#101c36 } </style>\n</head>\n<body bgcolor=\"#ffffff\" text=\"#000000\">\n<div style='display: none;'><a href='http://sattamatka.com/result.php?fp=-3'>Click here to enter</a></div>\n<noscript><meta http-equiv=\"refresh\" content=\"0; URL=http://sattamatka.com/result.php?fp=-5\"></noscript>\n</body>\n</html>", "is_json": false, "is_html": true, "contains_results": false, "data_structure": "html"}}}