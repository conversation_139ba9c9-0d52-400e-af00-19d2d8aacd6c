{"timestamp": "2025-06-11T18:57:32.443241", "analysis_type": "Final Comprehensive Randomness & Prediction Analysis", "findings": {"randomness_logic": {"DSWR": {"sample_size": 1998, "distribution_type": "UNIFORM_DISTRIBUTION", "randomness_quality": "GOOD", "generation_method": "MODULAR_ARITHMETIC_BASED", "exploitable_weaknesses": []}, "FRBD": {"sample_size": 1964, "distribution_type": "UNIFORM_DISTRIBUTION", "randomness_quality": "GOOD", "generation_method": "MODULAR_ARITHMETIC_BASED", "exploitable_weaknesses": [{"type": "MODULAR_BIAS", "modulus": 2, "preferred_remainder": 0.0, "exploitability": "HIGH", "description": "Numbers favor remainder 0.0 when divided by 2"}, {"type": "RANGE_BIAS", "range": [0, 33], "count": "788", "expected": 654.6666666666666, "exploitability": "LOW", "description": "Range (0, 33) appears 788 times vs expected 655"}]}, "GZBD": {"sample_size": 1955, "distribution_type": "PSEUDO_UNIFORM", "randomness_quality": "GOOD", "generation_method": "MODULAR_ARITHMETIC_BASED", "exploitable_weaknesses": [{"type": "RANGE_BIAS", "range": [0, 33], "count": "817", "expected": 651.6666666666666, "exploitability": "LOW", "description": "Range (0, 33) appears 817 times vs expected 652"}]}, "GALI": {"sample_size": 1120, "distribution_type": "UNIFORM_DISTRIBUTION", "randomness_quality": "GOOD", "generation_method": "MODULAR_ARITHMETIC_BASED", "exploitable_weaknesses": []}}, "pattern_exploitation": {"DSWR": {"strategies": {"trend_following": {"success_rate": 32.06221776216759, "total_tests": 1993, "description": "Predict direction based on recent trend"}, "mean_reversion": {"success_rate": 80.19292604501608, "total_tests": 1555, "description": "Predict reversion when far from mean"}, "modular_exploitation": {"success_rate": 34.55734406438632, "best_modulus": 3, "description": "Exploit modular bias (mod 3)"}, "frequency_analysis": {"success_rate": 6.707317073170732, "total_tests": 1968, "description": "Predict based on frequency analysis"}, "range_rotation": {"success_rate": 34.356136820925556, "total_tests": 1988, "description": "Predict range rotation patterns"}}, "successful_strategies": 1, "overall_potential": "LOW"}, "FRBD": {"strategies": {"trend_following": {"success_rate": 33.84379785604901, "total_tests": 1959, "description": "Predict direction based on recent trend"}, "mean_reversion": {"success_rate": 78.97106109324758, "total_tests": 1555, "description": "Predict reversion when far from mean"}, "modular_exploitation": {"success_rate": 34.54452405322416, "best_modulus": 3, "description": "Exploit modular bias (mod 3)"}, "frequency_analysis": {"success_rate": 7.394002068252327, "total_tests": 1934, "description": "Predict based on frequency analysis"}, "range_rotation": {"success_rate": 33.87922210849539, "total_tests": 1954, "description": "Predict range rotation patterns"}}, "successful_strategies": 1, "overall_potential": "LOW"}, "GZBD": {"strategies": {"trend_following": {"success_rate": 32.92307692307692, "total_tests": 1950, "description": "Predict direction based on recent trend"}, "mean_reversion": {"success_rate": 78.77389584706658, "total_tests": 1517, "description": "Predict reversion when far from mean"}, "modular_exploitation": {"success_rate": 35.47557840616967, "best_modulus": 3, "description": "Exploit modular bias (mod 3)"}, "frequency_analysis": {"success_rate": 7.428571428571429, "total_tests": 1925, "description": "Predict based on frequency analysis"}, "range_rotation": {"success_rate": 31.72236503856041, "total_tests": 1945, "description": "Predict range rotation patterns"}}, "successful_strategies": 1, "overall_potential": "LOW"}, "GALI": {"strategies": {"trend_following": {"success_rate": 33.7219730941704, "total_tests": 1115, "description": "Predict direction based on recent trend"}, "mean_reversion": {"success_rate": 81.92488262910797, "total_tests": 852, "description": "Predict reversion when far from mean"}, "modular_exploitation": {"success_rate": 35.4054054054054, "best_modulus": 3, "description": "Exploit modular bias (mod 3)"}, "frequency_analysis": {"success_rate": 6.7889908256880735, "total_tests": 1090, "description": "Predict based on frequency analysis"}, "range_rotation": {"success_rate": 32.972972972972975, "total_tests": 1110, "description": "Predict range rotation patterns"}}, "successful_strategies": 1, "overall_potential": "LOW"}}, "prediction_loopholes": {"DSWR": [{"type": "MODULAR_BIAS", "modulus": 13, "remainder": 10, "bias_strength": 0.24524524524524519, "frequency": 0.05805805805805806, "exploitability": "MEDIUM"}, {"type": "AUTOCORRELATION", "correlation": -0.06525090654001317, "direction": "NEGATIVE", "exploitability": "MEDIUM"}, {"type": "RANGE_BIAS", "range": "0-33", "count": "767", "expected": 666.0, "bias_strength": 0.15165165165165165, "exploitability": "LOW"}], "FRBD": [{"type": "MODULAR_BIAS", "modulus": 13, "remainder": 5.0, "bias_strength": 0.24439918533604896, "frequency": 0.09572301425661914, "exploitability": "MEDIUM"}, {"type": "RANGE_BIAS", "range": "0-33", "count": "788", "expected": 654.6666666666666, "bias_strength": 0.2036659877800408, "exploitability": "MEDIUM"}], "GZBD": [{"type": "MODULAR_BIAS", "modulus": 13, "remainder": 3.0, "bias_strength": 0.26342710997442453, "frequency": 0.09718670076726342, "exploitability": "MEDIUM"}, {"type": "MODULAR_BIAS", "modulus": 13, "remainder": 12.0, "bias_strength": 0.24859335038363173, "frequency": 0.05780051150895141, "exploitability": "MEDIUM"}, {"type": "RANGE_BIAS", "range": "0-33", "count": "817", "expected": 651.6666666666666, "bias_strength": 0.25370843989769826, "exploitability": "MEDIUM"}, {"type": "RANGE_BIAS", "range": "34-66", "count": "540", "expected": 651.6666666666666, "bias_strength": 0.17135549872122757, "exploitability": "LOW"}], "GALI": [{"type": "MODULAR_BIAS", "modulus": 13, "remainder": 11.0, "bias_strength": 0.22232142857142864, "frequency": 0.059821428571428574, "exploitability": "MEDIUM"}, {"type": "MODULAR_BIAS", "modulus": 13, "remainder": 9.0, "bias_strength": 0.32678571428571435, "frequency": 0.05178571428571429, "exploitability": "HIGH"}, {"type": "AUTOCORRELATION", "correlation": -0.05250877844720171, "direction": "NEGATIVE", "exploitability": "MEDIUM"}]}}, "final_verdict": {"verdict": "MEDIUM_PREDICTION_POTENTIAL", "confidence": "MEDIUM", "total_weaknesses": 3, "total_loopholes": 12, "high_exploitability_count": 2}, "methodology": ["Exact randomness logic analysis", "Pattern exploitation testing", "Prediction loophole identification", "Statistical weakness assessment", "Comprehensive strategy evaluation"]}