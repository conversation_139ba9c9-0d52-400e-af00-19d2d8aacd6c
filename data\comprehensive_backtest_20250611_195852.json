{"timestamp": "2025-06-11T19:58:52.547310", "analysis_type": "Comprehensive Strategy Backtest", "test_period": "30 most recent draws per game", "game_results": {"DSWR": {"test_size": 30, "strategies": {"lcg_formula": {"accuracy": 0.0, "within_5_accuracy": 10.0, "within_10_accuracy": 23.333333333333332, "predictions": [61, 43, 11, 59, 86, 65, 93, 45, 18, 41, 30, 42, 11, 46, 36, 60, 30, 83, 4, 17, 36, 65, 36, 7, 29, 36, 38, 94, 13, 68], "actuals": [20, 28, 16, 47, 90, 58, 70, 39, 46, 11, 83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "LCG: X(n+1) = (1103515245 * X + 1013904223) mod 4294967296"}, "recent_avoidance": {"accuracy": 86.66666666666667, "predictions": ["NOT in [15, 22, 23, 29, 32, 41, 59, 66, 86, 93]", "NOT in [15, 20, 22, 23, 29, 32, 59, 66, 86, 93]", "NOT in [15, 20, 22, 23, 28, 29, 32, 59, 66, 86]", "NOT in [15, 16, 20, 23, 28, 29, 32, 59, 66, 86]", "NOT in [15, 16, 20, 23, 28, 29, 47, 59, 66, 86]", "NOT in [15, 16, 20, 28, 29, 47, 59, 66, 86, 90]", "NOT in [16, 20, 28, 29, 47, 58, 59, 66, 86, 90]", "NOT in [16, 20, 28, 29, 47, 58, 66, 70, 86, 90]", "NOT in [16, 20, 28, 39, 47, 58, 66, 70, 86, 90]", "NOT in [16, 20, 28, 39, 46, 47, 58, 66, 70, 90]", "NOT in [11, 16, 20, 28, 39, 46, 47, 58, 70, 90]", "NOT in [11, 16, 28, 39, 46, 47, 58, 70, 83, 90]", "NOT in [11, 16, 28, 39, 46, 47, 58, 70, 83, 90]", "NOT in [7, 11, 28, 39, 46, 47, 58, 70, 83, 90]", "NOT in [7, 9, 11, 28, 39, 46, 58, 70, 83, 90]", "NOT in [7, 9, 11, 28, 39, 46, 53, 58, 70, 83]", "NOT in [7, 9, 11, 28, 39, 46, 53, 70, 83]", "NOT in [7, 9, 11, 28, 39, 46, 53, 60, 83]", "NOT in [7, 9, 11, 28, 46, 53, 60, 83, 93]", "NOT in [7, 9, 11, 26, 28, 53, 60, 83, 93]", "NOT in [7, 9, 11, 26, 28, 53, 60, 83, 93]", "NOT in [7, 9, 11, 14, 26, 28, 53, 60, 93]", "NOT in [7, 9, 11, 14, 26, 53, 60, 93]", "NOT in [4, 9, 11, 14, 26, 53, 60, 93]", "NOT in [4, 9, 11, 14, 26, 53, 60, 74, 93]", "NOT in [4, 9, 11, 14, 26, 60, 74, 85, 93]", "NOT in [4, 9, 14, 26, 59, 60, 74, 85, 93]", "NOT in [4, 9, 14, 26, 59, 74, 85, 93, 95]", "NOT in [2, 4, 9, 14, 26, 59, 74, 85, 95]", "NOT in [1, 2, 4, 9, 14, 59, 74, 85, 95]"], "actuals": [20, 28, 16, 47, 90, 58, 70, 39, 46, 11, 83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "Avoid numbers from last 10 draws"}, "pattern_breaking": {"accuracy": 86.66666666666667, "predictions": ["NOT 46 (break pattern)", "NOT 36 (break pattern)", "NOT 4 (break pattern)", "NOT 78 (break pattern)", "NOT 26 (break pattern)", "NOT 82 (break pattern)", "NOT 8 (break pattern)", "NOT 53 (break pattern)", "NOT 11 (break pattern)", "NOT 97 (break pattern)", "NOT 19 (break pattern)", "NOT 4 (break pattern)", "NOT 96 (break pattern)", "NOT 33 (break pattern)", "NOT 0 (break pattern)"], "actuals": [20, 16, 47, 90, 70, 39, 46, 11, 53, 11, 9, 4, 59, 95, 3], "method": "Predict arithmetic pattern breaks"}, "range_rotation": {"accuracy": 36.666666666666664, "predictions": ["Range (67, 99)", "Range (34, 66)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (34, 66)", "Range (0, 33)", "Range (67, 99)", "Range (67, 99)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (34, 66)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (34, 66)", "Range (34, 66)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)"], "actuals": [20, 28, 16, 47, 90, 58, 70, 39, 46, 11, 83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "Predict least used range"}, "mean_reversion": {"accuracy": 61.53846153846154, "predictions": ["DOWN toward 40.3", "UP toward 40.1", "UP toward 40.2", "UP toward 37.5", "DOWN toward 41.5", "DOWN toward 42.5", "DOWN toward 42.9", "UP toward 43.4", "DOWN toward 44.7", "UP toward 45.6", "UP toward 43.8", "UP toward 43.9", "UP toward 42.8", "DOWN toward 42.8", "DOWN toward 44.2", "UP toward 42.4", "UP toward 42.3", "UP toward 41.4", "UP toward 38.6", "UP toward 38.0", "DOWN toward 39.4", "DOWN toward 41.5", "DOWN toward 43.0", "DOWN toward 44.2", "UP toward 43.3", "UP toward 40.4"], "actuals": [20, 28, 16, 47, 58, 70, 39, 83, 28, 7, 9, 53, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "Predict reversion to 30-day mean"}, "prime_pattern": {"accuracy": 30.0, "predictions": ["Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number"], "actuals": [20, 28, 16, 47, 90, 58, 70, 39, 46, 11, 83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "Predict prime numbers more likely"}, "random_baseline": {"accuracy": 0.0, "within_5_accuracy": 10.0, "predictions": [51, 92, 14, 71, 60, 20, 82, 86, 74, 74, 87, 99, 23, 2, 21, 52, 1, 87, 29, 37, 1, 63, 59, 20, 32, 75, 57, 21, 88, 48], "actuals": [20, 28, 16, 47, 90, 58, 70, 39, 46, 11, 83, 28, 7, 9, 53, 11, 60, 93, 26, 9, 14, 9, 4, 74, 85, 59, 95, 2, 1, 3], "method": "Random number generation"}}}, "FRBD": {"test_size": 30, "strategies": {"lcg_formula": {"accuracy": 3.3333333333333335, "within_5_accuracy": 13.333333333333334, "within_10_accuracy": 26.666666666666668, "predictions": [51, 32, 4, 15, 45, 44, 19, 52, 99, 62, 99, 24, 97, 94, 69, 18, 76, 3, 83, 9, 42, 20, 34, 38, 63, 54, 35, 59, 6, 8], "actuals": [82, 86, 70, 8, 37, 55, 7, 58, 20, 58, 11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "method": "LCG: X(n+1) = (48271 * X + 0) mod 101"}, "recent_avoidance": {"accuracy": 93.33333333333333, "predictions": ["NOT in [20.0, 28.0, 32.0, 36.0, 75.0, 78.0, 79.0, 82.0, 84.0, 88.0]", "NOT in [20.0, 28.0, 32.0, 36.0, 75.0, 78.0, 82.0, 84.0, 88.0]", "NOT in [20.0, 28.0, 32.0, 36.0, 75.0, 78.0, 82.0, 86.0, 88.0]", "NOT in [20.0, 28.0, 32.0, 36.0, 70.0, 75.0, 78.0, 82.0, 86.0, 88.0]", "NOT in [8.0, 28.0, 32.0, 36.0, 70.0, 75.0, 78.0, 82.0, 86.0, 88.0]", "NOT in [8.0, 28.0, 32.0, 36.0, 37.0, 70.0, 75.0, 78.0, 82.0, 86.0]", "NOT in [8.0, 32.0, 36.0, 37.0, 55.0, 70.0, 75.0, 78.0, 82.0, 86.0]", "NOT in [7.0, 8.0, 36.0, 37.0, 55.0, 70.0, 75.0, 78.0, 82.0, 86.0]", "NOT in [7.0, 8.0, 36.0, 37.0, 55.0, 58.0, 70.0, 78.0, 82.0, 86.0]", "NOT in [7.0, 8.0, 20.0, 36.0, 37.0, 55.0, 58.0, 70.0, 82.0, 86.0]", "NOT in [7.0, 8.0, 20.0, 37.0, 55.0, 58.0, 70.0, 82.0, 86.0]", "NOT in [7.0, 8.0, 11.0, 20.0, 37.0, 55.0, 58.0, 70.0, 86.0]", "NOT in [7.0, 8.0, 11.0, 15.0, 20.0, 37.0, 55.0, 58.0, 70.0]", "NOT in [1.0, 7.0, 8.0, 11.0, 15.0, 20.0, 37.0, 55.0, 58.0]", "NOT in [1.0, 7.0, 11.0, 15.0, 19.0, 20.0, 37.0, 55.0, 58.0]", "NOT in [1.0, 7.0, 11.0, 15.0, 19.0, 20.0, 55.0, 58.0, 84.0]", "NOT in [1.0, 7.0, 11.0, 15.0, 18.0, 19.0, 20.0, 58.0, 84.0]", "NOT in [1.0, 11.0, 14.0, 15.0, 18.0, 19.0, 20.0, 58.0, 84.0]", "NOT in [1.0, 11.0, 14.0, 15.0, 17.0, 18.0, 19.0, 20.0, 58.0, 84.0]", "NOT in [1.0, 11.0, 14.0, 15.0, 17.0, 18.0, 19.0, 42.0, 58.0, 84.0]", "NOT in [1.0, 11.0, 14.0, 15.0, 17.0, 18.0, 19.0, 42.0, 84.0, 95.0]", "NOT in [1.0, 14.0, 15.0, 17.0, 18.0, 19.0, 26.0, 42.0, 84.0, 95.0]", "NOT in [1.0, 14.0, 17.0, 18.0, 19.0, 24.0, 26.0, 42.0, 84.0, 95.0]", "NOT in [9.0, 14.0, 17.0, 18.0, 19.0, 24.0, 26.0, 42.0, 84.0, 95.0]", "NOT in [9.0, 14.0, 17.0, 18.0, 24.0, 26.0, 42.0, 84.0, 92.0, 95.0]", "NOT in [9.0, 14.0, 17.0, 18.0, 24.0, 26.0, 42.0, 50.0, 92.0, 95.0]", "NOT in [9.0, 14.0, 17.0, 24.0, 26.0, 42.0, 50.0, 92.0, 95.0, 96.0]", "NOT in [6.0, 9.0, 17.0, 24.0, 26.0, 42.0, 50.0, 92.0, 95.0, 96.0]", "NOT in [6.0, 9.0, 24.0, 26.0, 28.0, 42.0, 50.0, 92.0, 95.0, 96.0]", "NOT in [6.0, 9.0, 24.0, 26.0, 28.0, 50.0, 71.0, 92.0, 95.0, 96.0]"], "actuals": [82, 86, 70, 8, 37, 55, 7, 58, 20, 58, 11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "method": "Avoid numbers from last 10 draws"}, "pattern_breaking": {"accuracy": 100.0, "predictions": ["NOT 90 (break pattern)", "NOT 54 (break pattern)", "NOT 66 (break pattern)", "NOT 73 (break pattern)", "NOT 96 (break pattern)", "NOT 19 (break pattern)", "NOT 37 (break pattern)", "NOT 10 (break pattern)", "NOT 20 (break pattern)", "NOT 67 (break pattern)", "NOT 22 (break pattern)", "NOT 8 (break pattern)", "NOT 50 (break pattern)"], "actuals": [70, 8, 55, 7, 11, 1, 84, 17, 42, 95, 9, 96, 71], "method": "Predict arithmetic pattern breaks"}, "range_rotation": {"accuracy": 30.0, "predictions": ["Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (67, 99)", "Range (34, 66)", "Range (67, 99)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)"], "actuals": [82, 86, 70, 8, 37, 55, 7, 58, 20, 58, 11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "method": "Predict least used range"}, "mean_reversion": {"accuracy": 80.0, "predictions": ["UP toward 53.2", "DOWN toward 55.3", "DOWN toward 58.1", "DOWN toward 57.4", "UP toward 57.6", "UP toward 56.5", "UP toward 54.9", "UP toward 54.1", "UP toward 51.0", "UP toward 51.3", "UP toward 49.2", "UP toward 48.5", "DOWN toward 48.3", "UP toward 46.6", "UP toward 45.8", "UP toward 44.8", "DOWN toward 46.6", "UP toward 44.9", "UP toward 42.9", "UP toward 40.4", "DOWN toward 42.8", "DOWN toward 43.8", "UP toward 43.0", "UP toward 41.4", "DOWN toward 41.2"], "actuals": [82, 86, 70, 8, 37, 55, 58, 58, 15, 1, 19, 84, 18, 14, 17, 42, 26, 24, 9, 92, 50, 6, 28, 71, 7], "method": "Predict reversion to 30-day mean"}, "prime_pattern": {"accuracy": 23.333333333333332, "predictions": ["Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number"], "actuals": [82, 86, 70, 8, 37, 55, 7, 58, 20, 58, 11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "method": "Predict prime numbers more likely"}, "random_baseline": {"accuracy": 0.0, "within_5_accuracy": 0.0, "predictions": [51, 92, 14, 71, 60, 20, 82, 86, 74, 74, 87, 99, 23, 2, 21, 52, 1, 87, 29, 37, 1, 63, 59, 20, 32, 75, 57, 21, 88, 48], "actuals": [82, 86, 70, 8, 37, 55, 7, 58, 20, 58, 11, 15, 1, 19, 84, 18, 14, 17, 42, 95, 26, 24, 9, 92, 50, 96, 6, 28, 71, 7], "method": "Random number generation"}}}, "GZBD": {"test_size": 30, "strategies": {"lcg_formula": {"accuracy": 0.0, "within_5_accuracy": 10.0, "within_10_accuracy": 20.0, "predictions": [34, 11, 11, 51, 81, 20, 38, 21, 68, 2, 7, 54, 98, 90, 50, 76, 33, 33, 47, 7, 73, 50, 94, 63, 73, 81, 55, 38, 8, 37], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 99, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "method": "LCG: X(n+1) = (1664525 * X + 1013904223) mod 256"}, "recent_avoidance": {"accuracy": 93.33333333333333, "predictions": ["NOT in [3.0, 28.0, 33.0, 34.0, 57.0, 70.0, 79.0, 86.0, 88.0]", "NOT in [3.0, 28.0, 33.0, 34.0, 70.0, 79.0, 86.0, 88.0, 92.0]", "NOT in [3.0, 28.0, 33.0, 34.0, 70.0, 79.0, 80.0, 86.0, 88.0, 92.0]", "NOT in [3.0, 28.0, 33.0, 34.0, 36.0, 70.0, 80.0, 86.0, 88.0, 92.0]", "NOT in [3.0, 28.0, 34.0, 36.0, 58.0, 70.0, 80.0, 86.0, 88.0, 92.0]", "NOT in [3.0, 28.0, 34.0, 36.0, 58.0, 61.0, 70.0, 80.0, 88.0, 92.0]", "NOT in [3.0, 23.0, 34.0, 36.0, 58.0, 61.0, 70.0, 80.0, 88.0, 92.0]", "NOT in [2.0, 3.0, 23.0, 34.0, 36.0, 58.0, 61.0, 80.0, 88.0, 92.0]", "NOT in [2.0, 3.0, 23.0, 34.0, 36.0, 45.0, 58.0, 61.0, 80.0, 92.0]", "NOT in [2.0, 3.0, 23.0, 36.0, 45.0, 58.0, 61.0, 80.0, 92.0, 99.0]", "NOT in [2.0, 23.0, 36.0, 45.0, 58.0, 61.0, 72.0, 80.0, 92.0, 99.0]", "NOT in [2.0, 23.0, 36.0, 45.0, 58.0, 61.0, 72.0, 80.0, 91.0, 99.0]", "NOT in [2.0, 23.0, 36.0, 45.0, 58.0, 61.0, 72.0, 79.0, 91.0, 99.0]", "NOT in [2.0, 23.0, 27.0, 45.0, 58.0, 61.0, 72.0, 79.0, 91.0, 99.0]", "NOT in [2.0, 23.0, 27.0, 45.0, 61.0, 71.0, 72.0, 79.0, 91.0, 99.0]", "NOT in [2.0, 23.0, 27.0, 45.0, 71.0, 72.0, 79.0, 85.0, 91.0, 99.0]", "NOT in [2.0, 27.0, 45.0, 71.0, 72.0, 74.0, 79.0, 85.0, 91.0, 99.0]", "NOT in [27.0, 45.0, 71.0, 72.0, 74.0, 79.0, 85.0, 91.0, 99.0]", "NOT in [4.0, 27.0, 71.0, 72.0, 74.0, 79.0, 85.0, 91.0, 99.0]", "NOT in [4.0, 27.0, 60.0, 71.0, 72.0, 74.0, 79.0, 85.0, 91.0]", "NOT in [4.0, 6.0, 27.0, 60.0, 71.0, 74.0, 79.0, 85.0, 91.0]", "NOT in [4.0, 6.0, 27.0, 60.0, 71.0, 74.0, 79.0, 85.0, 95.0]", "NOT in [4.0, 6.0, 27.0, 59.0, 60.0, 71.0, 74.0, 85.0, 95.0]", "NOT in [4.0, 6.0, 59.0, 60.0, 71.0, 74.0, 84.0, 85.0, 95.0]", "NOT in [4.0, 6.0, 59.0, 60.0, 74.0, 84.0, 85.0, 95.0]", "NOT in [4.0, 6.0, 46.0, 59.0, 60.0, 74.0, 84.0, 95.0]", "NOT in [4.0, 6.0, 32.0, 46.0, 59.0, 60.0, 74.0, 84.0, 95.0]", "NOT in [4.0, 6.0, 11.0, 32.0, 46.0, 59.0, 60.0, 84.0, 95.0]", "NOT in [1.0, 6.0, 11.0, 32.0, 46.0, 59.0, 60.0, 84.0, 95.0]", "NOT in [1.0, 6.0, 11.0, 32.0, 46.0, 59.0, 84.0, 94.0, 95.0]"], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 99, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "method": "Avoid numbers from last 10 draws"}, "pattern_breaking": {"accuracy": 100.0, "predictions": ["NOT 68 (break pattern)", "NOT 80 (break pattern)", "NOT 64 (break pattern)", "NOT 88 (break pattern)", "NOT 45 (break pattern)", "NOT 67 (break pattern)", "NOT 99 (break pattern)", "NOT 63 (break pattern)", "NOT 74 (break pattern)", "NOT 23 (break pattern)", "NOT 86 (break pattern)", "NOT 18 (break pattern)"], "actuals": [36, 61, 23, 99, 91, 27, 74, 74, 4, 84, 32, 11], "method": "Predict arithmetic pattern breaks"}, "range_rotation": {"accuracy": 43.333333333333336, "predictions": ["Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (67, 99)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (34, 66)", "Range (0, 33)", "Range (34, 66)", "Range (67, 99)", "Range (67, 99)", "Range (34, 66)"], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 99, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "method": "Predict least used range"}, "mean_reversion": {"accuracy": 73.07692307692307, "predictions": ["UP toward 46.9", "DOWN toward 46.8", "DOWN toward 46.6", "UP toward 46.6", "DOWN toward 48.0", "DOWN toward 49.0", "UP toward 47.1", "UP toward 45.4", "DOWN toward 48.3", "DOWN toward 48.6", "DOWN toward 49.8", "DOWN toward 50.6", "UP toward 51.3", "DOWN toward 52.1", "DOWN toward 52.0", "DOWN toward 53.2", "DOWN toward 55.5", "UP toward 54.1", "UP toward 55.0", "DOWN toward 56.3", "DOWN toward 57.3", "UP toward 56.4", "UP toward 55.2", "UP toward 53.2", "UP toward 50.3", "DOWN toward 52.3"], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 95, 59, 6, 46, 11, 1, 94, 19], "method": "Predict reversion to 30-day mean"}, "prime_pattern": {"accuracy": 26.666666666666668, "predictions": ["Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number"], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 99, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "method": "Predict prime numbers more likely"}, "random_baseline": {"accuracy": 0.0, "within_5_accuracy": 20.0, "predictions": [51, 92, 14, 71, 60, 20, 82, 86, 74, 74, 87, 99, 23, 2, 21, 52, 1, 87, 29, 37, 1, 63, 59, 20, 32, 75, 57, 21, 88, 48], "actuals": [92, 80, 36, 58, 61, 23, 2, 45, 99, 72, 91, 79, 27, 71, 85, 74, 74, 4, 60, 6, 95, 59, 84, 6, 46, 32, 11, 1, 94, 19], "method": "Random number generation"}}}, "GALI": {"test_size": 30, "strategies": {"lcg_formula": {"accuracy": 0, "predictions": [], "actuals": [], "method": "No LCG formula"}, "recent_avoidance": {"accuracy": 96.66666666666667, "predictions": ["NOT in [5.0, 25.0, 46.0, 47.0, 58.0, 68.0, 70.0, 72.0, 73.0, 98.0]", "NOT in [5.0, 25.0, 30.0, 46.0, 47.0, 58.0, 68.0, 72.0, 73.0, 98.0]", "NOT in [25.0, 30.0, 46.0, 47.0, 58.0, 68.0, 72.0, 73.0, 75.0, 98.0]", "NOT in [10.0, 25.0, 30.0, 47.0, 58.0, 68.0, 72.0, 73.0, 75.0, 98.0]", "NOT in [10.0, 25.0, 30.0, 38.0, 47.0, 68.0, 72.0, 73.0, 75.0, 98.0]", "NOT in [10.0, 25.0, 30.0, 38.0, 47.0, 53.0, 72.0, 73.0, 75.0, 98.0]", "NOT in [10.0, 23.0, 25.0, 30.0, 38.0, 53.0, 72.0, 73.0, 75.0, 98.0]", "NOT in [10.0, 23.0, 25.0, 30.0, 38.0, 53.0, 72.0, 75.0, 90.0, 98.0]", "NOT in [10.0, 23.0, 30.0, 38.0, 53.0, 72.0, 75.0, 90.0, 94.0, 98.0]", "NOT in [10.0, 21.0, 23.0, 30.0, 38.0, 53.0, 75.0, 90.0, 94.0, 98.0]", "NOT in [10.0, 21.0, 23.0, 30.0, 38.0, 53.0, 69.0, 75.0, 90.0, 94.0]", "NOT in [10.0, 12.0, 21.0, 23.0, 38.0, 53.0, 69.0, 75.0, 90.0, 94.0]", "NOT in [10.0, 12.0, 21.0, 23.0, 38.0, 53.0, 64.0, 69.0, 90.0, 94.0]", "NOT in [12.0, 21.0, 23.0, 38.0, 46.0, 53.0, 64.0, 69.0, 90.0, 94.0]", "NOT in [12.0, 21.0, 23.0, 31.0, 46.0, 53.0, 64.0, 69.0, 90.0, 94.0]", "NOT in [12.0, 21.0, 23.0, 31.0, 46.0, 64.0, 69.0, 71.0, 90.0, 94.0]", "NOT in [12.0, 21.0, 22.0, 31.0, 46.0, 64.0, 69.0, 71.0, 90.0, 94.0]", "NOT in [12.0, 21.0, 22.0, 31.0, 46.0, 64.0, 69.0, 71.0, 84.0, 94.0]", "NOT in [12.0, 21.0, 22.0, 28.0, 31.0, 46.0, 64.0, 69.0, 71.0, 84.0]", "NOT in [12.0, 22.0, 28.0, 31.0, 33.0, 46.0, 64.0, 69.0, 71.0, 84.0]", "NOT in [12.0, 22.0, 28.0, 31.0, 33.0, 46.0, 64.0, 71.0, 74.0, 84.0]", "NOT in [22.0, 28.0, 31.0, 33.0, 46.0, 64.0, 71.0, 74.0, 84.0, 92.0]", "NOT in [22.0, 28.0, 31.0, 33.0, 46.0, 47.0, 71.0, 74.0, 84.0, 92.0]", "NOT in [22.0, 28.0, 31.0, 33.0, 47.0, 57.0, 71.0, 74.0, 84.0, 92.0]", "NOT in [22.0, 28.0, 33.0, 47.0, 57.0, 71.0, 74.0, 84.0, 87.0, 92.0]", "NOT in [22.0, 28.0, 33.0, 39.0, 47.0, 57.0, 74.0, 84.0, 87.0, 92.0]", "NOT in [28.0, 33.0, 39.0, 47.0, 57.0, 74.0, 84.0, 87.0, 92.0, 94.0]", "NOT in [28.0, 33.0, 39.0, 43.0, 47.0, 57.0, 74.0, 87.0, 92.0, 94.0]", "NOT in [33.0, 39.0, 43.0, 47.0, 57.0, 74.0, 87.0, 92.0, 94.0]", "NOT in [15.0, 39.0, 43.0, 47.0, 57.0, 74.0, 87.0, 92.0, 94.0]"], "actuals": [30, 75, 10, 38, 53, 23, 90, 94, 21, 69, 12, 64, 46, 31, 71, 22, 84, 28, 33, 74, 92, 47, 57, 87, 39, 94, 43, 92, 15, 6], "method": "Avoid numbers from last 10 draws"}, "pattern_breaking": {"accuracy": 87.5, "predictions": ["NOT 66 (break pattern)", "NOT 68 (break pattern)", "NOT 98 (break pattern)", "NOT 28 (break pattern)", "NOT 16 (break pattern)", "NOT 38 (break pattern)", "NOT 2 (break pattern)", "NOT 67 (break pattern)"], "actuals": [53, 23, 21, 31, 71, 74, 57, 87], "method": "Predict arithmetic pattern breaks"}, "range_rotation": {"accuracy": 43.333333333333336, "predictions": ["Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (34, 66)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)", "Range (0, 33)"], "actuals": [30, 75, 10, 38, 53, 23, 90, 94, 21, 69, 12, 64, 46, 31, 71, 22, 84, 28, 33, 74, 92, 47, 57, 87, 39, 94, 43, 92, 15, 6], "method": "Predict least used range"}, "mean_reversion": {"accuracy": 88.46153846153845, "predictions": ["DOWN toward 56.2", "UP toward 56.4", "DOWN toward 55.6", "UP toward 55.5", "UP toward 53.9", "UP toward 53.2", "DOWN toward 53.2", "DOWN toward 53.6", "UP toward 52.0", "DOWN toward 52.3", "UP toward 50.4", "DOWN toward 52.2", "UP toward 51.9", "DOWN toward 52.6", "UP toward 52.6", "DOWN toward 52.7", "UP toward 51.5", "UP toward 51.5", "DOWN toward 51.0", "DOWN toward 51.7", "DOWN toward 54.5", "UP toward 53.5", "DOWN toward 55.1", "UP toward 54.1", "DOWN toward 56.3", "UP toward 54.4"], "actuals": [30, 75, 10, 38, 53, 90, 94, 21, 69, 12, 64, 46, 71, 22, 84, 28, 33, 74, 92, 47, 39, 94, 43, 92, 15, 6], "method": "Predict reversion to 30-day mean"}, "prime_pattern": {"accuracy": 20.0, "predictions": ["Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number", "Prime number"], "actuals": [30, 75, 10, 38, 53, 23, 90, 94, 21, 69, 12, 64, 46, 31, 71, 22, 84, 28, 33, 74, 92, 47, 57, 87, 39, 94, 43, 92, 15, 6], "method": "Predict prime numbers more likely"}, "random_baseline": {"accuracy": 0.0, "within_5_accuracy": 16.666666666666664, "predictions": [51, 92, 14, 71, 60, 20, 82, 86, 74, 74, 87, 99, 23, 2, 21, 52, 1, 87, 29, 37, 1, 63, 59, 20, 32, 75, 57, 21, 88, 48], "actuals": [30, 75, 10, 38, 53, 23, 90, 94, 21, 69, 12, 64, 46, 31, 71, 22, 84, 28, 33, 74, 92, 47, 57, 87, 39, 94, 43, 92, 15, 6], "method": "Random number generation"}}}}, "overall_performance": {"recent_avoidance": 92.5, "pattern_breaking": 93.54166666666667, "mean_reversion": 75.76923076923077, "range_rotation": 38.333333333333336, "prime_pattern": 25.0, "lcg_formula": 0.8333333333333334, "random_baseline": 0.0}, "best_strategy": ["pattern_breaking", 93.54166666666667], "methodology": ["LCG formula testing", "Recent avoidance strategy", "Pattern breaking strategy", "Range rotation strategy", "Mean reversion strategy", "Prime pattern strategy", "Random baseline comparison"]}