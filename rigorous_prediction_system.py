"""
Rigorous prediction system: Test on 20+ past results, then predict next numbers
"""

import pandas as pd
import numpy as np
from datetime import datetime
import random

def rigorous_prediction_system():
    """Test strategies on past results, then make next predictions"""
    
    print("🎯 RIGOROUS PREDICTION SYSTEM")
    print("=" * 70)
    print("Step 1: Test strategies on 20+ random past results")
    print("Step 2: Calculate actual accuracy")
    print("Step 3: Make next number predictions")
    print("=" * 70)
    
    # Load data
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    print(f"✅ Loaded {len(df)} total records")
    
    # Test each game
    all_accuracies = {}
    next_predictions = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            if len(game_data) >= 50:
                print(f"\n🎲 {game} RIGOROUS TESTING:")
                print("=" * 50)
                
                # Test strategies on random past results
                accuracy_results = test_strategies_on_past_results(game_data, game)
                all_accuracies[game] = accuracy_results
                
                # Make next prediction based on verified accuracy
                next_pred = make_next_prediction(game_data, accuracy_results, game)
                next_predictions[game] = next_pred
    
    # Generate final recommendations
    generate_final_recommendations(all_accuracies, next_predictions)
    
    return next_predictions

def test_strategies_on_past_results(data, game_name):
    """Test all strategies on 25 random past results"""
    
    print(f"📊 Testing strategies on 25 random past results...")
    
    # Select 25 random test points from the data (not just recent)
    if len(data) < 50:
        print(f"   ❌ Insufficient data for testing")
        return {}
    
    # Create test indices (random positions where we have enough history)
    min_history = 30  # Need 30 previous points for analysis
    max_test_idx = len(data) - 1
    possible_indices = list(range(min_history, max_test_idx))
    
    # Randomly select 25 test points
    random.seed(42)  # For reproducible results
    test_indices = random.sample(possible_indices, min(25, len(possible_indices)))
    test_indices.sort()
    
    print(f"   🎯 Testing on {len(test_indices)} random historical points")
    
    # Test each strategy
    strategies = {
        'pattern_breaking': test_pattern_breaking_random,
        'recent_avoidance': test_recent_avoidance_random,
        'mean_reversion': test_mean_reversion_random,
        'range_rotation': test_range_rotation_random
    }
    
    strategy_results = {}
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n   🔍 Testing {strategy_name}...")
        
        correct_predictions = 0
        total_predictions = 0
        detailed_results = []
        
        for test_idx in test_indices:
            # Get historical data up to test point
            historical_data = data.iloc[:test_idx]
            actual_number = int(data.iloc[test_idx])
            
            # Apply strategy
            prediction_result = strategy_func(historical_data, actual_number)
            
            if prediction_result is not None:
                is_correct, prediction_details = prediction_result
                
                if is_correct:
                    correct_predictions += 1
                
                total_predictions += 1
                detailed_results.append({
                    'test_idx': test_idx,
                    'actual': actual_number,
                    'prediction': prediction_details,
                    'correct': is_correct
                })
        
        # Calculate accuracy
        accuracy = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0
        
        strategy_results[strategy_name] = {
            'accuracy': accuracy,
            'correct': correct_predictions,
            'total': total_predictions,
            'details': detailed_results
        }
        
        print(f"      📊 {strategy_name}: {correct_predictions}/{total_predictions} = {accuracy:.1f}%")
    
    return strategy_results

def test_pattern_breaking_random(historical_data, actual_number):
    """Test pattern breaking strategy on a specific historical point"""
    
    if len(historical_data) < 3:
        return None
    
    # Look at last 2 numbers to detect pattern
    last_two = historical_data.tail(2).values
    diff = last_two[1] - last_two[0]
    expected_continuation = last_two[1] + diff
    
    # Only test if continuation would be valid
    if 0 <= expected_continuation <= 99:
        # Strategy: predict pattern will break (actual != expected)
        pattern_broken = abs(actual_number - expected_continuation) > 5
        
        prediction_details = f"Expected {expected_continuation:.0f}, got {actual_number}"
        return pattern_broken, prediction_details
    
    return None

def test_recent_avoidance_random(historical_data, actual_number):
    """Test recent avoidance strategy on a specific historical point"""
    
    if len(historical_data) < 10:
        return None
    
    # Get last 10 numbers
    last_10 = set(historical_data.tail(10))
    
    # Strategy: predict number will NOT be in last 10
    not_in_recent = actual_number not in last_10
    
    prediction_details = f"Last 10: {sorted(last_10)}, got {actual_number}"
    return not_in_recent, prediction_details

def test_mean_reversion_random(historical_data, actual_number):
    """Test mean reversion strategy on a specific historical point"""
    
    if len(historical_data) < 30:
        return None
    
    # Calculate 30-day mean
    recent_30 = historical_data.tail(30)
    rolling_mean = recent_30.mean()
    current_value = historical_data.iloc[-1]
    
    # Only predict when far from mean
    if abs(current_value - rolling_mean) > 10:
        # Predict direction toward mean
        if current_value > rolling_mean:
            # Predict decrease
            correct_direction = actual_number < current_value
            prediction_details = f"Mean {rolling_mean:.1f}, current {current_value}, predicted DOWN, got {actual_number}"
        else:
            # Predict increase
            correct_direction = actual_number > current_value
            prediction_details = f"Mean {rolling_mean:.1f}, current {current_value}, predicted UP, got {actual_number}"
        
        return correct_direction, prediction_details
    
    return None

def test_range_rotation_random(historical_data, actual_number):
    """Test range rotation strategy on a specific historical point"""
    
    if len(historical_data) < 10:
        return None
    
    ranges = [(0, 33), (34, 66), (67, 99)]
    
    # Analyze last 10 numbers
    recent_10 = historical_data.tail(10)
    range_counts = [0, 0, 0]
    
    for value in recent_10:
        for i, (start, end) in enumerate(ranges):
            if start <= value <= end:
                range_counts[i] += 1
                break
    
    # Predict least used range
    least_used_idx = range_counts.index(min(range_counts))
    predicted_range = ranges[least_used_idx]
    
    # Check if actual is in predicted range
    in_predicted_range = predicted_range[0] <= actual_number <= predicted_range[1]
    
    prediction_details = f"Predicted range {predicted_range}, got {actual_number}"
    return in_predicted_range, prediction_details

def make_next_prediction(data, accuracy_results, game_name):
    """Make next number prediction based on verified strategy accuracy"""
    
    print(f"\n🔮 MAKING NEXT PREDICTION FOR {game_name}:")
    print("-" * 40)
    
    # Rank strategies by accuracy
    sorted_strategies = sorted(accuracy_results.items(), 
                              key=lambda x: x[1]['accuracy'], 
                              reverse=True)
    
    print(f"📊 Strategy accuracy ranking:")
    for strategy, results in sorted_strategies:
        accuracy = results['accuracy']
        print(f"   {strategy}: {accuracy:.1f}%")
    
    # Use best performing strategies
    predictions = {}
    
    # Get current state for predictions
    recent_data = data.tail(30)
    
    for strategy_name, strategy_results in sorted_strategies:
        if strategy_results['accuracy'] > 50:  # Only use strategies >50% accurate
            
            if strategy_name == 'pattern_breaking':
                pred = predict_pattern_breaking(recent_data)
            elif strategy_name == 'recent_avoidance':
                pred = predict_recent_avoidance(recent_data)
            elif strategy_name == 'mean_reversion':
                pred = predict_mean_reversion(recent_data)
            elif strategy_name == 'range_rotation':
                pred = predict_range_rotation(recent_data)
            else:
                pred = None
            
            if pred:
                predictions[strategy_name] = {
                    'prediction': pred,
                    'accuracy': strategy_results['accuracy'],
                    'confidence': 'HIGH' if strategy_results['accuracy'] > 80 else 'MEDIUM'
                }
    
    # Generate final prediction
    if predictions:
        # Use highest accuracy strategy
        best_strategy = max(predictions.keys(), key=lambda x: predictions[x]['accuracy'])
        best_prediction = predictions[best_strategy]
        
        final_prediction = {
            'primary_strategy': best_strategy,
            'primary_prediction': best_prediction['prediction'],
            'primary_accuracy': best_prediction['accuracy'],
            'all_predictions': predictions,
            'confidence_level': best_prediction['confidence']
        }
        
        print(f"\n🎯 FINAL PREDICTION:")
        print(f"   Strategy: {best_strategy}")
        print(f"   Prediction: {best_prediction['prediction']}")
        print(f"   Verified accuracy: {best_prediction['accuracy']:.1f}%")
        print(f"   Confidence: {best_prediction['confidence']}")
        
        return final_prediction
    
    else:
        print(f"   ❌ No strategies achieved >50% accuracy")
        return None

def predict_pattern_breaking(recent_data):
    """Generate pattern breaking prediction"""
    
    if len(recent_data) < 3:
        return None
    
    # Check for patterns in last few numbers
    last_three = recent_data.tail(3).values
    
    # Check for arithmetic progression
    diff1 = last_three[1] - last_three[0]
    diff2 = last_three[2] - last_three[1]
    
    if diff1 == diff2 and diff1 != 0:
        expected_next = last_three[2] + diff1
        if 0 <= expected_next <= 99:
            # Predict pattern will break - avoid expected number and nearby
            avoid_numbers = [expected_next]
            if expected_next > 0:
                avoid_numbers.append(expected_next - 1)
            if expected_next < 99:
                avoid_numbers.append(expected_next + 1)
            
            return f"AVOID {avoid_numbers} (pattern break)"
    
    # Check for simple repetition
    if last_three[1] == last_three[2]:
        return f"AVOID {int(last_three[2])} (repetition break)"
    
    return "No clear pattern to break"

def predict_recent_avoidance(recent_data):
    """Generate recent avoidance prediction"""
    
    if len(recent_data) < 10:
        return None
    
    last_10 = set(recent_data.tail(10))
    available_numbers = [i for i in range(100) if i not in last_10]
    
    return f"TARGET {len(available_numbers)} numbers: AVOID {sorted(last_10)}"

def predict_mean_reversion(recent_data):
    """Generate mean reversion prediction"""
    
    if len(recent_data) < 30:
        return None
    
    rolling_mean = recent_data.tail(30).mean()
    current_value = recent_data.iloc[-1]
    deviation = current_value - rolling_mean
    
    if abs(deviation) > 10:
        if deviation > 0:
            return f"PREDICT DECREASE (current {current_value:.0f} > mean {rolling_mean:.1f})"
        else:
            return f"PREDICT INCREASE (current {current_value:.0f} < mean {rolling_mean:.1f})"
    
    return f"Near mean ({rolling_mean:.1f}) - no strong prediction"

def predict_range_rotation(recent_data):
    """Generate range rotation prediction"""
    
    if len(recent_data) < 10:
        return None
    
    ranges = [(0, 33), (34, 66), (67, 99)]
    range_names = ['LOW (0-33)', 'MID (34-66)', 'HIGH (67-99)']
    
    # Count recent usage
    recent_10 = recent_data.tail(10)
    range_counts = [0, 0, 0]
    
    for value in recent_10:
        for i, (start, end) in enumerate(ranges):
            if start <= value <= end:
                range_counts[i] += 1
                break
    
    # Find least used range
    least_used_idx = range_counts.index(min(range_counts))
    
    return f"TARGET {range_names[least_used_idx]} (used {range_counts[least_used_idx]}/10 times)"

def generate_final_recommendations(all_accuracies, next_predictions):
    """Generate final recommendations based on all results"""
    
    print(f"\n🎯 FINAL RECOMMENDATIONS")
    print("=" * 60)
    
    # Calculate overall best strategies
    strategy_totals = {}
    
    for game, accuracies in all_accuracies.items():
        for strategy, results in accuracies.items():
            if strategy not in strategy_totals:
                strategy_totals[strategy] = []
            strategy_totals[strategy].append(results['accuracy'])
    
    # Calculate average accuracies
    avg_accuracies = {}
    for strategy, accuracies in strategy_totals.items():
        avg_accuracies[strategy] = np.mean(accuracies)
    
    # Sort by average accuracy
    sorted_avg = sorted(avg_accuracies.items(), key=lambda x: x[1], reverse=True)
    
    print(f"📊 VERIFIED STRATEGY PERFORMANCE (25 random tests each):")
    for strategy, avg_accuracy in sorted_avg:
        if avg_accuracy >= 70:
            status = "🚨 HIGHLY EXPLOITABLE"
        elif avg_accuracy >= 50:
            status = "⚡ EXPLOITABLE"
        elif avg_accuracy >= 30:
            status = "💡 MARGINAL"
        else:
            status = "❌ INEFFECTIVE"
        
        print(f"   {strategy}: {avg_accuracy:.1f}% {status}")
    
    # Game-specific recommendations
    print(f"\n🎲 GAME-SPECIFIC PREDICTIONS:")
    
    for game, prediction in next_predictions.items():
        if prediction:
            accuracy = prediction['primary_accuracy']
            strategy = prediction['primary_strategy']
            pred_text = prediction['primary_prediction']
            confidence = prediction['confidence_level']
            
            if accuracy >= 70:
                recommendation = "🚨 STRONG BET"
                stake = "2% max"
            elif accuracy >= 50:
                recommendation = "⚡ MODERATE BET"
                stake = "1% max"
            else:
                recommendation = "💡 PAPER TRADE"
                stake = "0%"
            
            print(f"\n   🎯 {game}:")
            print(f"      Strategy: {strategy}")
            print(f"      Prediction: {pred_text}")
            print(f"      Verified accuracy: {accuracy:.1f}%")
            print(f"      Confidence: {confidence}")
            print(f"      Recommendation: {recommendation} ({stake})")
        else:
            print(f"\n   ❌ {game}: No reliable prediction available")
    
    # Overall recommendation
    best_game = None
    best_accuracy = 0
    
    for game, prediction in next_predictions.items():
        if prediction and prediction['primary_accuracy'] > best_accuracy:
            best_accuracy = prediction['primary_accuracy']
            best_game = game
    
    print(f"\n💰 OVERALL RECOMMENDATION:")
    
    if best_accuracy >= 70:
        print(f"   🎯 BEST OPPORTUNITY: {best_game}")
        print(f"   📊 Verified accuracy: {best_accuracy:.1f}%")
        print(f"   💰 Recommended action: PLACE SMALL BET (1-2% max)")
        print(f"   ⚠️ Risk level: MODERATE (still gambling)")
    elif best_accuracy >= 50:
        print(f"   ⚠️ MARGINAL OPPORTUNITY: {best_game}")
        print(f"   📊 Verified accuracy: {best_accuracy:.1f}%")
        print(f"   💰 Recommended action: PAPER TRADE ONLY")
    else:
        print(f"   ❌ NO RELIABLE OPPORTUNITIES")
        print(f"   📊 Best accuracy: {best_accuracy:.1f}%")
        print(f"   💰 Recommended action: DO NOT BET")
    
    print(f"\n⚠️ CRITICAL REMINDERS:")
    print(f"   • These accuracies are based on 25 random historical tests")
    print(f"   • Past performance does not guarantee future results")
    print(f"   • Algorithms could change without notice")
    print(f"   • Never bet more than you can afford to lose")
    print(f"   • Track actual results vs predictions")

if __name__ == "__main__":
    rigorous_prediction_system()
