"""
Advanced API Investigation Tool for Satta King Websites
Investigates how multiple satta sites get their data and detect shared APIs
"""

import requests
import json
import time
import re
import os
from datetime import datetime
from urllib.parse import urlparse, urljoin
import threading
from concurrent.futures import ThreadPoolExecutor

class SattaAPIInvestigator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.sites = [
            "https://satta-king-fast.com",
            "https://sattaking.com", 
            "https://sattakinglive.com",
            "https://sattaresult.net",
            "https://sattamatka.com",
            "https://sattaking143.com",
            "https://sattakinggali.com",
            "https://sattakingdesawar.com"
        ]
        
        self.results = {}
        
    def investigate_all_sites(self):
        """Main investigation function"""
        print("🕵️ SATTA KING API INVESTIGATION")
        print("=" * 60)
        print(f"Investigating {len(self.sites)} sites for shared APIs and data sources...")
        
        # Phase 1: Basic site analysis
        print("\n📊 PHASE 1: BASIC SITE ANALYSIS")
        print("-" * 40)
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {executor.submit(self.analyze_site, site): site for site in self.sites}
            
            for future in futures:
                site = futures[future]
                try:
                    result = future.result(timeout=30)
                    self.results[site] = result
                except Exception as e:
                    print(f"❌ {site}: {str(e)}")
                    self.results[site] = {'error': str(e), 'accessible': False}
        
        # Phase 2: Cross-site comparison
        print("\n🔍 PHASE 2: CROSS-SITE COMPARISON")
        print("-" * 40)
        
        comparison = self.compare_sites()
        
        # Phase 3: API endpoint discovery
        print("\n🌐 PHASE 3: API ENDPOINT DISCOVERY")
        print("-" * 40)
        
        api_analysis = self.discover_apis()
        
        # Phase 4: Network pattern analysis
        print("\n📡 PHASE 4: NETWORK PATTERN ANALYSIS")
        print("-" * 40)
        
        network_analysis = self.analyze_network_patterns()
        
        # Generate comprehensive report
        report = self.generate_comprehensive_report(comparison, api_analysis, network_analysis)
        
        return report
    
    def analyze_site(self, site_url):
        """Analyze individual site for API patterns"""
        print(f"🔍 Analyzing: {site_url}")
        
        try:
            response = self.session.get(site_url, timeout=15)
            
            analysis = {
                'url': site_url,
                'status_code': response.status_code,
                'accessible': True,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.content),
                'headers': dict(response.headers),
                'results_data': {},
                'api_endpoints': [],
                'external_requests': [],
                'javascript_apis': [],
                'hidden_forms': [],
                'ajax_patterns': []
            }
            
            # Parse HTML content
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract current results
            analysis['results_data'] = self.extract_results(soup)
            
            # Find API endpoints in JavaScript
            analysis['javascript_apis'] = self.find_js_apis(soup)
            
            # Find AJAX patterns
            analysis['ajax_patterns'] = self.find_ajax_patterns(soup)
            
            # Find external requests
            analysis['external_requests'] = self.find_external_requests(soup)
            
            # Find hidden forms (potential API submission points)
            analysis['hidden_forms'] = self.find_hidden_forms(soup)
            
            # Test for common API endpoints
            analysis['api_endpoints'] = self.test_api_endpoints(site_url)
            
            print(f"   ✅ Results: {len(analysis['results_data'])}, APIs: {len(analysis['javascript_apis'])}")
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            return {'error': str(e), 'accessible': False}
    
    def extract_results(self, soup):
        """Extract current satta results from HTML"""
        results = {}
        
        # Common game patterns
        games = ['DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD', 'GALI']
        
        # Look in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    text = ' '.join([cell.get_text() for cell in cells])
                    
                    # Extract game and result patterns
                    for game in games:
                        pattern = rf'{game}[:\s]*(\d{{1,2}})'
                        match = re.search(pattern, text, re.IGNORECASE)
                        if match:
                            results[game] = match.group(1)
        
        # Look in divs and spans
        for element in soup.find_all(['div', 'span']):
            text = element.get_text()
            for game in games:
                pattern = rf'{game}[:\s]*(\d{{1,2}})'
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    results[game] = match.group(1)
        
        return results
    
    def find_js_apis(self, soup):
        """Find API endpoints mentioned in JavaScript"""
        apis = []
        
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Look for URL patterns
                url_patterns = [
                    r'https?://[^\s"\']+(?:api|data|result|update)[^\s"\']*',
                    r'["\'](?:api|data|result|update)/[^"\']*["\']',
                    r'fetch\s*\(\s*["\']([^"\']+)["\']',
                    r'ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in url_patterns:
                    matches = re.findall(pattern, script.string, re.IGNORECASE)
                    apis.extend(matches)
        
        return list(set(apis))  # Remove duplicates
    
    def find_ajax_patterns(self, soup):
        """Find AJAX call patterns in JavaScript"""
        patterns = []
        
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Look for AJAX patterns
                ajax_indicators = [
                    'XMLHttpRequest',
                    'fetch(',
                    '$.ajax',
                    '$.get',
                    '$.post',
                    'axios.',
                    'setInterval',
                    'setTimeout'
                ]
                
                for indicator in ajax_indicators:
                    if indicator in script.string:
                        patterns.append(indicator)
        
        return list(set(patterns))
    
    def find_external_requests(self, soup):
        """Find external resource requests"""
        external = []
        
        # Check scripts, links, images
        for tag in soup.find_all(['script', 'link', 'img']):
            src = tag.get('src') or tag.get('href')
            if src and src.startswith('http'):
                domain = urlparse(src).netloc
                external.append(domain)
        
        return list(set(external))
    
    def find_hidden_forms(self, soup):
        """Find hidden forms that might submit to APIs"""
        forms = []
        
        for form in soup.find_all('form'):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            
            hidden_inputs = form.find_all('input', type='hidden')
            
            if hidden_inputs or 'api' in action.lower():
                forms.append({
                    'action': action,
                    'method': method,
                    'hidden_inputs': len(hidden_inputs)
                })
        
        return forms
    
    def test_api_endpoints(self, base_url):
        """Test common API endpoint patterns"""
        endpoints = []
        
        common_paths = [
            '/api/results',
            '/api/data',
            '/api/latest',
            '/data.php',
            '/result.php',
            '/api.php',
            '/update.php',
            '/live.php',
            '/json/results',
            '/ajax/data'
        ]
        
        for path in common_paths:
            try:
                url = urljoin(base_url, path)
                response = self.session.head(url, timeout=5)
                
                if response.status_code in [200, 301, 302]:
                    endpoints.append({
                        'url': url,
                        'status': response.status_code,
                        'content_type': response.headers.get('content-type', '')
                    })
                    
            except:
                continue
                
            time.sleep(0.2)  # Rate limiting
        
        return endpoints
    
    def compare_sites(self):
        """Compare results and patterns across sites"""
        accessible_sites = {url: data for url, data in self.results.items() 
                           if data.get('accessible', False)}
        
        comparison = {
            'identical_results': {},
            'timing_analysis': {},
            'shared_patterns': [],
            'api_overlap': {}
        }
        
        # Compare results
        all_games = set()
        for site_data in accessible_sites.values():
            all_games.update(site_data.get('results_data', {}).keys())
        
        for game in all_games:
            game_results = {}
            for site, data in accessible_sites.items():
                result = data.get('results_data', {}).get(game)
                if result:
                    game_results[site] = result
            
            if len(game_results) > 1:
                unique_results = set(game_results.values())
                if len(unique_results) == 1:
                    comparison['identical_results'][game] = {
                        'result': list(unique_results)[0],
                        'sites': list(game_results.keys()),
                        'match_count': len(game_results)
                    }
        
        print(f"   📊 Found {len(comparison['identical_results'])} games with identical results")
        
        return comparison
    
    def discover_apis(self):
        """Discover and analyze API endpoints"""
        all_apis = []
        api_domains = {}
        
        for site, data in self.results.items():
            if data.get('accessible', False):
                apis = data.get('javascript_apis', []) + [ep['url'] for ep in data.get('api_endpoints', [])]
                all_apis.extend(apis)
                
                for api in apis:
                    try:
                        domain = urlparse(api).netloc
                        if domain:
                            if domain not in api_domains:
                                api_domains[domain] = []
                            api_domains[domain].append(site)
                    except:
                        continue
        
        # Find shared API domains
        shared_domains = {domain: sites for domain, sites in api_domains.items() if len(sites) > 1}
        
        print(f"   🌐 Found {len(shared_domains)} shared API domains")
        
        return {
            'all_apis': list(set(all_apis)),
            'shared_domains': shared_domains,
            'total_unique_apis': len(set(all_apis))
        }
    
    def analyze_network_patterns(self):
        """Analyze network request patterns"""
        patterns = {
            'common_external_domains': {},
            'ajax_usage': {},
            'update_mechanisms': []
        }
        
        # Analyze external domains
        all_externals = []
        for site, data in self.results.items():
            if data.get('accessible', False):
                externals = data.get('external_requests', [])
                all_externals.extend(externals)
        
        from collections import Counter
        domain_counts = Counter(all_externals)
        patterns['common_external_domains'] = {domain: count for domain, count in domain_counts.items() if count > 1}
        
        # Analyze AJAX usage
        for site, data in self.results.items():
            if data.get('accessible', False):
                ajax_patterns = data.get('ajax_patterns', [])
                patterns['ajax_usage'][site] = ajax_patterns
        
        print(f"   📡 Found {len(patterns['common_external_domains'])} common external domains")
        
        return patterns
    
    def generate_comprehensive_report(self, comparison, api_analysis, network_analysis):
        """Generate final comprehensive report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'investigation_summary': {
                'sites_analyzed': len(self.sites),
                'accessible_sites': len([r for r in self.results.values() if r.get('accessible', False)]),
                'identical_results_found': len(comparison.get('identical_results', {})),
                'shared_api_domains': len(api_analysis.get('shared_domains', {})),
                'common_external_domains': len(network_analysis.get('common_external_domains', {}))
            },
            'site_results': self.results,
            'comparison_analysis': comparison,
            'api_analysis': api_analysis,
            'network_analysis': network_analysis,
            'conclusions': self.generate_conclusions(comparison, api_analysis, network_analysis)
        }
        
        # Save report
        os.makedirs('data', exist_ok=True)
        report_file = f'data/comprehensive_api_investigation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 Comprehensive report saved to: {report_file}")
        
        # Print summary
        self.print_summary(report)
        
        return report
    
    def generate_conclusions(self, comparison, api_analysis, network_analysis):
        """Generate investigation conclusions"""
        
        conclusions = {
            'shared_data_source': False,
            'api_sharing_detected': False,
            'evidence_strength': 'WEAK',
            'key_findings': [],
            'technical_evidence': [],
            'recommendations': []
        }
        
        # Analyze identical results
        identical_count = len(comparison.get('identical_results', {}))
        if identical_count > 0:
            conclusions['shared_data_source'] = True
            conclusions['key_findings'].append(f"Found {identical_count} games with identical results across multiple sites")
            
            if identical_count >= 3:
                conclusions['evidence_strength'] = 'STRONG'
            elif identical_count >= 2:
                conclusions['evidence_strength'] = 'MODERATE'
        
        # Analyze shared APIs
        shared_domains = api_analysis.get('shared_domains', {})
        if shared_domains:
            conclusions['api_sharing_detected'] = True
            conclusions['technical_evidence'].append(f"Detected {len(shared_domains)} shared API domains")
            
            for domain, sites in shared_domains.items():
                conclusions['technical_evidence'].append(f"Domain '{domain}' used by {len(sites)} sites")
        
        # Analyze network patterns
        common_externals = network_analysis.get('common_external_domains', {})
        if common_externals:
            conclusions['technical_evidence'].append(f"Found {len(common_externals)} common external domains")
        
        # Generate recommendations
        if conclusions['shared_data_source']:
            conclusions['recommendations'].append("Monitor result update timing across sites to confirm synchronization")
            conclusions['recommendations'].append("Investigate the central data source or API provider")
        
        if conclusions['api_sharing_detected']:
            conclusions['recommendations'].append("Reverse engineer the shared API endpoints")
            conclusions['recommendations'].append("Check if APIs are publicly accessible or require authentication")
        
        conclusions['recommendations'].append("Set up continuous monitoring to track data flow patterns")
        conclusions['recommendations'].append("Analyze server response headers for infrastructure clues")
        
        return conclusions
    
    def print_summary(self, report):
        """Print investigation summary"""
        
        print("\n📋 INVESTIGATION SUMMARY")
        print("=" * 60)
        
        summary = report['investigation_summary']
        conclusions = report['conclusions']
        
        print(f"Sites Analyzed: {summary['sites_analyzed']}")
        print(f"Accessible Sites: {summary['accessible_sites']}")
        print(f"Identical Results Found: {summary['identical_results_found']}")
        print(f"Shared API Domains: {summary['shared_api_domains']}")
        print(f"Common External Domains: {summary['common_external_domains']}")
        
        print(f"\nShared Data Source: {'YES' if conclusions['shared_data_source'] else 'NO'}")
        print(f"API Sharing Detected: {'YES' if conclusions['api_sharing_detected'] else 'NO'}")
        print(f"Evidence Strength: {conclusions['evidence_strength']}")
        
        print("\nKey Findings:")
        for finding in conclusions['key_findings']:
            print(f"  • {finding}")
        
        print("\nTechnical Evidence:")
        for evidence in conclusions['technical_evidence']:
            print(f"  • {evidence}")

if __name__ == "__main__":
    investigator = SattaAPIInvestigator()
    report = investigator.investigate_all_sites()
