"""
ML Training system using existing data with backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

import config

class MLTrainer:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.results = {}
        
    def load_existing_data(self):
        """Load existing scraped data"""
        print("📊 Loading existing data...")
        
        # Try to load from existing files
        data_files = []
        if os.path.exists(config.RAW_DATA_DIR):
            for file in os.listdir(config.RAW_DATA_DIR):
                if file.endswith('.csv'):
                    data_files.append(os.path.join(config.RAW_DATA_DIR, file))
        
        if not data_files:
            print("❌ No existing data files found")
            return None
        
        # Load the most recent file
        latest_file = max(data_files, key=os.path.getmtime)
        print(f"Loading: {latest_file}")
        
        df = pd.read_csv(latest_file)
        
        # Clean data
        try:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        except:
            print("❌ Error parsing dates")
            return None
        
        df = df.dropna(subset=['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # Convert game columns to numeric
        for game in config.GAMES:
            if game in df.columns:
                df[game] = pd.to_numeric(df[game], errors='coerce')
        
        print(f"✅ Loaded {len(df)} records from {df['date'].min()} to {df['date'].max()}")
        return df
    
    def create_features(self, df, target_game, lookback=30):
        """Create features for ML training"""
        print(f"🔧 Creating features for {target_game}...")
        
        if target_game not in df.columns:
            return None, None
        
        # Create feature dataframe
        features_df = df.copy()
        
        # Time features
        features_df['day_of_week'] = features_df['date'].dt.dayofweek
        features_df['day_of_month'] = features_df['date'].dt.day
        features_df['month'] = features_df['date'].dt.month
        
        # Lag features
        for lag in [1, 2, 3, 7]:
            features_df[f'lag_{lag}'] = features_df[target_game].shift(lag)
        
        # Rolling statistics
        for window in [3, 7, 14]:
            features_df[f'mean_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).mean()
            features_df[f'std_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).std()
            features_df[f'min_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).min()
            features_df[f'max_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).max()
        
        # Difference features
        features_df['diff_1'] = features_df[target_game].diff(1)
        features_df['diff_7'] = features_df[target_game].diff(7)
        
        # Pattern features
        features_df['is_even'] = (features_df[target_game] % 2 == 0).astype(int)
        features_df['digit_sum'] = features_df[target_game].apply(
            lambda x: sum(int(d) for d in str(int(x))) if pd.notna(x) else np.nan
        )
        
        # Cross-game features
        other_games = [g for g in config.GAMES if g != target_game and g in features_df.columns]
        for other_game in other_games:
            features_df[f'corr_{other_game}'] = features_df[target_game].rolling(window=14, min_periods=5).corr(
                features_df[other_game]
            )
        
        # Select feature columns
        feature_cols = [col for col in features_df.columns 
                       if col not in ['date', target_game] and pd.api.types.is_numeric_dtype(features_df[col])]
        
        X = features_df[feature_cols].copy()
        y = features_df[target_game].copy()
        
        # Remove rows with missing target
        mask = ~y.isna()
        X = X[mask]
        y = y[mask]
        dates = features_df.loc[mask, 'date']
        
        # Fill missing features
        X = X.fillna(X.median())
        
        # Keep only recent data for training
        if len(X) > lookback:
            X = X.tail(lookback)
            y = y.tail(lookback)
            dates = dates.tail(lookback)
        
        print(f"✅ Created {X.shape[1]} features, {len(X)} samples")
        return X, y, dates
    
    def train_and_test(self, X, y, dates, game_name):
        """Train models with time series split and backtesting"""
        print(f"🤖 Training models for {game_name}...")
        
        if len(X) < 20:
            print(f"❌ Insufficient data: {len(X)} samples")
            return None
        
        # Time series split (use 70% for training, 30% for testing)
        split_idx = int(len(X) * 0.7)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        dates_test = dates.iloc[split_idx:]
        
        print(f"   Training: {len(X_train)} samples")
        print(f"   Testing: {len(X_test)} samples")
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Define models
        models = {
            'random_forest': RandomForestRegressor(n_estimators=50, max_depth=8, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=50, max_depth=4, random_state=42),
            'ridge': Ridge(alpha=1.0)
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"   Training {name}...")
            
            try:
                if name == 'ridge':
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                
                # Clip predictions to valid range
                y_pred = np.clip(y_pred, 0, 99)
                
                # Calculate metrics
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                
                # Calculate accuracy within ranges
                accuracy_5 = np.mean(np.abs(y_test - y_pred) <= 5) * 100
                accuracy_10 = np.mean(np.abs(y_test - y_pred) <= 10) * 100
                accuracy_15 = np.mean(np.abs(y_test - y_pred) <= 15) * 100
                
                # Directional accuracy
                if len(y_test) > 1:
                    y_test_diff = np.diff(y_test)
                    y_pred_diff = np.diff(y_pred)
                    directional_acc = np.mean(np.sign(y_test_diff) == np.sign(y_pred_diff)) * 100
                else:
                    directional_acc = 0
                
                results[name] = {
                    'model': model,
                    'mae': mae,
                    'rmse': rmse,
                    'accuracy_5': accuracy_5,
                    'accuracy_10': accuracy_10,
                    'accuracy_15': accuracy_15,
                    'directional_accuracy': directional_acc,
                    'predictions': y_pred.tolist(),
                    'actual': y_test.tolist(),
                    'dates': dates_test.tolist()
                }
                
                print(f"     MAE: {mae:.2f}, RMSE: {rmse:.2f}")
                print(f"     Accuracy ±5: {accuracy_5:.1f}%, ±10: {accuracy_10:.1f}%, ±15: {accuracy_15:.1f}%")
                print(f"     Directional: {directional_acc:.1f}%")
                
            except Exception as e:
                print(f"     ❌ Error: {str(e)}")
                continue
        
        if results:
            # Select best model based on accuracy_10
            best_name = max(results.keys(), key=lambda x: results[x]['accuracy_10'])
            best_model = results[best_name]
            
            self.models[game_name] = {
                'best_model': best_model['model'],
                'best_name': best_name,
                'scaler': scaler if best_name == 'ridge' else None,
                'feature_names': X.columns.tolist()
            }
            
            self.results[game_name] = results
            
            print(f"   ✅ Best model: {best_name} (Accuracy ±10: {best_model['accuracy_10']:.1f}%)")
            return results
        
        return None
    
    def predict_next(self, X, game_name):
        """Predict next value"""
        if game_name not in self.models:
            return None
        
        model_info = self.models[game_name]
        model = model_info['best_model']
        scaler = model_info['scaler']
        
        # Use last row for prediction
        last_features = X.iloc[-1:].copy()
        
        if scaler:
            last_features_scaled = scaler.transform(last_features)
            prediction = model.predict(last_features_scaled)[0]
        else:
            prediction = model.predict(last_features)[0]
        
        prediction = np.clip(prediction, 0, 99)
        return int(round(prediction))
    
    def generate_backtest_report(self, game_name):
        """Generate detailed backtest report"""
        if game_name not in self.results:
            return None
        
        results = self.results[game_name]
        best_name = max(results.keys(), key=lambda x: results[x]['accuracy_10'])
        best_result = results[best_name]
        
        report = {
            'game': game_name,
            'best_model': best_name,
            'metrics': {
                'mae': best_result['mae'],
                'rmse': best_result['rmse'],
                'accuracy_5': best_result['accuracy_5'],
                'accuracy_10': best_result['accuracy_10'],
                'accuracy_15': best_result['accuracy_15'],
                'directional_accuracy': best_result['directional_accuracy']
            },
            'predictions_vs_actual': [
                {
                    'date': str(date),
                    'actual': int(actual),
                    'predicted': int(pred),
                    'error': int(actual - pred)
                }
                for date, actual, pred in zip(
                    best_result['dates'],
                    best_result['actual'],
                    best_result['predictions']
                )
            ]
        }
        
        return report

def main():
    """Main training pipeline"""
    print("🚀 ML TRAINING SYSTEM WITH BACKTESTING")
    print("=" * 60)
    
    trainer = MLTrainer()
    
    # Load existing data
    df = trainer.load_existing_data()
    if df is None:
        print("❌ No data available for training")
        return
    
    print(f"\n🤖 TRAINING MODELS")
    print("-" * 30)
    
    all_predictions = {}
    all_reports = {}
    
    for game in config.GAMES:
        if game in df.columns:
            game_data = df[game].dropna()
            if len(game_data) >= 20:
                print(f"\n🎯 Training {config.GAME_NAMES.get(game, game)}...")
                
                # Create features
                X, y, dates = trainer.create_features(df, game)
                
                if X is not None:
                    # Train and test
                    results = trainer.train_and_test(X, y, dates, game)
                    
                    if results:
                        # Make prediction
                        prediction = trainer.predict_next(X, game)
                        if prediction is not None:
                            all_predictions[game] = {
                                'game_name': config.GAME_NAMES.get(game, game),
                                'prediction': prediction,
                                'accuracy': max(r['accuracy_10'] for r in results.values()),
                                'model': trainer.models[game]['best_name']
                            }
                        
                        # Generate backtest report
                        report = trainer.generate_backtest_report(game)
                        if report:
                            all_reports[game] = report
            else:
                print(f"❌ Insufficient data for {game}: {len(game_data)} records")
    
    # Display results
    print(f"\n🔮 ML PREDICTIONS")
    print("=" * 40)
    
    if all_predictions:
        for game, pred_info in all_predictions.items():
            accuracy = pred_info['accuracy']
            confidence_emoji = "🟢" if accuracy > 50 else "🟡" if accuracy > 30 else "🔴"
            print(f"{confidence_emoji} {pred_info['game_name']}: {pred_info['prediction']:02d} "
                  f"(Accuracy: {accuracy:.1f}%, Model: {pred_info['model']})")
        
        # Save comprehensive report
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'model_type': 'ML with Backtesting',
            'predictions': all_predictions,
            'backtest_reports': all_reports,
            'summary': {
                'total_games': len(all_predictions),
                'avg_accuracy': np.mean([p['accuracy'] for p in all_predictions.values()]),
                'data_source': 'Historical scraped data'
            }
        }
        
        os.makedirs(config.DATA_DIR, exist_ok=True)
        report_file = os.path.join(config.DATA_DIR, f'ml_backtest_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(report_file, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        # Show backtest summary
        print(f"\n📊 BACKTEST SUMMARY")
        print("-" * 30)
        for game, report in all_reports.items():
            game_name = config.GAME_NAMES.get(game, game)
            metrics = report['metrics']
            print(f"\n{game_name}:")
            print(f"  Model: {report['best_model']}")
            print(f"  MAE: {metrics['mae']:.2f}")
            print(f"  Accuracy ±10: {metrics['accuracy_10']:.1f}%")
            print(f"  Directional: {metrics['directional_accuracy']:.1f}%")
            
            # Show last few predictions
            recent_preds = report['predictions_vs_actual'][-5:]
            print(f"  Recent predictions:")
            for pred in recent_preds:
                error_sign = "✓" if abs(pred['error']) <= 10 else "✗"
                date_str = pred['date'][:10] if isinstance(pred['date'], str) else str(pred['date'])[:10]
                print(f"    {date_str}: {pred['actual']:02d} vs {pred['predicted']:02d} {error_sign}")
    
    else:
        print("❌ No predictions generated")

if __name__ == "__main__":
    main()
