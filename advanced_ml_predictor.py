"""
Advanced ML-based prediction system with backtesting and accuracy measurement
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.svm import SVR
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, accuracy_score
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

from scraper import SattaKingScraper
import config

class AdvancedMLPredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_names = {}
        self.accuracy_scores = {}
        
    def scrape_extensive_data(self, start_year=2022, end_year=2025):
        """Scrape extensive historical data for training"""
        print(f"🔄 Scraping extensive data from {start_year} to {end_year}...")
        
        scraper = SattaKingScraper()
        all_data = []
        
        for year in range(start_year, end_year + 1):
            for month in range(1, 13):
                # Skip future months
                current_date = datetime.now()
                if year == current_date.year and month > current_date.month:
                    continue
                
                month_names = {
                    1: 'January', 2: 'February', 3: 'March', 4: 'April',
                    5: 'May', 6: 'June', 7: 'July', 8: 'August',
                    9: 'September', 10: 'October', 11: 'November', 12: 'December'
                }
                
                month_name = month_names[month]
                print(f"  Scraping {month_name} {year}...")
                
                data = scraper.get_month_data(month_name, year)
                if data:
                    all_data.extend(data)
                    print(f"    ✓ Found {len(data)} records")
                else:
                    print(f"    ✗ No data found")
        
        if all_data:
            df = pd.DataFrame(all_data)

            # Clean date parsing
            try:
                df['date'] = pd.to_datetime(df['date'], format='mixed', errors='coerce')
            except:
                df['date'] = pd.to_datetime(df['date'], errors='coerce')

            # Remove invalid dates
            df = df.dropna(subset=['date'])
            df = df.sort_values('date').reset_index(drop=True)

            # Clean and convert data
            for game in config.GAMES:
                if game in df.columns:
                    df[game] = pd.to_numeric(df[game], errors='coerce')

            # Remove duplicates and invalid data
            df = df.drop_duplicates(subset=['date'], keep='first')
            
            print(f"✅ Total scraped: {len(df)} records from {df['date'].min()} to {df['date'].max()}")
            
            # Save raw data
            os.makedirs(config.RAW_DATA_DIR, exist_ok=True)
            filename = f'extensive_data_{start_year}_{end_year}.csv'
            filepath = os.path.join(config.RAW_DATA_DIR, filename)
            df.to_csv(filepath, index=False)
            print(f"💾 Saved to: {filepath}")
            
            return df
        else:
            print("❌ No data could be scraped")
            return None
    
    def create_advanced_features(self, df, target_game):
        """Create comprehensive features for ML training"""
        print(f"🔧 Creating advanced features for {target_game}...")
        
        # Ensure we have the target game data
        if target_game not in df.columns:
            print(f"❌ {target_game} not found in data")
            return None, None
        
        # Create a copy for feature engineering
        features_df = df.copy()
        
        # Sort by date
        features_df = features_df.sort_values('date').reset_index(drop=True)
        
        # Time-based features
        features_df['day_of_week'] = features_df['date'].dt.dayofweek
        features_df['day_of_month'] = features_df['date'].dt.day
        features_df['month'] = features_df['date'].dt.month
        features_df['quarter'] = features_df['date'].dt.quarter
        features_df['day_of_year'] = features_df['date'].dt.dayofyear
        features_df['week_of_year'] = features_df['date'].dt.isocalendar().week
        
        # Target variable
        target_col = target_game
        target_data = features_df[target_col].dropna()
        
        if len(target_data) < 50:
            print(f"❌ Insufficient data for {target_game}: {len(target_data)} records")
            return None, None
        
        # Lag features (previous values)
        for lag in [1, 2, 3, 7, 14, 30]:
            features_df[f'{target_game}_lag_{lag}'] = features_df[target_col].shift(lag)
        
        # Rolling statistics
        for window in [3, 7, 14, 30]:
            features_df[f'{target_game}_mean_{window}'] = features_df[target_col].rolling(window=window, min_periods=1).mean()
            features_df[f'{target_game}_std_{window}'] = features_df[target_col].rolling(window=window, min_periods=1).std()
            features_df[f'{target_game}_min_{window}'] = features_df[target_col].rolling(window=window, min_periods=1).min()
            features_df[f'{target_game}_max_{window}'] = features_df[target_col].rolling(window=window, min_periods=1).max()
            features_df[f'{target_game}_median_{window}'] = features_df[target_col].rolling(window=window, min_periods=1).median()
        
        # Difference features
        features_df[f'{target_game}_diff_1'] = features_df[target_col].diff(1)
        features_df[f'{target_game}_diff_7'] = features_df[target_col].diff(7)
        features_df[f'{target_game}_diff_30'] = features_df[target_col].diff(30)
        
        # Pattern features
        features_df[f'{target_game}_is_even'] = (features_df[target_col] % 2 == 0).astype(int)
        features_df[f'{target_game}_digit_sum'] = features_df[target_col].apply(
            lambda x: sum(int(d) for d in str(int(x))) if pd.notna(x) else np.nan
        )
        features_df[f'{target_game}_range_category'] = pd.cut(
            features_df[target_col], bins=[0, 33, 66, 99], labels=[0, 1, 2], include_lowest=True
        )
        
        # Frequency features
        for window in [30, 60, 90]:
            features_df[f'{target_game}_freq_{window}'] = features_df[target_col].rolling(
                window=window, min_periods=1
            ).apply(lambda x: (x == x.iloc[-1]).sum() if len(x) > 0 and pd.notna(x.iloc[-1]) else 0)
        
        # Cross-game features (if other games exist)
        other_games = [g for g in config.GAMES if g != target_game and g in features_df.columns]
        if other_games:
            # Correlation features
            for other_game in other_games:
                # Lag correlations
                for lag in [0, 1, 7]:
                    other_shifted = features_df[other_game].shift(lag)
                    features_df[f'{target_game}_{other_game}_corr_lag_{lag}'] = features_df[target_col].rolling(
                        window=30, min_periods=10
                    ).corr(other_shifted)
                
                # Same number indicator
                features_df[f'{target_game}_{other_game}_same'] = (
                    features_df[target_col] == features_df[other_game]
                ).astype(int)
        
        # Cyclical features (encode cyclical nature)
        features_df['day_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['day_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['month_sin'] = np.sin(2 * np.pi * features_df['month'] / 12)
        features_df['month_cos'] = np.cos(2 * np.pi * features_df['month'] / 12)
        
        # Select feature columns (exclude non-numeric and target)
        feature_cols = []
        for col in features_df.columns:
            if col not in ['date', target_col] and pd.api.types.is_numeric_dtype(features_df[col]):
                feature_cols.append(col)
        
        # Prepare final dataset
        X = features_df[feature_cols].copy()
        y = features_df[target_col].copy()
        
        # Remove rows where target is missing
        mask = ~y.isna()
        X = X[mask]
        y = y[mask]
        dates = features_df.loc[mask, 'date']
        
        # Fill missing values in features
        X = X.fillna(X.median())
        
        print(f"✅ Features created: {X.shape[1]} features, {len(X)} samples")
        print(f"   Date range: {dates.min()} to {dates.max()}")
        
        return X, y, dates, feature_cols
    
    def train_models(self, X, y, game_name):
        """Train multiple ML models"""
        print(f"🤖 Training ML models for {game_name}...")
        
        # Split data chronologically (80% train, 20% test)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        print(f"   Training set: {len(X_train)} samples")
        print(f"   Test set: {len(X_test)} samples")
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Define models
        models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100, 
                max_depth=10, 
                min_samples_split=5,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'ridge': Ridge(alpha=1.0),
            'svr': SVR(kernel='rbf', C=1.0, gamma='scale')
        }
        
        results = {}
        
        for name, model in models.items():
            print(f"   Training {name}...")
            
            try:
                if name in ['ridge', 'svr']:
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                
                # Clip predictions to valid range
                y_pred = np.clip(y_pred, 0, 99)
                
                # Calculate metrics
                mae = mean_absolute_error(y_test, y_pred)
                mse = mean_squared_error(y_test, y_pred)
                rmse = np.sqrt(mse)
                
                # Calculate accuracy (within ±5 range)
                accuracy_5 = np.mean(np.abs(y_test - y_pred) <= 5) * 100
                accuracy_10 = np.mean(np.abs(y_test - y_pred) <= 10) * 100
                
                # Calculate directional accuracy
                y_test_diff = np.diff(y_test)
                y_pred_diff = np.diff(y_pred)
                directional_accuracy = np.mean(np.sign(y_test_diff) == np.sign(y_pred_diff)) * 100
                
                results[name] = {
                    'model': model,
                    'mae': mae,
                    'mse': mse,
                    'rmse': rmse,
                    'accuracy_5': accuracy_5,
                    'accuracy_10': accuracy_10,
                    'directional_accuracy': directional_accuracy,
                    'predictions': y_pred,
                    'actual': y_test.values
                }
                
                print(f"     MAE: {mae:.2f}, RMSE: {rmse:.2f}")
                print(f"     Accuracy (±5): {accuracy_5:.1f}%")
                print(f"     Accuracy (±10): {accuracy_10:.1f}%")
                print(f"     Directional: {directional_accuracy:.1f}%")
                
            except Exception as e:
                print(f"     ❌ Error training {name}: {str(e)}")
                continue
        
        if results:
            # Select best model based on accuracy_10
            best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy_10'])
            best_model = results[best_model_name]
            
            self.models[game_name] = {
                'best_model': best_model['model'],
                'best_model_name': best_model_name,
                'scaler': scaler if best_model_name in ['ridge', 'svr'] else None,
                'results': results,
                'feature_names': X.columns.tolist()
            }
            
            self.accuracy_scores[game_name] = {
                'best_model': best_model_name,
                'mae': best_model['mae'],
                'rmse': best_model['rmse'],
                'accuracy_5': best_model['accuracy_5'],
                'accuracy_10': best_model['accuracy_10'],
                'directional_accuracy': best_model['directional_accuracy']
            }
            
            print(f"   ✅ Best model: {best_model_name}")
            print(f"   ✅ Best accuracy (±10): {best_model['accuracy_10']:.1f}%")
            
            return results
        else:
            print(f"   ❌ No models trained successfully for {game_name}")
            return None
    
    def backtest_model(self, X, y, dates, game_name, test_days=30):
        """Backtest model on recent data"""
        print(f"🔍 Backtesting {game_name} on last {test_days} days...")
        
        if game_name not in self.models:
            print(f"❌ No trained model for {game_name}")
            return None
        
        model_info = self.models[game_name]
        model = model_info['best_model']
        scaler = model_info['scaler']
        
        # Use last test_days for backtesting
        backtest_X = X.iloc[-test_days:]
        backtest_y = y.iloc[-test_days:]
        backtest_dates = dates.iloc[-test_days:]
        
        # Make predictions
        if scaler:
            backtest_X_scaled = scaler.transform(backtest_X)
            predictions = model.predict(backtest_X_scaled)
        else:
            predictions = model.predict(backtest_X)
        
        predictions = np.clip(predictions, 0, 99)
        
        # Calculate backtest metrics
        mae = mean_absolute_error(backtest_y, predictions)
        accuracy_5 = np.mean(np.abs(backtest_y - predictions) <= 5) * 100
        accuracy_10 = np.mean(np.abs(backtest_y - predictions) <= 10) * 100
        
        backtest_results = {
            'dates': backtest_dates.tolist(),
            'actual': backtest_y.tolist(),
            'predicted': predictions.tolist(),
            'mae': mae,
            'accuracy_5': accuracy_5,
            'accuracy_10': accuracy_10,
            'errors': (backtest_y - predictions).tolist()
        }
        
        print(f"   Backtest MAE: {mae:.2f}")
        print(f"   Backtest Accuracy (±5): {accuracy_5:.1f}%")
        print(f"   Backtest Accuracy (±10): {accuracy_10:.1f}%")
        
        return backtest_results
    
    def predict_next_value(self, X, game_name):
        """Predict next value using trained model"""
        if game_name not in self.models:
            print(f"❌ No trained model for {game_name}")
            return None
        
        model_info = self.models[game_name]
        model = model_info['best_model']
        scaler = model_info['scaler']
        
        # Use last row for prediction
        last_features = X.iloc[-1:].copy()
        
        # Make prediction
        if scaler:
            last_features_scaled = scaler.transform(last_features)
            prediction = model.predict(last_features_scaled)[0]
        else:
            prediction = model.predict(last_features)[0]
        
        prediction = np.clip(prediction, 0, 99)
        
        return int(round(prediction))
    
    def save_models(self, game_name):
        """Save trained models"""
        if game_name in self.models:
            os.makedirs(config.MODELS_DIR, exist_ok=True)
            model_file = os.path.join(config.MODELS_DIR, f'{game_name}_advanced_ml.joblib')
            joblib.dump(self.models[game_name], model_file)
            print(f"💾 Saved model for {game_name} to {model_file}")

def main():
    """Main training and prediction pipeline"""
    print("🚀 ADVANCED ML PREDICTION SYSTEM")
    print("=" * 60)
    
    predictor = AdvancedMLPredictor()
    
    # Step 1: Scrape extensive data
    print("\n📡 STEP 1: SCRAPING EXTENSIVE DATA")
    print("-" * 40)
    df = predictor.scrape_extensive_data(start_year=2023, end_year=2025)
    
    if df is None or len(df) < 100:
        print("❌ Insufficient data for training. Need at least 100 records.")
        return
    
    # Step 2: Train models for each game
    print(f"\n🤖 STEP 2: TRAINING ML MODELS")
    print("-" * 40)
    
    all_results = {}
    all_backtests = {}
    
    for game in config.GAMES:
        if game in df.columns:
            print(f"\n🎯 Processing {config.GAME_NAMES.get(game, game)}...")
            
            # Create features
            X, y, dates, feature_cols = predictor.create_advanced_features(df, game)
            
            if X is not None and len(X) >= 100:
                # Train models
                results = predictor.train_models(X, y, game)
                
                if results:
                    all_results[game] = results
                    
                    # Backtest
                    backtest = predictor.backtest_model(X, y, dates, game)
                    if backtest:
                        all_backtests[game] = backtest
                    
                    # Save model
                    predictor.save_models(game)
                    
                    # Make prediction for next value
                    next_pred = predictor.predict_next_value(X, game)
                    if next_pred is not None:
                        print(f"   🔮 Next prediction: {next_pred:02d}")
            else:
                print(f"   ❌ Insufficient data for {game}")
    
    # Step 3: Generate comprehensive report
    print(f"\n📊 STEP 3: GENERATING RESULTS")
    print("-" * 40)
    
    if predictor.accuracy_scores:
        print("\n🏆 MODEL PERFORMANCE SUMMARY:")
        print("=" * 50)
        
        for game, scores in predictor.accuracy_scores.items():
            game_name = config.GAME_NAMES.get(game, game)
            print(f"\n{game_name}:")
            print(f"  Best Model: {scores['best_model']}")
            print(f"  MAE: {scores['mae']:.2f}")
            print(f"  RMSE: {scores['rmse']:.2f}")
            print(f"  Accuracy (±5): {scores['accuracy_5']:.1f}%")
            print(f"  Accuracy (±10): {scores['accuracy_10']:.1f}%")
            print(f"  Directional: {scores['directional_accuracy']:.1f}%")
        
        # Generate final predictions
        print(f"\n🔮 FINAL ML PREDICTIONS:")
        print("=" * 30)
        
        final_predictions = {}
        
        for game in config.GAMES:
            if game in predictor.models:
                # Get latest data for prediction
                X, y, dates, _ = predictor.create_advanced_features(df, game)
                if X is not None:
                    prediction = predictor.predict_next_value(X, game)
                    if prediction is not None:
                        accuracy = predictor.accuracy_scores[game]['accuracy_10']
                        game_name = config.GAME_NAMES.get(game, game)
                        
                        final_predictions[game] = {
                            'game_name': game_name,
                            'prediction': prediction,
                            'accuracy': accuracy,
                            'model': predictor.accuracy_scores[game]['best_model']
                        }
                        
                        confidence_emoji = "🟢" if accuracy > 60 else "🟡" if accuracy > 40 else "🔴"
                        print(f"{confidence_emoji} {game_name}: {prediction:02d} (Accuracy: {accuracy:.1f}%)")
        
        # Save comprehensive report
        report = {
            'timestamp': datetime.now().isoformat(),
            'model_type': 'Advanced ML',
            'data_summary': {
                'total_records': len(df),
                'date_range': {
                    'start': df['date'].min().isoformat(),
                    'end': df['date'].max().isoformat()
                }
            },
            'predictions': final_predictions,
            'accuracy_scores': predictor.accuracy_scores,
            'backtests': all_backtests
        }
        
        os.makedirs(config.DATA_DIR, exist_ok=True)
        report_file = os.path.join(config.DATA_DIR, f'advanced_ml_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 Comprehensive report saved to: {report_file}")
        
    else:
        print("❌ No models trained successfully")

if __name__ == "__main__":
    main()
