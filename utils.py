"""
Utility functions for Satta King prediction bot
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import config

def plot_game_trends(data, game, days=90, save_path=None):
    """
    Plot trends for a specific game
    """
    if game not in data.columns:
        print(f"Game {game} not found in data")
        return None
    
    # Get recent data
    recent_data = data[['date', game]].dropna().tail(days)
    
    if len(recent_data) == 0:
        print(f"No data available for {game}")
        return None
    
    plt.figure(figsize=(12, 6))
    
    # Line plot
    plt.subplot(1, 2, 1)
    plt.plot(recent_data['date'], recent_data[game], marker='o', linewidth=2, markersize=4)
    plt.title(f'{config.GAME_NAMES.get(game, game)} - Last {days} Days')
    plt.xlabel('Date')
    plt.ylabel('Number')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # Distribution plot
    plt.subplot(1, 2, 2)
    plt.hist(recent_data[game], bins=20, alpha=0.7, edgecolor='black')
    plt.title(f'{config.GAME_NAMES.get(game, game)} - Distribution')
    plt.xlabel('Number')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    else:
        plt.show()
    
    return plt.gcf()

def calculate_statistics(data, game, days=30):
    """
    Calculate comprehensive statistics for a game
    """
    if game not in data.columns:
        return None
    
    recent_data = data[game].dropna().tail(days)
    
    if len(recent_data) == 0:
        return None
    
    stats = {
        'count': len(recent_data),
        'mean': float(recent_data.mean()),
        'median': float(recent_data.median()),
        'std': float(recent_data.std()),
        'min': int(recent_data.min()),
        'max': int(recent_data.max()),
        'range': int(recent_data.max() - recent_data.min()),
        'q25': float(recent_data.quantile(0.25)),
        'q75': float(recent_data.quantile(0.75)),
        'skewness': float(recent_data.skew()),
        'kurtosis': float(recent_data.kurtosis())
    }
    
    # Most frequent numbers
    value_counts = recent_data.value_counts()
    stats['most_frequent'] = value_counts.head(5).to_dict()
    
    # Patterns
    stats['even_count'] = int((recent_data % 2 == 0).sum())
    stats['odd_count'] = int((recent_data % 2 == 1).sum())
    stats['even_percentage'] = float(stats['even_count'] / len(recent_data) * 100)
    
    # Digit sum analysis
    digit_sums = recent_data.apply(lambda x: sum(int(d) for d in str(int(x))))
    stats['avg_digit_sum'] = float(digit_sums.mean())
    
    # Consecutive differences
    diffs = recent_data.diff().dropna()
    stats['avg_change'] = float(diffs.mean())
    stats['volatility'] = float(diffs.std())
    
    return stats

def find_patterns(data, game, pattern_length=3):
    """
    Find recurring patterns in the data
    """
    if game not in data.columns:
        return None
    
    values = data[game].dropna().values
    
    if len(values) < pattern_length * 2:
        return None
    
    patterns = {}
    
    # Find sequences of specified length
    for i in range(len(values) - pattern_length + 1):
        pattern = tuple(values[i:i + pattern_length])
        if pattern in patterns:
            patterns[pattern] += 1
        else:
            patterns[pattern] = 1
    
    # Sort by frequency
    sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
    
    # Return top patterns
    return sorted_patterns[:10]

def generate_summary_report(data, output_file=None):
    """
    Generate a comprehensive summary report
    """
    report = {
        'generated_at': datetime.now().isoformat(),
        'data_summary': {
            'total_records': len(data),
            'date_range': {
                'start': data['date'].min().isoformat() if 'date' in data.columns else None,
                'end': data['date'].max().isoformat() if 'date' in data.columns else None
            }
        },
        'games': {}
    }
    
    for game in config.GAMES:
        if game in data.columns:
            stats = calculate_statistics(data, game, days=90)
            patterns = find_patterns(data, game)
            
            report['games'][game] = {
                'name': config.GAME_NAMES.get(game, game),
                'statistics': stats,
                'top_patterns': patterns[:5] if patterns else []
            }
    
    if output_file:
        import json
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"Summary report saved to {output_file}")
    
    return report

def validate_data_quality(data):
    """
    Validate the quality of scraped data
    """
    issues = []
    
    # Check for missing dates
    if 'date' in data.columns:
        if data['date'].isnull().any():
            issues.append("Missing dates found")
    
    # Check data completeness for each game
    for game in config.GAMES:
        if game in data.columns:
            null_count = data[game].isnull().sum()
            total_count = len(data)
            null_percentage = (null_count / total_count) * 100
            
            if null_percentage > 50:
                issues.append(f"{game}: High missing data ({null_percentage:.1f}%)")
            
            # Check for invalid values (should be 0-99)
            valid_data = data[game].dropna()
            if len(valid_data) > 0:
                invalid_values = valid_data[(valid_data < 0) | (valid_data > 99)]
                if len(invalid_values) > 0:
                    issues.append(f"{game}: Invalid values found (outside 0-99 range)")
    
    # Check for duplicate dates
    if 'date' in data.columns:
        duplicate_dates = data['date'].duplicated().sum()
        if duplicate_dates > 0:
            issues.append(f"Duplicate dates found: {duplicate_dates}")
    
    return issues

def export_data(data, format='csv', filename=None):
    """
    Export data in various formats
    """
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"satta_data_export_{timestamp}"
    
    if format.lower() == 'csv':
        filepath = f"{filename}.csv"
        data.to_csv(filepath, index=False)
    elif format.lower() == 'excel':
        filepath = f"{filename}.xlsx"
        data.to_excel(filepath, index=False)
    elif format.lower() == 'json':
        filepath = f"{filename}.json"
        data.to_json(filepath, orient='records', date_format='iso')
    else:
        raise ValueError("Unsupported format. Use 'csv', 'excel', or 'json'")
    
    print(f"Data exported to {filepath}")
    return filepath

def create_visualization_dashboard(data, save_dir=None):
    """
    Create a comprehensive visualization dashboard
    """
    if save_dir is None:
        save_dir = os.path.join(config.DATA_DIR, 'visualizations')
        os.makedirs(save_dir, exist_ok=True)
    
    # Create plots for each game
    for game in config.GAMES:
        if game in data.columns:
            # Trend plot
            plot_path = os.path.join(save_dir, f"{game}_trends.png")
            plot_game_trends(data, game, days=90, save_path=plot_path)
            plt.close()
            
            # Correlation heatmap
            if len([g for g in config.GAMES if g in data.columns]) > 1:
                plt.figure(figsize=(8, 6))
                game_data = data[config.GAMES].dropna()
                if len(game_data) > 0:
                    correlation_matrix = game_data.corr()
                    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
                    plt.title('Game Correlations')
                    plt.tight_layout()
                    plt.savefig(os.path.join(save_dir, 'correlations.png'), dpi=300, bbox_inches='tight')
                    plt.close()
    
    print(f"Visualizations saved to {save_dir}")
    return save_dir

if __name__ == "__main__":
    # Example usage
    print("Utility functions loaded successfully")
