{"timestamp": "2025-06-10T20:33:42.175975", "method": "Ensemble (ML + Statistical)", "predictions": {"DSWR": {"game_name": "Desawar", "ml_prediction": 0, "stat_prediction": 27, "ensemble_prediction": 16, "ml_weight": 0.5555555555555556, "stat_weight": 0.7677791339861018, "confidence": 66.16673447708287}, "FRBD": {"game_name": "Faridabad", "ml_prediction": 16, "stat_prediction": 38, "ensemble_prediction": 29, "ml_weight": 0.4444444444444444, "stat_weight": 0.7001224739793845, "confidence": 57.22834592119145}, "GZBD": {"game_name": "Ghaziabad", "ml_prediction": 22, "stat_prediction": 36, "ensemble_prediction": 31, "ml_weight": 0.4444444444444444, "stat_weight": 0.7074810314889384, "confidence": 57.59627379666914}, "GALI": {"game_name": "Gali", "ml_prediction": 39, "stat_prediction": 36, "ensemble_prediction": 37, "ml_weight": 0.5555555555555556, "stat_weight": 0.6858701685283485, "confidence": 62.071286204195204}}, "summary": {"total_games": 4, "avg_confidence": 60.76566009978467, "methodology": "Weighted ensemble based on individual method accuracy"}}