"""
Intercept the real API calls by analyzing JavaScript and network patterns
Find the actual data sources these sites use
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import os

class RealAPIInterceptor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'https://satta-king-fast.com/'
        })
        
    def intercept_real_apis(self):
        """Find the real APIs by analyzing JavaScript and page behavior"""
        
        print("🕵️ INTERCEPTING REAL SATTA KING APIs")
        print("=" * 60)
        
        # Start with the main working site
        main_site = "https://satta-king-fast.com"
        
        print(f"\n📊 ANALYZING: {main_site}")
        print("-" * 40)
        
        try:
            response = self.session.get(main_site)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract all JavaScript for analysis
            js_analysis = self.extract_javascript_apis(soup, main_site)
            
            # Look for AJAX calls in the page
            ajax_calls = self.find_ajax_patterns(soup)
            
            # Check for data embedded in HTML
            embedded_data = self.extract_embedded_data(soup)
            
            # Test for real-time data endpoints
            realtime_endpoints = self.test_realtime_endpoints(main_site)
            
            # Try to find the actual data source
            data_source = self.find_data_source(main_site)
            
            # Compile results
            results = {
                'site': main_site,
                'javascript_apis': js_analysis,
                'ajax_patterns': ajax_calls,
                'embedded_data': embedded_data,
                'realtime_endpoints': realtime_endpoints,
                'data_source': data_source,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save and display results
            self.save_results(results)
            self.display_findings(results)
            
            return results
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return {'error': str(e)}
    
    def extract_javascript_apis(self, soup, base_url):
        """Extract API calls from JavaScript code"""
        
        print("   🔍 Analyzing JavaScript for API calls...")
        
        js_apis = {
            'external_scripts': [],
            'inline_code': [],
            'api_urls': [],
            'ajax_calls': [],
            'fetch_calls': [],
            'websocket_connections': []
        }
        
        # Analyze external scripts
        scripts = soup.find_all('script', src=True)
        for script in scripts:
            src = script.get('src')
            if src:
                js_apis['external_scripts'].append(src)
                
                # Download and analyze external scripts
                try:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    
                    if src.startswith('http'):
                        script_response = self.session.get(src, timeout=5)
                        script_content = script_response.text
                        
                        # Look for API patterns in external scripts
                        api_patterns = self.find_api_patterns_in_js(script_content)
                        js_apis['api_urls'].extend(api_patterns)
                        
                except:
                    continue
        
        # Analyze inline scripts
        inline_scripts = soup.find_all('script', src=False)
        for script in inline_scripts:
            if script.string:
                js_apis['inline_code'].append(script.string)
                
                # Look for API patterns
                api_patterns = self.find_api_patterns_in_js(script.string)
                js_apis['api_urls'].extend(api_patterns)
        
        print(f"   Found {len(js_apis['external_scripts'])} external scripts")
        print(f"   Found {len(js_apis['inline_code'])} inline scripts")
        print(f"   Found {len(js_apis['api_urls'])} potential API URLs")
        
        return js_apis
    
    def find_api_patterns_in_js(self, js_content):
        """Find API patterns in JavaScript content"""
        
        api_patterns = []
        
        # Common API URL patterns
        url_patterns = [
            r'["\']https?://[^"\']+(?:api|data|result|update|live)[^"\']*["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']',
            r'get\s*\(\s*["\']([^"\']+)["\']',
            r'post\s*\(\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest[^;]*open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in url_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match[0] else match[1]
                
                # Clean up the URL
                url = match.strip('\'"')
                if url and ('api' in url.lower() or 'data' in url.lower() or 'result' in url.lower()):
                    api_patterns.append(url)
        
        return list(set(api_patterns))  # Remove duplicates
    
    def find_ajax_patterns(self, soup):
        """Find AJAX call patterns"""
        
        print("   🔍 Looking for AJAX patterns...")
        
        ajax_patterns = {
            'jquery_ajax': False,
            'fetch_api': False,
            'xmlhttprequest': False,
            'intervals': [],
            'timeouts': []
        }
        
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                content = script.string.lower()
                
                if '$.ajax' in content or 'jquery' in content:
                    ajax_patterns['jquery_ajax'] = True
                
                if 'fetch(' in content:
                    ajax_patterns['fetch_api'] = True
                
                if 'xmlhttprequest' in content:
                    ajax_patterns['xmlhttprequest'] = True
                
                # Look for polling intervals
                interval_matches = re.findall(r'setinterval\s*\([^,]+,\s*(\d+)', content)
                ajax_patterns['intervals'].extend(interval_matches)
                
                timeout_matches = re.findall(r'settimeout\s*\([^,]+,\s*(\d+)', content)
                ajax_patterns['timeouts'].extend(timeout_matches)
        
        print(f"   AJAX patterns: jQuery={ajax_patterns['jquery_ajax']}, Fetch={ajax_patterns['fetch_api']}")
        
        return ajax_patterns
    
    def extract_embedded_data(self, soup):
        """Extract data that might be embedded in the HTML"""
        
        print("   🔍 Looking for embedded data...")
        
        embedded = {
            'json_data': [],
            'data_attributes': [],
            'hidden_inputs': [],
            'script_variables': []
        }
        
        # Look for JSON in script tags
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Look for JSON objects
                json_patterns = [
                    r'var\s+\w+\s*=\s*({[^;]+});',
                    r'let\s+\w+\s*=\s*({[^;]+});',
                    r'const\s+\w+\s*=\s*({[^;]+});',
                    r'window\.\w+\s*=\s*({[^;]+});'
                ]
                
                for pattern in json_patterns:
                    matches = re.findall(pattern, script.string)
                    for match in matches:
                        try:
                            json_obj = json.loads(match)
                            embedded['json_data'].append(json_obj)
                        except:
                            continue
        
        # Look for data attributes
        elements_with_data = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        for element in elements_with_data:
            for attr, value in element.attrs.items():
                if attr.startswith('data-'):
                    embedded['data_attributes'].append({attr: value})
        
        # Look for hidden inputs
        hidden_inputs = soup.find_all('input', type='hidden')
        for inp in hidden_inputs:
            embedded['hidden_inputs'].append({
                'name': inp.get('name'),
                'value': inp.get('value')
            })
        
        print(f"   Found {len(embedded['json_data'])} JSON objects")
        print(f"   Found {len(embedded['data_attributes'])} data attributes")
        
        return embedded
    
    def test_realtime_endpoints(self, base_url):
        """Test for real-time data endpoints"""
        
        print("   🔍 Testing real-time endpoints...")
        
        # Common real-time endpoint patterns
        realtime_paths = [
            '/live', '/current', '/now', '/today', '/latest',
            '/api/live', '/api/current', '/api/now',
            '/data/live', '/data/current', '/data/now',
            '/result/live', '/result/current', '/result/now'
        ]
        
        working_endpoints = []
        
        for path in realtime_paths:
            try:
                url = urljoin(base_url, path)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    # Check if it contains satta data
                    content = response.text.lower()
                    if any(game in content for game in ['gali', 'dswr', 'frbd', 'gzbd']):
                        working_endpoints.append({
                            'url': url,
                            'status': response.status_code,
                            'content_type': response.headers.get('content-type', ''),
                            'has_satta_data': True
                        })
                        print(f"   ✅ Found: {url}")
                
            except:
                continue
            
            time.sleep(0.2)
        
        return working_endpoints
    
    def find_data_source(self, base_url):
        """Try to find the actual data source"""
        
        print("   🔍 Searching for data source...")
        
        data_source = {
            'method': 'unknown',
            'source_url': None,
            'update_mechanism': 'unknown',
            'evidence': []
        }
        
        try:
            # Get the page and look for current results
            response = self.session.get(base_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract current results
            current_results = self.extract_current_results(soup)
            
            if current_results:
                data_source['evidence'].append(f"Found current results: {current_results}")
                
                # Check if results are in HTML (server-side rendering)
                if soup.find_all('table'):
                    data_source['method'] = 'server_side_rendering'
                    data_source['evidence'].append("Results embedded in HTML tables")
                
                # Check for dynamic loading
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and any(keyword in script.string.lower() for keyword in ['result', 'data', 'update']):
                        data_source['method'] = 'client_side_loading'
                        data_source['evidence'].append("JavaScript handles result loading")
                        break
            
            # Check response headers for caching info
            cache_control = response.headers.get('cache-control', '')
            if 'no-cache' in cache_control:
                data_source['update_mechanism'] = 'real_time'
                data_source['evidence'].append("No-cache headers suggest real-time updates")
            
        except Exception as e:
            data_source['evidence'].append(f"Error in analysis: {str(e)}")
        
        return data_source
    
    def extract_current_results(self, soup):
        """Extract current satta results from the page"""
        
        results = {}
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        # Look in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    text = ' '.join([cell.get_text() for cell in cells])
                    
                    for game in games:
                        pattern = rf'{game}[:\s]*(\d{{1,2}})'
                        match = re.search(pattern, text, re.IGNORECASE)
                        if match:
                            results[game] = match.group(1)
        
        return results
    
    def save_results(self, results):
        """Save the interception results"""
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/real_api_interception_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Interception results saved to: {report_file}")
    
    def display_findings(self, results):
        """Display the key findings"""
        
        print(f"\n📋 REAL API INTERCEPTION RESULTS")
        print("=" * 60)
        
        js_apis = results.get('javascript_apis', {})
        ajax_patterns = results.get('ajax_patterns', {})
        embedded_data = results.get('embedded_data', {})
        realtime_endpoints = results.get('realtime_endpoints', [])
        data_source = results.get('data_source', {})
        
        print(f"JavaScript APIs Found: {len(js_apis.get('api_urls', []))}")
        print(f"AJAX Patterns: {any(ajax_patterns.values())}")
        print(f"Embedded Data: {len(embedded_data.get('json_data', []))}")
        print(f"Real-time Endpoints: {len(realtime_endpoints)}")
        print(f"Data Source Method: {data_source.get('method', 'unknown')}")
        
        # Show working endpoints
        if realtime_endpoints:
            print(f"\n✅ WORKING ENDPOINTS FOUND:")
            for endpoint in realtime_endpoints:
                print(f"  • {endpoint['url']}")
                print(f"    Status: {endpoint['status']}")
                print(f"    Type: {endpoint['content_type']}")
        
        # Show API URLs found in JavaScript
        api_urls = js_apis.get('api_urls', [])
        if api_urls:
            print(f"\n🔗 API URLs FOUND IN JAVASCRIPT:")
            for url in api_urls[:10]:  # Show first 10
                print(f"  • {url}")
        
        # Show data source evidence
        evidence = data_source.get('evidence', [])
        if evidence:
            print(f"\n🔍 DATA SOURCE EVIDENCE:")
            for item in evidence:
                print(f"  • {item}")

if __name__ == "__main__":
    print("🚀 Starting real API interception...")
    
    interceptor = RealAPIInterceptor()
    results = interceptor.intercept_real_apis()
    
    print("\n✅ API interception complete!")
    
    # Check if we found any working APIs
    realtime_endpoints = results.get('realtime_endpoints', [])
    api_urls = results.get('javascript_apis', {}).get('api_urls', [])
    
    if realtime_endpoints or api_urls:
        print(f"\n🎯 SUCCESS! Found potential API access points.")
        print("Check the saved report for detailed information.")
    else:
        print(f"\n⚠️ No direct API access found.")
        print("The data appears to be server-side rendered or uses protected APIs.")
