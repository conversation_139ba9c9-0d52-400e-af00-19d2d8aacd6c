"""
Main application for Satta King scraper and prediction bot
"""

import argparse
import logging
import json
import os
from datetime import datetime
import sys

from scraper import SattaKingScraper
from data_processor import SattaDataProcessor
from predictor import SattaPredictor
import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(config.LOGS_DIR, 'main.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def scrape_data(start_year=None, end_year=None):
    """
    Scrape data from the website
    """
    logger.info("Starting data scraping...")
    
    scraper = SattaKingScraper()
    data = scraper.scrape_all_data(start_year, end_year)
    
    if data:
        logger.info(f"Successfully scraped {len(data)} records")
        return True
    else:
        logger.error("Failed to scrape data")
        return False

def process_data():
    """
    Process scraped data
    """
    logger.info("Starting data processing...")
    
    processor = SattaDataProcessor()
    try:
        df, file_path = processor.process_all()
        logger.info(f"Data processed successfully. Saved to: {file_path}")
        logger.info(f"Processed data shape: {df.shape}")
        return True
    except Exception as e:
        logger.error(f"Error processing data: {str(e)}")
        return False

def train_models(games=None):
    """
    Train prediction models
    """
    logger.info("Starting model training...")
    
    predictor = SattaPredictor()
    try:
        predictor.load_and_prepare_data()
        predictor.train_models(games)
        logger.info("Model training completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error training models: {str(e)}")
        return False

def make_predictions(games=None, method='ensemble'):
    """
    Make predictions for next numbers
    """
    logger.info("Generating predictions...")
    
    predictor = SattaPredictor()
    try:
        predictor.load_and_prepare_data()
        
        # Generate comprehensive report
        report = predictor.generate_report(games)
        
        # Save report
        report_file = predictor.save_report(report)
        
        # Display predictions
        print("\n" + "="*50)
        print("SATTA KING PREDICTIONS")
        print("="*50)
        print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        if report['predictions']:
            for game, pred_info in report['predictions'].items():
                game_name = config.GAME_NAMES.get(game, game)
                prediction = pred_info['prediction']
                confidence = pred_info['confidence']
                
                print(f"{game_name:12}: {prediction:2d} (Confidence: {confidence:.1%})")
            
            print()
            print(f"Average Confidence: {report['summary']['average_confidence']:.1%}")
        else:
            print("No predictions could be generated.")
        
        print("="*50)
        print(f"Detailed report saved to: {report_file}")
        
        return True
    except Exception as e:
        logger.error(f"Error making predictions: {str(e)}")
        return False

def show_analysis(game=None, days=30):
    """
    Show analysis of recent patterns
    """
    predictor = SattaPredictor()
    try:
        predictor.load_and_prepare_data()
        
        games_to_analyze = [game] if game else config.GAMES
        
        print("\n" + "="*60)
        print("RECENT PATTERN ANALYSIS")
        print("="*60)
        
        for g in games_to_analyze:
            analysis = predictor.get_analysis(g, days)
            if analysis:
                game_name = config.GAME_NAMES.get(g, g)
                print(f"\n{game_name} (Last {days} days):")
                print(f"  Last Value: {analysis['last_value']}")
                print(f"  Average: {analysis['mean']:.1f}")
                print(f"  Range: {analysis['min']} - {analysis['max']}")
                print(f"  Most Frequent: {analysis['most_frequent']}")
                print(f"  Trend: {analysis['trend']}")
                print(f"  Recent Values: {analysis['recent_values'][-10:]}")  # Last 10 values
        
        print("="*60)
        return True
    except Exception as e:
        logger.error(f"Error showing analysis: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Satta King Scraper and Prediction Bot')
    parser.add_argument('action', choices=['scrape', 'process', 'train', 'predict', 'analyze', 'full'],
                       help='Action to perform')
    parser.add_argument('--games', nargs='+', choices=config.GAMES,
                       help='Specific games to process (default: all)')
    parser.add_argument('--start-year', type=int, default=config.START_YEAR,
                       help='Start year for scraping')
    parser.add_argument('--end-year', type=int, default=config.END_YEAR,
                       help='End year for scraping')
    parser.add_argument('--method', choices=['ensemble', 'traditional', 'lstm'], default='ensemble',
                       help='Prediction method')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days for analysis')
    
    args = parser.parse_args()
    
    success = True
    
    if args.action == 'scrape':
        success = scrape_data(args.start_year, args.end_year)
    
    elif args.action == 'process':
        success = process_data()
    
    elif args.action == 'train':
        success = train_models(args.games)
    
    elif args.action == 'predict':
        success = make_predictions(args.games, args.method)
    
    elif args.action == 'analyze':
        game = args.games[0] if args.games else None
        success = show_analysis(game, args.days)
    
    elif args.action == 'full':
        # Complete pipeline
        print("Running complete pipeline...")
        
        # Step 1: Scrape data
        print("\n1. Scraping data...")
        if not scrape_data(args.start_year, args.end_year):
            success = False
        
        # Step 2: Process data
        if success:
            print("\n2. Processing data...")
            if not process_data():
                success = False
        
        # Step 3: Train models
        if success:
            print("\n3. Training models...")
            if not train_models(args.games):
                success = False
        
        # Step 4: Make predictions
        if success:
            print("\n4. Generating predictions...")
            if not make_predictions(args.games, args.method):
                success = False
    
    if success:
        print("\nOperation completed successfully!")
        sys.exit(0)
    else:
        print("\nOperation failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
