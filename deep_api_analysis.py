"""
Deep analysis of how Satta King websites get their data
Focus on discovering the actual data sources and API patterns
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import os

class DeepSattaAPIAnalyzer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
    def analyze_data_sources(self):
        """Deep analysis of how sites get their data"""
        
        print("🔍 DEEP SATTA KING DATA SOURCE ANALYSIS")
        print("=" * 60)
        
        # Focus on the main working site
        main_site = "https://satta-king-fast.com"
        
        print(f"\n📊 ANALYZING: {main_site}")
        print("-" * 40)
        
        try:
            response = self.session.get(main_site)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            analysis = {
                'site': main_site,
                'data_update_mechanism': self.find_update_mechanism(soup),
                'javascript_analysis': self.analyze_javascript(soup),
                'form_analysis': self.analyze_forms(soup),
                'network_requests': self.find_network_requests(soup),
                'data_patterns': self.analyze_data_patterns(soup),
                'server_analysis': self.analyze_server_response(response)
            }
            
            # Test for real-time updates
            print("\n🔄 TESTING REAL-TIME UPDATE MECHANISM")
            print("-" * 40)
            
            update_analysis = self.test_realtime_updates(main_site)
            analysis['realtime_updates'] = update_analysis
            
            # Look for admin interfaces
            print("\n🔐 SEARCHING FOR ADMIN INTERFACES")
            print("-" * 40)
            
            admin_analysis = self.search_admin_interfaces(main_site)
            analysis['admin_interfaces'] = admin_analysis
            
            # Test for database connections
            print("\n💾 TESTING DATABASE CONNECTIONS")
            print("-" * 40)
            
            db_analysis = self.test_database_connections(main_site)
            analysis['database_analysis'] = db_analysis
            
            # Generate conclusions
            conclusions = self.generate_conclusions(analysis)
            analysis['conclusions'] = conclusions
            
            # Save detailed report
            self.save_analysis_report(analysis)
            
            return analysis
            
        except Exception as e:
            print(f"❌ Error in analysis: {str(e)}")
            return {'error': str(e)}
    
    def find_update_mechanism(self, soup):
        """Find how the site updates its data"""
        
        mechanisms = {
            'auto_refresh': False,
            'ajax_polling': False,
            'websockets': False,
            'server_push': False,
            'manual_update': False,
            'evidence': []
        }
        
        # Check for auto-refresh meta tags
        refresh_meta = soup.find('meta', attrs={'http-equiv': 'refresh'})
        if refresh_meta:
            mechanisms['auto_refresh'] = True
            mechanisms['evidence'].append(f"Auto-refresh meta tag: {refresh_meta.get('content')}")
        
        # Check JavaScript for polling/AJAX
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                content = script.string.lower()
                
                if 'setinterval' in content or 'settimeout' in content:
                    mechanisms['ajax_polling'] = True
                    mechanisms['evidence'].append("JavaScript polling detected")
                
                if 'websocket' in content or 'ws://' in content or 'wss://' in content:
                    mechanisms['websockets'] = True
                    mechanisms['evidence'].append("WebSocket connection detected")
                
                if 'eventsource' in content or 'server-sent' in content:
                    mechanisms['server_push'] = True
                    mechanisms['evidence'].append("Server-sent events detected")
        
        if not any([mechanisms['auto_refresh'], mechanisms['ajax_polling'], 
                   mechanisms['websockets'], mechanisms['server_push']]):
            mechanisms['manual_update'] = True
            mechanisms['evidence'].append("No automatic update mechanism found")
        
        print(f"   Update mechanism: {[k for k, v in mechanisms.items() if v and k != 'evidence']}")
        
        return mechanisms
    
    def analyze_javascript(self, soup):
        """Analyze JavaScript for data handling patterns"""
        
        js_analysis = {
            'external_scripts': [],
            'inline_scripts_count': 0,
            'api_calls': [],
            'data_manipulation': [],
            'suspicious_patterns': []
        }
        
        scripts = soup.find_all('script')
        
        for script in scripts:
            if script.get('src'):
                js_analysis['external_scripts'].append(script.get('src'))
            elif script.string:
                js_analysis['inline_scripts_count'] += 1
                content = script.string
                
                # Look for API calls
                api_patterns = [
                    r'fetch\s*\(\s*["\']([^"\']+)["\']',
                    r'ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']',
                    r'get\s*\(\s*["\']([^"\']+)["\']',
                    r'post\s*\(\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    js_analysis['api_calls'].extend(matches)
                
                # Look for data manipulation
                if any(keyword in content.lower() for keyword in ['result', 'number', 'generate', 'random']):
                    js_analysis['data_manipulation'].append("Data manipulation keywords found")
                
                # Look for suspicious patterns
                if 'math.random' in content.lower():
                    js_analysis['suspicious_patterns'].append("Random number generation")
                
                if any(keyword in content.lower() for keyword in ['admin', 'secret', 'key', 'password']):
                    js_analysis['suspicious_patterns'].append("Admin/secret keywords")
        
        print(f"   JavaScript: {js_analysis['inline_scripts_count']} inline, {len(js_analysis['external_scripts'])} external")
        print(f"   API calls found: {len(js_analysis['api_calls'])}")
        
        return js_analysis
    
    def analyze_forms(self, soup):
        """Analyze forms for data submission"""
        
        forms_analysis = {
            'total_forms': 0,
            'hidden_forms': [],
            'admin_forms': [],
            'data_forms': []
        }
        
        forms = soup.find_all('form')
        forms_analysis['total_forms'] = len(forms)
        
        for form in forms:
            action = form.get('action', '')
            method = form.get('method', 'GET')
            
            # Check for hidden inputs
            hidden_inputs = form.find_all('input', type='hidden')
            if hidden_inputs:
                forms_analysis['hidden_forms'].append({
                    'action': action,
                    'method': method,
                    'hidden_count': len(hidden_inputs)
                })
            
            # Check for admin-related forms
            if any(keyword in action.lower() for keyword in ['admin', 'update', 'manage']):
                forms_analysis['admin_forms'].append({
                    'action': action,
                    'method': method
                })
            
            # Check for data-related forms
            if any(keyword in action.lower() for keyword in ['data', 'result', 'api']):
                forms_analysis['data_forms'].append({
                    'action': action,
                    'method': method
                })
        
        print(f"   Forms: {forms_analysis['total_forms']} total, {len(forms_analysis['hidden_forms'])} with hidden inputs")
        
        return forms_analysis
    
    def find_network_requests(self, soup):
        """Find potential network requests in the page"""
        
        requests_analysis = {
            'external_domains': set(),
            'api_like_urls': [],
            'cdn_resources': [],
            'tracking_scripts': []
        }
        
        # Check all external resources
        for tag in soup.find_all(['script', 'link', 'img', 'iframe']):
            src = tag.get('src') or tag.get('href')
            if src and src.startswith('http'):
                domain = urlparse(src).netloc
                requests_analysis['external_domains'].add(domain)
                
                # Categorize resources
                if any(keyword in src.lower() for keyword in ['api', 'data', 'result']):
                    requests_analysis['api_like_urls'].append(src)
                elif any(keyword in src.lower() for keyword in ['cdn', 'static', 'assets']):
                    requests_analysis['cdn_resources'].append(src)
                elif any(keyword in src.lower() for keyword in ['analytics', 'tracking', 'ads']):
                    requests_analysis['tracking_scripts'].append(src)
        
        requests_analysis['external_domains'] = list(requests_analysis['external_domains'])
        
        print(f"   External domains: {len(requests_analysis['external_domains'])}")
        print(f"   API-like URLs: {len(requests_analysis['api_like_urls'])}")
        
        return requests_analysis
    
    def analyze_data_patterns(self, soup):
        """Analyze how data is structured and presented"""
        
        data_analysis = {
            'data_tables': 0,
            'result_patterns': [],
            'number_patterns': [],
            'timestamp_patterns': []
        }
        
        # Analyze tables
        tables = soup.find_all('table')
        data_analysis['data_tables'] = len(tables)
        
        # Look for result patterns in text
        text_content = soup.get_text()
        
        # Find number patterns (potential results)
        number_matches = re.findall(r'\b\d{1,2}\b', text_content)
        data_analysis['number_patterns'] = list(set(number_matches))[:20]  # First 20 unique
        
        # Find game result patterns
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        for game in games:
            pattern = rf'{game}[:\s]*(\d{{1,2}})'
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                data_analysis['result_patterns'].append({
                    'game': game,
                    'results': matches
                })
        
        # Find timestamp patterns
        time_patterns = [
            r'\d{1,2}:\d{2}\s*(?:AM|PM)',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{2}-\d{2}'
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                data_analysis['timestamp_patterns'].extend(matches[:5])  # First 5
        
        print(f"   Data tables: {data_analysis['data_tables']}")
        print(f"   Result patterns: {len(data_analysis['result_patterns'])}")
        
        return data_analysis
    
    def analyze_server_response(self, response):
        """Analyze server response headers for clues"""
        
        server_analysis = {
            'server': response.headers.get('server', 'unknown'),
            'cache_control': response.headers.get('cache-control', ''),
            'last_modified': response.headers.get('last-modified', ''),
            'content_type': response.headers.get('content-type', ''),
            'response_time': response.elapsed.total_seconds(),
            'cdn_detected': False,
            'dynamic_content': False
        }
        
        # Check for CDN
        cdn_headers = ['cf-ray', 'x-cache', 'x-served-by', 'x-amz-cf-id']
        for header in cdn_headers:
            if header in response.headers:
                server_analysis['cdn_detected'] = True
                break
        
        # Check for dynamic content
        if 'no-cache' in server_analysis['cache_control'] or 'no-store' in server_analysis['cache_control']:
            server_analysis['dynamic_content'] = True
        
        print(f"   Server: {server_analysis['server']}")
        print(f"   CDN: {server_analysis['cdn_detected']}")
        print(f"   Dynamic: {server_analysis['dynamic_content']}")
        
        return server_analysis
    
    def test_realtime_updates(self, base_url):
        """Test if the site updates in real-time"""
        
        print("   Testing for real-time updates...")
        
        update_test = {
            'tested': True,
            'updates_detected': False,
            'update_frequency': 'unknown',
            'test_results': []
        }
        
        try:
            # Get initial state
            initial_response = self.session.get(base_url)
            initial_content = initial_response.text
            
            # Wait and check again
            time.sleep(30)
            
            second_response = self.session.get(base_url)
            second_content = second_response.text
            
            # Compare content
            if initial_content != second_content:
                update_test['updates_detected'] = True
                update_test['test_results'].append("Content changed in 30 seconds")
            else:
                update_test['test_results'].append("No content change in 30 seconds")
            
            print(f"   Real-time updates: {update_test['updates_detected']}")
            
        except Exception as e:
            update_test['tested'] = False
            update_test['error'] = str(e)
            print(f"   Update test failed: {str(e)}")
        
        return update_test
    
    def search_admin_interfaces(self, base_url):
        """Search for admin interfaces"""
        
        admin_analysis = {
            'admin_paths_found': [],
            'login_forms': [],
            'protected_areas': []
        }
        
        admin_paths = [
            '/admin', '/admin.php', '/admin/', '/administrator',
            '/manage', '/control', '/panel', '/dashboard',
            '/wp-admin', '/login', '/login.php'
        ]
        
        for path in admin_paths:
            try:
                url = urljoin(base_url, path)
                response = self.session.head(url, timeout=5)
                
                if response.status_code in [200, 301, 302, 401, 403]:
                    admin_analysis['admin_paths_found'].append({
                        'path': path,
                        'status': response.status_code,
                        'url': url
                    })
                    
                    if response.status_code == 401:
                        admin_analysis['protected_areas'].append(url)
                
            except:
                continue
            
            time.sleep(0.2)
        
        print(f"   Admin paths found: {len(admin_analysis['admin_paths_found'])}")
        
        return admin_analysis
    
    def test_database_connections(self, base_url):
        """Test for database connection patterns"""
        
        db_analysis = {
            'sql_errors': [],
            'database_hints': [],
            'connection_tests': []
        }
        
        # Test for SQL injection to reveal database info
        test_params = ['\'', '"', '1=1', 'OR 1=1', 'UNION SELECT']
        
        for param in test_params:
            try:
                test_url = f"{base_url}?id={param}"
                response = self.session.get(test_url, timeout=5)
                
                # Look for database error messages
                error_patterns = [
                    'mysql', 'sql', 'database', 'table', 'column',
                    'syntax error', 'query failed', 'connection failed'
                ]
                
                content_lower = response.text.lower()
                for pattern in error_patterns:
                    if pattern in content_lower:
                        db_analysis['sql_errors'].append({
                            'param': param,
                            'pattern': pattern,
                            'status': response.status_code
                        })
                        break
                
            except:
                continue
            
            time.sleep(0.5)
        
        print(f"   Database hints found: {len(db_analysis['sql_errors'])}")
        
        return db_analysis
    
    def generate_conclusions(self, analysis):
        """Generate conclusions about data sources"""
        
        conclusions = {
            'data_source_type': 'unknown',
            'update_mechanism': 'unknown',
            'manipulation_risk': 'unknown',
            'evidence': [],
            'recommendations': []
        }
        
        # Analyze update mechanism
        update_mech = analysis.get('update_mechanism', {})
        if update_mech.get('manual_update'):
            conclusions['update_mechanism'] = 'manual'
            conclusions['evidence'].append("No automatic update mechanism detected")
        elif update_mech.get('ajax_polling'):
            conclusions['update_mechanism'] = 'ajax_polling'
            conclusions['evidence'].append("AJAX polling detected")
        
        # Analyze server response
        server_info = analysis.get('server_analysis', {})
        if server_info.get('dynamic_content'):
            conclusions['data_source_type'] = 'database_driven'
            conclusions['evidence'].append("Dynamic content suggests database backend")
        
        # Analyze admin interfaces
        admin_info = analysis.get('admin_interfaces', {})
        if admin_info.get('admin_paths_found'):
            conclusions['manipulation_risk'] = 'high'
            conclusions['evidence'].append("Admin interfaces found")
        
        # Generate recommendations
        conclusions['recommendations'] = [
            "Monitor network traffic during result updates",
            "Investigate server-side technologies used",
            "Check for database connection strings in source code",
            "Test API endpoints for data access patterns"
        ]
        
        return conclusions
    
    def save_analysis_report(self, analysis):
        """Save detailed analysis report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'analysis_type': 'deep_data_source_analysis',
            'analysis': analysis
        }
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/deep_data_source_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 Deep analysis report saved to: {report_file}")
        
        # Print summary
        self.print_analysis_summary(analysis)
    
    def print_analysis_summary(self, analysis):
        """Print analysis summary"""
        
        print("\n📋 ANALYSIS SUMMARY")
        print("=" * 60)
        
        conclusions = analysis.get('conclusions', {})
        
        print(f"Data Source Type: {conclusions.get('data_source_type', 'unknown')}")
        print(f"Update Mechanism: {conclusions.get('update_mechanism', 'unknown')}")
        print(f"Manipulation Risk: {conclusions.get('manipulation_risk', 'unknown')}")
        
        print("\nKey Evidence:")
        for evidence in conclusions.get('evidence', []):
            print(f"  • {evidence}")
        
        print("\nRecommendations:")
        for rec in conclusions.get('recommendations', []):
            print(f"  • {rec}")

if __name__ == "__main__":
    analyzer = DeepSattaAPIAnalyzer()
    analysis = analyzer.analyze_data_sources()
