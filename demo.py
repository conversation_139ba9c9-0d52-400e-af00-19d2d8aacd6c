"""
Demo script to showcase the Satta King prediction bot functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

from scraper import SattaKingScraper
from data_processor import SattaDataProcessor
from predictor import SattaPredictor
from utils import calculate_statistics, plot_game_trends
import config

def create_sample_data():
    """Create sample data for demonstration"""
    print("Creating sample data for demonstration...")
    
    # Generate sample data for the last 6 months
    start_date = datetime.now() - timedelta(days=180)
    dates = pd.date_range(start=start_date, end=datetime.now(), freq='D')
    
    data = []
    np.random.seed(42)  # For reproducible results
    
    for date in dates:
        # Generate realistic-looking numbers (0-99) with some patterns
        base_trend = int(50 + 20 * np.sin(date.dayofyear / 365 * 2 * np.pi))
        
        record = {
            'date': date.strftime('%Y-%m-%d'),
            'day': date.day,
            'month': date.strftime('%B'),
            'year': date.year,
            'DSWR': np.random.randint(0, 100),
            'FRBD': np.random.randint(0, 100),
            'GZBD': np.random.randint(0, 100),
            'GALI': np.random.randint(0, 100)
        }
        
        # Add some correlation between games
        if np.random.random() < 0.3:  # 30% chance of correlation
            base_num = record['DSWR']
            record['FRBD'] = (base_num + np.random.randint(-10, 11)) % 100
        
        data.append(record)
    
    # Save sample data
    df = pd.DataFrame(data)
    os.makedirs(config.RAW_DATA_DIR, exist_ok=True)
    sample_file = os.path.join(config.RAW_DATA_DIR, 'sample_satta_data.csv')
    df.to_csv(sample_file, index=False)
    
    print(f"Sample data created with {len(data)} records")
    print(f"Saved to: {sample_file}")
    return df

def demo_scraping():
    """Demonstrate web scraping functionality"""
    print("\n" + "="*60)
    print("DEMO: Web Scraping")
    print("="*60)
    
    scraper = SattaKingScraper()
    
    # Scrape recent months
    print("Scraping recent months data...")
    recent_months = [
        ("June", 2025),
        ("May", 2025),
        ("April", 2025)
    ]
    
    all_data = []
    for month, year in recent_months:
        print(f"Scraping {month} {year}...")
        data = scraper.get_month_data(month, year)
        if data:
            all_data.extend(data)
            print(f"  Found {len(data)} records")
        else:
            print(f"  No data found")
    
    if all_data:
        # Save scraped data
        df = pd.DataFrame(all_data)
        scraped_file = os.path.join(config.RAW_DATA_DIR, 'scraped_demo_data.csv')
        df.to_csv(scraped_file, index=False)
        print(f"\nScraped data saved to: {scraped_file}")
        print(f"Total records: {len(all_data)}")
        
        # Show sample
        print("\nSample scraped data:")
        print(df.head().to_string())
        return df
    else:
        print("No data could be scraped. Using sample data instead.")
        return create_sample_data()

def demo_data_processing(data):
    """Demonstrate data processing functionality"""
    print("\n" + "="*60)
    print("DEMO: Data Processing")
    print("="*60)
    
    processor = SattaDataProcessor()
    
    # Clean and process data
    print("Cleaning data...")
    clean_data = processor.clean_data(data.copy())
    print(f"Cleaned data shape: {clean_data.shape}")
    
    print("\nCreating features...")
    processed_data = processor.create_features(clean_data)
    print(f"Processed data shape: {processed_data.shape}")
    print(f"Features created: {processed_data.shape[1] - clean_data.shape[1]} new features")
    
    # Save processed data
    processed_file = processor.save_processed_data(processed_data, 'demo_processed_data.csv')
    print(f"Processed data saved to: {processed_file}")
    
    return processed_data

def demo_analysis(data):
    """Demonstrate data analysis functionality"""
    print("\n" + "="*60)
    print("DEMO: Data Analysis")
    print("="*60)
    
    for game in config.GAMES:
        if game in data.columns:
            print(f"\nAnalyzing {config.GAME_NAMES.get(game, game)}:")
            
            # Calculate statistics
            stats = calculate_statistics(data, game, days=30)
            if stats:
                print(f"  Recent average: {stats['mean']:.1f}")
                print(f"  Range: {stats['min']} - {stats['max']}")
                print(f"  Most frequent: {list(stats['most_frequent'].keys())[:3]}")
                print(f"  Even numbers: {stats['even_percentage']:.1f}%")

def demo_prediction():
    """Demonstrate prediction functionality"""
    print("\n" + "="*60)
    print("DEMO: Prediction Models")
    print("="*60)
    
    # Use sample data for faster demo
    print("Using sample data for prediction demo...")
    sample_data = create_sample_data()
    
    predictor = SattaPredictor()
    predictor.data = sample_data
    
    # Train models (simplified for demo)
    print("\nTraining prediction models...")
    try:
        # Train on sample data
        for game in ['DSWR', 'GALI']:  # Train on 2 games for demo
            if game in sample_data.columns:
                print(f"Training models for {config.GAME_NAMES.get(game, game)}...")
                
                # Prepare data
                X, y, feature_cols = predictor.processor.prepare_ml_data(sample_data, game)
                
                if len(X) > 50:  # Need sufficient data
                    # Train traditional models
                    predictor.models.train_traditional_models(X, y, game)
                    print(f"  Traditional models trained for {game}")
                    
                    # Create sequences for LSTM (if enough data)
                    if len(sample_data) > 100:
                        sequences_X, sequences_y = predictor.processor.create_sequences(sample_data, game)
                        if len(sequences_X) > 20:
                            predictor.models.train_lstm_model(sequences_X, sequences_y, game)
                            print(f"  LSTM model trained for {game}")
        
        predictor.is_trained = True
        
        # Generate predictions
        print("\nGenerating predictions...")
        predictions = predictor.predict_next_numbers(['DSWR', 'GALI'])
        
        if predictions:
            print("\nPrediction Results:")
            for game, pred_info in predictions.items():
                game_name = config.GAME_NAMES.get(game, game)
                print(f"  {game_name}: {pred_info['prediction']:02d} (Confidence: {pred_info['confidence']:.1%})")
        else:
            print("No predictions could be generated")
            
    except Exception as e:
        print(f"Error in prediction demo: {str(e)}")
        print("This is normal for a demo - real training requires more data and time")

def demo_report():
    """Generate a demo report"""
    print("\n" + "="*60)
    print("DEMO: Prediction Report")
    print("="*60)
    
    # Create a sample report
    sample_report = {
        "timestamp": datetime.now().isoformat(),
        "predictions": {
            "DSWR": {
                "prediction": 45,
                "confidence": 0.72,
                "method": "ensemble"
            },
            "FRBD": {
                "prediction": 23,
                "confidence": 0.68,
                "method": "ensemble"
            },
            "GZBD": {
                "prediction": 67,
                "confidence": 0.75,
                "method": "ensemble"
            },
            "GALI": {
                "prediction": 89,
                "confidence": 0.71,
                "method": "ensemble"
            }
        },
        "analysis": {
            "DSWR": {
                "recent_values": [24, 59, 96, 32, 79, 48, 19, 93, 9, 26],
                "mean": 48.5,
                "trend": "increasing",
                "most_frequent": 24
            }
        },
        "summary": {
            "total_predictions": 4,
            "average_confidence": 0.715,
            "games_predicted": ["DSWR", "FRBD", "GZBD", "GALI"]
        }
    }
    
    print("Sample Prediction Report:")
    print(json.dumps(sample_report, indent=2))
    
    # Save report
    report_file = os.path.join(config.DATA_DIR, 'demo_report.json')
    with open(report_file, 'w') as f:
        json.dump(sample_report, f, indent=2)
    
    print(f"\nReport saved to: {report_file}")

def main():
    """Run the complete demo"""
    print("SATTA KING PREDICTION BOT - DEMO")
    print("="*60)
    print("This demo showcases the key features of the prediction bot:")
    print("1. Web scraping from Satta King websites")
    print("2. Data processing and feature engineering")
    print("3. Statistical analysis")
    print("4. Machine learning predictions")
    print("5. Report generation")
    
    try:
        # Demo 1: Web Scraping
        scraped_data = demo_scraping()
        
        # Demo 2: Data Processing
        if scraped_data is not None and len(scraped_data) > 0:
            processed_data = demo_data_processing(scraped_data)
            
            # Demo 3: Analysis
            demo_analysis(processed_data)
        
        # Demo 4: Prediction (using sample data)
        demo_prediction()
        
        # Demo 5: Report
        demo_report()
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\nTo use the full application:")
        print("1. Run 'python main.py full' for complete pipeline")
        print("2. Run 'streamlit run web_app.py' for web interface")
        print("3. Run 'python main.py predict' for quick predictions")
        
    except Exception as e:
        print(f"\nDemo error: {str(e)}")
        print("This is normal for a demo environment")

if __name__ == "__main__":
    main()
