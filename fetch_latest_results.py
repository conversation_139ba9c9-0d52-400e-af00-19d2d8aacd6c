"""
Fetch latest results to validate our predictions and improve the system
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from scraper import SattaKingScraper
import config

def fetch_latest_results():
    """Fetch the very latest results to check our predictions"""
    print("🔍 FETCHING LATEST RESULTS TO VALIDATE PREDICTIONS")
    print("=" * 60)
    
    scraper = SattaKingScraper()
    
    # Get current date and recent dates
    current_date = datetime.now()
    
    # Try to get the most recent data
    print("📡 Fetching latest data from website...")
    
    # Get current month
    current_month = current_date.strftime('%B')
    current_year = current_date.year
    
    print(f"Scraping {current_month} {current_year}...")
    latest_data = scraper.get_month_data(current_month, current_year)
    
    if latest_data:
        df = pd.DataFrame(latest_data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # Convert to numeric
        for game in config.GAMES:
            if game in df.columns:
                df[game] = pd.to_numeric(df[game], errors='coerce')
        
        print(f"✅ Found {len(df)} records")
        print(f"Latest date: {df['date'].max()}")
        
        # Show the most recent results
        print("\n📊 MOST RECENT RESULTS:")
        print("-" * 40)
        
        recent_results = df.tail(5)
        for _, row in recent_results.iterrows():
            date_str = row['date'].strftime('%Y-%m-%d')
            print(f"\n{date_str}:")
            for game in config.GAMES:
                if game in row and pd.notna(row[game]):
                    game_name = config.GAME_NAMES.get(game, game)
                    print(f"  {game_name}: {int(row[game]):02d}")
        
        return df
    else:
        print("❌ No latest data found")
        return None

def validate_our_predictions(latest_df):
    """Check how our previous predictions performed"""
    print("\n\n🎯 VALIDATING OUR PREVIOUS PREDICTIONS")
    print("=" * 50)
    
    # Load our previous predictions
    prediction_files = [f for f in os.listdir(config.DATA_DIR) 
                       if f.startswith('final_ensemble_predictions_')]
    
    if not prediction_files:
        print("❌ No previous predictions found")
        return
    
    latest_pred_file = max(prediction_files, 
                          key=lambda x: os.path.getmtime(os.path.join(config.DATA_DIR, x)))
    
    with open(os.path.join(config.DATA_DIR, latest_pred_file), 'r') as f:
        our_predictions = json.load(f)
    
    print(f"📋 Checking predictions from: {latest_pred_file}")
    
    # Get the most recent actual results
    if len(latest_df) == 0:
        print("❌ No actual results to compare against")
        return
    
    latest_actual = latest_df.iloc[-1]
    print(f"📅 Latest actual results ({latest_actual['date'].strftime('%Y-%m-%d')}):")
    
    validation_results = {}
    
    for game in config.GAMES:
        if game in our_predictions.get('predictions', {}) and game in latest_actual:
            predicted = our_predictions['predictions'][game]['ensemble_prediction']
            actual = int(latest_actual[game]) if pd.notna(latest_actual[game]) else None
            
            if actual is not None:
                error = abs(predicted - actual)
                accuracy_5 = error <= 5
                accuracy_10 = error <= 10
                
                game_name = config.GAME_NAMES.get(game, game)
                
                validation_results[game] = {
                    'predicted': predicted,
                    'actual': actual,
                    'error': error,
                    'accuracy_5': accuracy_5,
                    'accuracy_10': accuracy_10
                }
                
                status_5 = "✅" if accuracy_5 else "❌"
                status_10 = "✅" if accuracy_10 else "❌"
                
                print(f"\n{game_name}:")
                print(f"  Predicted: {predicted:02d}")
                print(f"  Actual: {actual:02d}")
                print(f"  Error: {error}")
                print(f"  Within ±5: {status_5}")
                print(f"  Within ±10: {status_10}")
    
    # Calculate overall accuracy
    if validation_results:
        total_games = len(validation_results)
        accuracy_5_count = sum(1 for r in validation_results.values() if r['accuracy_5'])
        accuracy_10_count = sum(1 for r in validation_results.values() if r['accuracy_10'])
        
        print(f"\n📊 OVERALL VALIDATION RESULTS:")
        print(f"  Accuracy within ±5: {accuracy_5_count}/{total_games} ({accuracy_5_count/total_games*100:.1f}%)")
        print(f"  Accuracy within ±10: {accuracy_10_count}/{total_games} ({accuracy_10_count/total_games*100:.1f}%)")
        print(f"  Average error: {np.mean([r['error'] for r in validation_results.values()]):.2f}")
    
    return validation_results

def scrape_extensive_historical_data():
    """Scrape much more historical data for better training"""
    print("\n\n📈 SCRAPING EXTENSIVE HISTORICAL DATA")
    print("=" * 50)
    
    scraper = SattaKingScraper()
    all_data = []
    
    # Scrape from 2020 to 2025 for more data
    start_year = 2020
    end_year = 2025
    
    print(f"🔄 Scraping data from {start_year} to {end_year}...")
    
    months = ['January', 'February', 'March', 'April', 'May', 'June',
              'July', 'August', 'September', 'October', 'November', 'December']
    
    total_months = (end_year - start_year + 1) * 12
    scraped_months = 0
    
    for year in range(start_year, end_year + 1):
        for month_idx, month in enumerate(months, 1):
            # Skip future months
            current_date = datetime.now()
            if year == current_date.year and month_idx > current_date.month:
                continue
            
            print(f"  Scraping {month} {year}...")
            data = scraper.get_month_data(month, year)
            
            if data:
                all_data.extend(data)
                print(f"    ✅ Found {len(data)} records")
            else:
                print(f"    ❌ No data")
            
            scraped_months += 1
            
            # Progress indicator
            if scraped_months % 12 == 0:
                print(f"    📊 Progress: {scraped_months}/{total_months} months completed")
    
    if all_data:
        df = pd.DataFrame(all_data)
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df = df.dropna(subset=['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # Clean data
        for game in config.GAMES:
            if game in df.columns:
                df[game] = pd.to_numeric(df[game], errors='coerce')
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['date'], keep='first')
        
        print(f"\n✅ EXTENSIVE DATA COLLECTED:")
        print(f"  Total records: {len(df)}")
        print(f"  Date range: {df['date'].min()} to {df['date'].max()}")
        print(f"  Years covered: {df['date'].dt.year.nunique()}")
        
        # Save the extensive dataset
        os.makedirs(config.RAW_DATA_DIR, exist_ok=True)
        extensive_file = os.path.join(config.RAW_DATA_DIR, 'extensive_historical_data.csv')
        df.to_csv(extensive_file, index=False)
        print(f"💾 Saved to: {extensive_file}")
        
        # Data quality report
        print(f"\n📊 DATA QUALITY REPORT:")
        for game in config.GAMES:
            if game in df.columns:
                game_data = df[game].dropna()
                coverage = len(game_data) / len(df) * 100
                game_name = config.GAME_NAMES.get(game, game)
                print(f"  {game_name}: {len(game_data)} records ({coverage:.1f}% coverage)")
        
        return df
    else:
        print("❌ No extensive data could be scraped")
        return None

def analyze_data_patterns(df):
    """Analyze patterns in the extensive dataset"""
    print("\n\n🔍 ANALYZING DATA PATTERNS")
    print("=" * 40)
    
    for game in config.GAMES:
        if game in df.columns:
            game_data = df[game].dropna()
            if len(game_data) > 0:
                game_name = config.GAME_NAMES.get(game, game)
                
                print(f"\n📈 {game_name} Analysis:")
                print(f"  Records: {len(game_data)}")
                print(f"  Range: {game_data.min():.0f} - {game_data.max():.0f}")
                print(f"  Mean: {game_data.mean():.2f}")
                print(f"  Std Dev: {game_data.std():.2f}")
                
                # Frequency analysis
                freq = game_data.value_counts().head(5)
                print(f"  Most frequent: {list(freq.index)}")
                
                # Pattern analysis
                even_pct = (game_data % 2 == 0).mean() * 100
                print(f"  Even numbers: {even_pct:.1f}%")
                
                # Range distribution
                low_pct = (game_data <= 33).mean() * 100
                mid_pct = ((game_data > 33) & (game_data <= 66)).mean() * 100
                high_pct = (game_data > 66).mean() * 100
                print(f"  Range distribution: Low {low_pct:.1f}%, Mid {mid_pct:.1f}%, High {high_pct:.1f}%")

def main():
    """Main function to fetch results and analyze"""
    print("🚀 FETCHING LATEST RESULTS & IMPROVING SYSTEM")
    print("=" * 60)
    
    # Step 1: Fetch latest results
    latest_df = fetch_latest_results()
    
    # Step 2: Validate our predictions
    if latest_df is not None:
        validation_results = validate_our_predictions(latest_df)
    
    # Step 3: Scrape extensive historical data
    extensive_df = scrape_extensive_historical_data()
    
    # Step 4: Analyze patterns
    if extensive_df is not None:
        analyze_data_patterns(extensive_df)
    
    print("\n" + "=" * 60)
    print("📋 DATA COLLECTION COMPLETE")
    print("=" * 60)
    print("✅ Fetched latest results for validation")
    print("✅ Scraped extensive historical data")
    print("✅ Analyzed data patterns and quality")
    print("\n🎯 Ready for advanced ML training!")

if __name__ == "__main__":
    main()
