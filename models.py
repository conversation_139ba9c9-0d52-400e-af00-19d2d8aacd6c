"""
Machine Learning models for Satta King prediction
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.svm import SVR
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
import joblib
import logging
import os
import config

logger = logging.getLogger(__name__)

class SattaPredictionModels:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_names = {}
        
    def create_lstm_model(self, sequence_length, features=1):
        """
        Create LSTM model for time series prediction
        """
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(sequence_length, features)),
            Dropout(0.2),
            LSTM(50, return_sequences=True),
            Dropout(0.2),
            LSTM(50),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        return model
    
    def train_traditional_models(self, X, y, game_name):
        """
        Train traditional ML models
        """
        logger.info(f"Training traditional models for {game_name}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=config.TEST_SIZE, random_state=config.RANDOM_STATE, shuffle=False
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        self.scalers[game_name] = scaler
        self.feature_names[game_name] = X.columns.tolist() if hasattr(X, 'columns') else list(range(X.shape[1]))
        
        # Define models
        models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=config.RANDOM_STATE),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=config.RANDOM_STATE),
            'linear_regression': LinearRegression(),
            'ridge': Ridge(alpha=1.0),
            'svr': SVR(kernel='rbf', C=1.0, gamma='scale')
        }
        
        results = {}
        
        for name, model in models.items():
            logger.info(f"Training {name} for {game_name}")
            
            # Train model
            if name == 'svr':
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
            
            # Evaluate
            mae = mean_absolute_error(y_test, y_pred)
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            results[name] = {
                'model': model,
                'mae': mae,
                'mse': mse,
                'r2': r2,
                'predictions': y_pred
            }
            
            logger.info(f"{name} - MAE: {mae:.2f}, MSE: {mse:.2f}, R2: {r2:.3f}")
        
        # Select best model based on MAE
        best_model_name = min(results.keys(), key=lambda x: results[x]['mae'])
        best_model = results[best_model_name]['model']
        
        self.models[f"{game_name}_traditional"] = {
            'model': best_model,
            'type': best_model_name,
            'scaler': scaler if best_model_name == 'svr' else None,
            'results': results
        }
        
        logger.info(f"Best traditional model for {game_name}: {best_model_name}")
        return results
    
    def train_lstm_model(self, sequences_X, sequences_y, game_name):
        """
        Train LSTM model for time series prediction
        """
        logger.info(f"Training LSTM model for {game_name}")
        
        # Reshape data for LSTM
        X = sequences_X.reshape((sequences_X.shape[0], sequences_X.shape[1], 1))
        y = sequences_y
        
        # Split data
        split_idx = int(len(X) * (1 - config.TEST_SIZE))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Scale data
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train.reshape(-1, X_train.shape[-1])).reshape(X_train.shape)
        X_test_scaled = scaler.transform(X_test.reshape(-1, X_test.shape[-1])).reshape(X_test.shape)
        
        # Create and train model
        model = self.create_lstm_model(X_train.shape[1], X_train.shape[2])
        
        # Train with early stopping
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        
        history = model.fit(
            X_train_scaled, y_train,
            epochs=100,
            batch_size=32,
            validation_data=(X_test_scaled, y_test),
            callbacks=[early_stopping],
            verbose=0
        )
        
        # Evaluate
        y_pred = model.predict(X_test_scaled)
        mae = mean_absolute_error(y_test, y_pred)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        self.models[f"{game_name}_lstm"] = {
            'model': model,
            'scaler': scaler,
            'history': history.history,
            'mae': mae,
            'mse': mse,
            'r2': r2
        }
        
        logger.info(f"LSTM model for {game_name} - MAE: {mae:.2f}, MSE: {mse:.2f}, R2: {r2:.3f}")
        return model, history
    
    def predict_next_number(self, game_name, recent_data=None, method='ensemble'):
        """
        Predict the next number for a specific game
        """
        if method == 'ensemble':
            predictions = []
            
            # Traditional model prediction
            if f"{game_name}_traditional" in self.models:
                trad_pred = self._predict_traditional(game_name, recent_data)
                if trad_pred is not None:
                    predictions.append(trad_pred)
            
            # LSTM model prediction
            if f"{game_name}_lstm" in self.models:
                lstm_pred = self._predict_lstm(game_name, recent_data)
                if lstm_pred is not None:
                    predictions.append(lstm_pred)
            
            if predictions:
                # Ensemble prediction (average)
                final_pred = np.mean(predictions)
                return round(final_pred)
            
        elif method == 'traditional' and f"{game_name}_traditional" in self.models:
            return self._predict_traditional(game_name, recent_data)
            
        elif method == 'lstm' and f"{game_name}_lstm" in self.models:
            return self._predict_lstm(game_name, recent_data)
        
        return None
    
    def _predict_traditional(self, game_name, recent_data):
        """
        Make prediction using traditional ML model
        """
        model_info = self.models.get(f"{game_name}_traditional")
        if not model_info:
            return None
        
        model = model_info['model']
        scaler = model_info.get('scaler')
        
        if recent_data is None:
            return None
        
        # Prepare features (this would need the same feature engineering as training)
        # For now, return a simple prediction
        if scaler:
            recent_data_scaled = scaler.transform(recent_data.reshape(1, -1))
            prediction = model.predict(recent_data_scaled)[0]
        else:
            prediction = model.predict(recent_data.reshape(1, -1))[0]
        
        return round(max(0, min(99, prediction)))
    
    def _predict_lstm(self, game_name, recent_sequence):
        """
        Make prediction using LSTM model
        """
        model_info = self.models.get(f"{game_name}_lstm")
        if not model_info:
            return None
        
        model = model_info['model']
        scaler = model_info['scaler']
        
        if recent_sequence is None or len(recent_sequence) < config.SEQUENCE_LENGTH:
            return None
        
        # Prepare sequence
        sequence = recent_sequence[-config.SEQUENCE_LENGTH:].reshape(1, config.SEQUENCE_LENGTH, 1)
        sequence_scaled = scaler.transform(sequence.reshape(-1, 1)).reshape(sequence.shape)
        
        prediction = model.predict(sequence_scaled)[0][0]
        return round(max(0, min(99, prediction)))
    
    def save_models(self, game_name):
        """
        Save trained models
        """
        # Save traditional model
        if f"{game_name}_traditional" in self.models:
            model_path = os.path.join(config.MODELS_DIR, f"{game_name}_traditional.joblib")
            joblib.dump(self.models[f"{game_name}_traditional"], model_path)
            logger.info(f"Saved traditional model for {game_name}")
        
        # Save LSTM model
        if f"{game_name}_lstm" in self.models:
            model_path = os.path.join(config.MODELS_DIR, f"{game_name}_lstm.h5")
            self.models[f"{game_name}_lstm"]['model'].save(model_path)
            
            # Save scaler separately
            scaler_path = os.path.join(config.MODELS_DIR, f"{game_name}_lstm_scaler.joblib")
            joblib.dump(self.models[f"{game_name}_lstm"]['scaler'], scaler_path)
            logger.info(f"Saved LSTM model for {game_name}")
    
    def load_models(self, game_name):
        """
        Load saved models
        """
        # Load traditional model
        model_path = os.path.join(config.MODELS_DIR, f"{game_name}_traditional.joblib")
        if os.path.exists(model_path):
            self.models[f"{game_name}_traditional"] = joblib.load(model_path)
            logger.info(f"Loaded traditional model for {game_name}")
        
        # Load LSTM model
        model_path = os.path.join(config.MODELS_DIR, f"{game_name}_lstm.h5")
        scaler_path = os.path.join(config.MODELS_DIR, f"{game_name}_lstm_scaler.joblib")
        
        if os.path.exists(model_path) and os.path.exists(scaler_path):
            model = tf.keras.models.load_model(model_path)
            scaler = joblib.load(scaler_path)
            
            self.models[f"{game_name}_lstm"] = {
                'model': model,
                'scaler': scaler
            }
            logger.info(f"Loaded LSTM model for {game_name}")

if __name__ == "__main__":
    # Example usage
    models = SattaPredictionModels()
    print("Models class initialized")
