"""
Main prediction interface for Satta King bot
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import json
from data_processor import SattaDataProcessor
from models import SattaPredictionModels
import config

logger = logging.getLogger(__name__)

class SattaPredictor:
    def __init__(self):
        self.processor = SattaDataProcessor()
        self.models = SattaPredictionModels()
        self.data = None
        self.is_trained = False
        
    def load_and_prepare_data(self, data_path=None):
        """
        Load and prepare data for training/prediction
        """
        logger.info("Loading and preparing data...")
        
        if data_path is None:
            # Check if processed data exists
            processed_file = os.path.join(config.PROCESSED_DATA_DIR, 'processed_satta_data.csv')
            if os.path.exists(processed_file):
                self.data = pd.read_csv(processed_file)
                self.data['date'] = pd.to_datetime(self.data['date'])
            else:
                # Process raw data
                self.data, _ = self.processor.process_all()
        else:
            self.data = pd.read_csv(data_path)
            self.data['date'] = pd.to_datetime(self.data['date'])
        
        logger.info(f"Data loaded. Shape: {self.data.shape}")
        return self.data
    
    def train_models(self, games=None):
        """
        Train prediction models for specified games
        """
        if self.data is None:
            self.load_and_prepare_data()
        
        if games is None:
            games = config.GAMES
        
        logger.info(f"Training models for games: {games}")
        
        for game in games:
            if game not in self.data.columns:
                logger.warning(f"Game {game} not found in data")
                continue
            
            logger.info(f"Training models for {game}")
            
            try:
                # Prepare data for traditional ML models
                X, y, feature_cols = self.processor.prepare_ml_data(self.data, game)
                
                if len(X) > 0:
                    # Train traditional models
                    self.models.train_traditional_models(X, y, game)
                    
                    # Prepare sequences for LSTM
                    sequences_X, sequences_y = self.processor.create_sequences(self.data, game)
                    
                    if len(sequences_X) > 0:
                        # Train LSTM model
                        self.models.train_lstm_model(sequences_X, sequences_y, game)
                    
                    # Save models
                    self.models.save_models(game)
                    
                    logger.info(f"Successfully trained models for {game}")
                else:
                    logger.warning(f"No valid data for training {game}")
                    
            except Exception as e:
                logger.error(f"Error training models for {game}: {str(e)}")
        
        self.is_trained = True
        logger.info("Model training completed")
    
    def predict_next_numbers(self, games=None, method='ensemble'):
        """
        Predict next numbers for specified games
        """
        if games is None:
            games = config.GAMES
        
        if not self.is_trained:
            # Try to load existing models
            self.load_models(games)
        
        predictions = {}
        
        for game in games:
            try:
                # Get recent data for the game
                recent_data = self._get_recent_data(game)
                
                if recent_data is not None:
                    prediction = self.models.predict_next_number(game, recent_data, method)
                    
                    if prediction is not None:
                        predictions[game] = {
                            'prediction': int(prediction),
                            'confidence': self._calculate_confidence(game, prediction),
                            'method': method
                        }
                        logger.info(f"Predicted {game}: {prediction}")
                    else:
                        logger.warning(f"Could not generate prediction for {game}")
                else:
                    logger.warning(f"No recent data available for {game}")
                    
            except Exception as e:
                logger.error(f"Error predicting {game}: {str(e)}")
        
        return predictions
    
    def _get_recent_data(self, game):
        """
        Get recent data for prediction
        """
        if self.data is None or game not in self.data.columns:
            return None
        
        # Get last 30 non-null values
        recent_values = self.data[game].dropna().tail(config.SEQUENCE_LENGTH)
        
        if len(recent_values) >= config.SEQUENCE_LENGTH:
            return recent_values.values
        
        return None
    
    def _calculate_confidence(self, game, prediction):
        """
        Calculate confidence score for prediction
        """
        # Simple confidence calculation based on model performance
        # This could be enhanced with more sophisticated methods
        
        base_confidence = 0.5
        
        # Check if we have model performance metrics
        if f"{game}_traditional" in self.models.models:
            trad_results = self.models.models[f"{game}_traditional"].get('results', {})
            if trad_results:
                best_r2 = max([r.get('r2', 0) for r in trad_results.values()])
                base_confidence += best_r2 * 0.3
        
        if f"{game}_lstm" in self.models.models:
            lstm_r2 = self.models.models[f"{game}_lstm"].get('r2', 0)
            base_confidence += lstm_r2 * 0.2
        
        return min(1.0, max(0.1, base_confidence))
    
    def load_models(self, games=None):
        """
        Load pre-trained models
        """
        if games is None:
            games = config.GAMES
        
        for game in games:
            try:
                self.models.load_models(game)
            except Exception as e:
                logger.warning(f"Could not load models for {game}: {str(e)}")
        
        self.is_trained = True
    
    def get_analysis(self, game, days=30):
        """
        Get analysis of recent patterns for a game
        """
        if self.data is None or game not in self.data.columns:
            return None
        
        recent_data = self.data[game].dropna().tail(days)
        
        if len(recent_data) == 0:
            return None
        
        analysis = {
            'game': game,
            'recent_values': recent_data.tolist(),
            'mean': float(recent_data.mean()),
            'std': float(recent_data.std()),
            'min': int(recent_data.min()),
            'max': int(recent_data.max()),
            'most_frequent': int(recent_data.mode().iloc[0]) if len(recent_data.mode()) > 0 else None,
            'trend': 'increasing' if recent_data.iloc[-1] > recent_data.iloc[0] else 'decreasing',
            'last_value': int(recent_data.iloc[-1]),
            'days_analyzed': len(recent_data)
        }
        
        return analysis
    
    def generate_report(self, games=None):
        """
        Generate comprehensive prediction report
        """
        if games is None:
            games = config.GAMES
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'predictions': self.predict_next_numbers(games),
            'analysis': {},
            'summary': {}
        }
        
        # Add analysis for each game
        for game in games:
            analysis = self.get_analysis(game)
            if analysis:
                report['analysis'][game] = analysis
        
        # Add summary statistics
        if report['predictions']:
            avg_confidence = np.mean([p['confidence'] for p in report['predictions'].values()])
            report['summary'] = {
                'total_predictions': len(report['predictions']),
                'average_confidence': float(avg_confidence),
                'games_predicted': list(report['predictions'].keys())
            }
        
        return report
    
    def save_report(self, report, filename=None):
        """
        Save prediction report to file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"prediction_report_{timestamp}.json"
        
        filepath = os.path.join(config.DATA_DIR, filename)
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Report saved to {filepath}")
        return filepath

if __name__ == "__main__":
    predictor = SattaPredictor()
    
    # Load data
    predictor.load_and_prepare_data()
    
    # Train models
    predictor.train_models()
    
    # Generate predictions
    report = predictor.generate_report()
    
    # Save report
    predictor.save_report(report)
    
    print("Prediction report generated!")
    print(json.dumps(report, indent=2))
