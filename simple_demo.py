"""
Simple demo script for Satta King scraper (without ML dependencies)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

from scraper import Satta<PERSON>ingScraper
import config

def demo_scraping():
    """Demonstrate web scraping functionality"""
    print("SATTA KING SCRAPER DEMO")
    print("="*50)
    
    scraper = SattaKingScraper()
    
    # Test scraping recent months
    print("Testing web scraping functionality...")
    print("Scraping data from Satta King website...")
    
    recent_months = [
        ("June", 2025),
        ("May", 2025)
    ]
    
    all_data = []
    for month, year in recent_months:
        print(f"\nScraping {month} {year}...")
        data = scraper.get_month_data(month, year)
        if data:
            all_data.extend(data)
            print(f"  ✓ Found {len(data)} records")
            
            # Show sample data
            print("  Sample records:")
            for i, record in enumerate(data[:3]):
                print(f"    {record['date']}: DSWR={record.get('DSWR', 'XX')}, "
                      f"FRBD={record.get('FRBD', 'XX')}, "
                      f"GZBD={record.get('GZBD', 'XX')}, "
                      f"GALI={record.get('GALI', 'XX')}")
        else:
            print(f"  ✗ No data found for {month} {year}")
    
    if all_data:
        # Save data
        df = pd.DataFrame(all_data)
        os.makedirs(config.RAW_DATA_DIR, exist_ok=True)
        output_file = os.path.join(config.RAW_DATA_DIR, 'demo_scraped_data.csv')
        df.to_csv(output_file, index=False)
        
        print(f"\n✓ Total records scraped: {len(all_data)}")
        print(f"✓ Data saved to: {output_file}")
        
        # Basic analysis
        print("\nBASIC DATA ANALYSIS")
        print("-" * 30)
        
        for game in config.GAMES:
            if game in df.columns:
                game_data = df[game].dropna()
                if len(game_data) > 0:
                    game_name = config.GAME_NAMES.get(game, game)
                    print(f"\n{game_name}:")
                    print(f"  Records: {len(game_data)}")
                    print(f"  Range: {game_data.min()} - {game_data.max()}")
                    print(f"  Average: {game_data.mean():.1f}")
                    print(f"  Most recent: {game_data.iloc[-1] if len(game_data) > 0 else 'N/A'}")
                    
                    # Most frequent numbers
                    freq = game_data.value_counts().head(3)
                    print(f"  Most frequent: {list(freq.index)}")
        
        return df
    else:
        print("\n✗ No data could be scraped")
        return None

def demo_simple_prediction(data):
    """Simple prediction without ML"""
    print("\n\nSIMPLE PREDICTION DEMO")
    print("="*50)
    print("Using basic statistical methods for prediction...")
    
    predictions = {}
    
    for game in config.GAMES:
        if game in data.columns:
            game_data = data[game].dropna()
            if len(game_data) >= 10:  # Need at least 10 records
                game_name = config.GAME_NAMES.get(game, game)
                
                # Simple prediction methods
                recent_values = game_data.tail(10).values
                
                # Method 1: Average of recent values
                avg_prediction = int(np.mean(recent_values))
                
                # Method 2: Most frequent in recent data
                freq_prediction = int(pd.Series(recent_values).mode().iloc[0])
                
                # Method 3: Trend-based (simple linear trend)
                if len(recent_values) >= 5:
                    x = np.arange(len(recent_values))
                    trend = np.polyfit(x, recent_values, 1)[0]
                    trend_prediction = int(recent_values[-1] + trend)
                    trend_prediction = max(0, min(99, trend_prediction))  # Keep in 0-99 range
                else:
                    trend_prediction = avg_prediction
                
                # Ensemble prediction (average of methods)
                ensemble_prediction = int(np.mean([avg_prediction, freq_prediction, trend_prediction]))
                ensemble_prediction = max(0, min(99, ensemble_prediction))
                
                predictions[game] = {
                    'game_name': game_name,
                    'prediction': ensemble_prediction,
                    'methods': {
                        'average': avg_prediction,
                        'frequency': freq_prediction,
                        'trend': trend_prediction
                    },
                    'recent_values': recent_values.tolist(),
                    'confidence': 'Medium'  # Simple confidence
                }
    
    # Display predictions
    if predictions:
        print("\nPREDICTION RESULTS")
        print("-" * 30)
        for game, pred_info in predictions.items():
            print(f"\n{pred_info['game_name']}:")
            print(f"  Predicted Number: {pred_info['prediction']:02d}")
            print(f"  Confidence: {pred_info['confidence']}")
            print(f"  Recent Values: {pred_info['recent_values'][-5:]}")  # Last 5
            print(f"  Method Details:")
            print(f"    Average-based: {pred_info['methods']['average']}")
            print(f"    Frequency-based: {pred_info['methods']['frequency']}")
            print(f"    Trend-based: {pred_info['methods']['trend']}")
    
    return predictions

def demo_report(data, predictions):
    """Generate a simple report"""
    print("\n\nGENERATING REPORT")
    print("="*50)
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "data_summary": {
            "total_records": len(data),
            "date_range": {
                "start": data['date'].min() if 'date' in data.columns else None,
                "end": data['date'].max() if 'date' in data.columns else None
            },
            "games_available": [game for game in config.GAMES if game in data.columns]
        },
        "predictions": {}
    }
    
    # Add predictions to report
    for game, pred_info in predictions.items():
        report["predictions"][game] = {
            "game_name": pred_info['game_name'],
            "prediction": pred_info['prediction'],
            "confidence": pred_info['confidence'],
            "recent_values": pred_info['recent_values'][-10:]  # Last 10 values
        }
    
    # Save report
    os.makedirs(config.DATA_DIR, exist_ok=True)
    report_file = os.path.join(config.DATA_DIR, 'simple_demo_report.json')
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"✓ Report saved to: {report_file}")
    
    # Display summary
    print("\nREPORT SUMMARY")
    print("-" * 20)
    print(f"Total Records: {report['data_summary']['total_records']}")
    print(f"Date Range: {report['data_summary']['date_range']['start']} to {report['data_summary']['date_range']['end']}")
    print(f"Games Analyzed: {len(report['predictions'])}")
    print(f"Predictions Generated: {len([p for p in report['predictions'].values() if p['prediction']])}")
    
    return report

def main():
    """Run the simple demo"""
    print("SATTA KING PREDICTION BOT - SIMPLE DEMO")
    print("="*60)
    print("This demo shows basic functionality without ML dependencies:")
    print("• Web scraping from Satta King websites")
    print("• Data analysis and statistics")
    print("• Simple prediction methods")
    print("• Report generation")
    print()
    
    try:
        # Step 1: Scrape data
        data = demo_scraping()
        
        if data is not None and len(data) > 0:
            # Step 2: Simple predictions
            predictions = demo_simple_prediction(data)
            
            # Step 3: Generate report
            if predictions:
                report = demo_report(data, predictions)
                
                print("\n" + "="*60)
                print("DEMO COMPLETED SUCCESSFULLY!")
                print("="*60)
                print("\nNext Steps:")
                print("1. Install TensorFlow for advanced ML predictions:")
                print("   pip install tensorflow")
                print("2. Run full pipeline: python main.py full")
                print("3. Try web interface: streamlit run web_app.py")
                
            else:
                print("\n✗ Could not generate predictions")
        else:
            print("\n✗ No data available for analysis")
            
    except Exception as e:
        print(f"\nDemo error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
