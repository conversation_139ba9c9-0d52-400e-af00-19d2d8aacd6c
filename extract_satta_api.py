"""
Extract and access the actual Satta King APIs
Attempt to get raw data from the discovered API endpoints
"""

import requests
import json
import time
from datetime import datetime
import re
from bs4 import BeautifulSoup
import os

class SattaAPIExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/html, application/xml, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        # Discovered working API endpoints
        self.api_endpoints = [
            "https://sattaking.com/api/results",
            "https://sattaking.com/api/data", 
            "https://sattaking.com/api/latest",
            "https://sattaking.com/json/results",
            "https://sattaking.com/ajax/data",
            "https://sattamatka.com/api/results",
            "https://sattamatka.com/api/data",
            "https://sattamatka.com/result.php",
            "https://sattamatka.com/data.php"
        ]
        
    def extract_api_data(self):
        """Extract data from all discovered API endpoints"""
        
        print("🔍 EXTRACTING SATTA KING API DATA")
        print("=" * 60)
        
        api_data = {}
        
        for endpoint in self.api_endpoints:
            print(f"\n📡 Testing: {endpoint}")
            print("-" * 50)
            
            try:
                # Try different HTTP methods
                methods = ['GET', 'POST']
                
                for method in methods:
                    print(f"   Trying {method} request...")
                    
                    if method == 'GET':
                        response = self.session.get(endpoint, timeout=15)
                    else:
                        response = self.session.post(endpoint, timeout=15)
                    
                    result = self.analyze_api_response(endpoint, method, response)
                    
                    if result['has_data']:
                        api_data[f"{endpoint}_{method}"] = result
                        print(f"   ✅ {method}: Found data!")
                        break
                    else:
                        print(f"   ❌ {method}: No useful data")
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                continue
            
            time.sleep(1)  # Rate limiting
        
        # Try to find hidden/undocumented endpoints
        print(f"\n🔍 SEARCHING FOR HIDDEN ENDPOINTS")
        print("-" * 50)
        
        hidden_endpoints = self.discover_hidden_endpoints()
        api_data.update(hidden_endpoints)
        
        # Try to reverse engineer API parameters
        print(f"\n🔧 REVERSE ENGINEERING API PARAMETERS")
        print("-" * 50)
        
        parameterized_data = self.test_api_parameters()
        api_data.update(parameterized_data)
        
        # Save extracted data
        self.save_api_data(api_data)
        
        return api_data
    
    def analyze_api_response(self, endpoint, method, response):
        """Analyze API response for useful data"""
        
        result = {
            'endpoint': endpoint,
            'method': method,
            'status_code': response.status_code,
            'content_type': response.headers.get('content-type', ''),
            'has_data': False,
            'data_type': 'unknown',
            'extracted_data': {},
            'raw_content': '',
            'satta_results': {}
        }
        
        if response.status_code != 200:
            return result
        
        content = response.text
        result['raw_content'] = content[:1000]  # First 1000 chars
        
        # Try to parse as JSON
        try:
            json_data = response.json()
            result['has_data'] = True
            result['data_type'] = 'json'
            result['extracted_data'] = json_data
            
            # Look for satta results in JSON
            self.extract_satta_from_json(json_data, result)
            
            print(f"   📊 JSON data found: {len(str(json_data))} chars")
            return result
            
        except:
            pass
        
        # Try to parse as XML
        if 'xml' in response.headers.get('content-type', '').lower():
            try:
                import xml.etree.ElementTree as ET
                root = ET.fromstring(content)
                result['has_data'] = True
                result['data_type'] = 'xml'
                result['extracted_data'] = self.xml_to_dict(root)
                
                print(f"   📊 XML data found")
                return result
            except:
                pass
        
        # Parse as HTML and look for structured data
        if 'html' in response.headers.get('content-type', '').lower():
            soup = BeautifulSoup(content, 'html.parser')
            
            # Look for JSON in script tags
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    json_matches = re.findall(r'({[^{}]*(?:"result"|"data"|"satta")[^{}]*})', script.string)
                    for match in json_matches:
                        try:
                            json_data = json.loads(match)
                            result['has_data'] = True
                            result['data_type'] = 'embedded_json'
                            result['extracted_data'] = json_data
                            
                            print(f"   📊 Embedded JSON found in script")
                            return result
                        except:
                            continue
            
            # Look for data tables
            tables = soup.find_all('table')
            if tables:
                table_data = self.extract_table_data(tables[0])
                if table_data:
                    result['has_data'] = True
                    result['data_type'] = 'html_table'
                    result['extracted_data'] = table_data
                    
                    print(f"   📊 Table data found: {len(table_data)} rows")
                    return result
            
            # Look for satta results in text
            satta_results = self.extract_satta_from_text(content)
            if satta_results:
                result['has_data'] = True
                result['data_type'] = 'text_results'
                result['satta_results'] = satta_results
                
                print(f"   📊 Satta results found: {len(satta_results)} games")
                return result
        
        return result
    
    def extract_satta_from_json(self, json_data, result):
        """Extract satta results from JSON data"""
        
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        def search_json(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Check if key matches a game name
                    for game in games:
                        if game.lower() in key.lower():
                            result['satta_results'][game] = value
                    
                    search_json(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_json(item, f"{path}[{i}]")
        
        search_json(json_data)
    
    def extract_table_data(self, table):
        """Extract data from HTML table"""
        
        table_data = []
        rows = table.find_all('tr')
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if cells:
                row_data = [cell.get_text().strip() for cell in cells]
                table_data.append(row_data)
        
        return table_data
    
    def extract_satta_from_text(self, content):
        """Extract satta results from text content"""
        
        results = {}
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        for game in games:
            # Look for patterns like "GALI: 45" or "DSWR 23"
            patterns = [
                rf'{game}[:\s]*(\d{{1,2}})',
                rf'"{game}"[:\s]*"?(\d{{1,2}})"?',
                rf"'{game}'[:\s]*'?(\d{{1,2}})'?"
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    results[game] = matches[0]
                    break
        
        return results
    
    def discover_hidden_endpoints(self):
        """Try to discover hidden or undocumented API endpoints"""
        
        hidden_data = {}
        base_urls = ["https://sattaking.com", "https://sattamatka.com"]
        
        # Common API path patterns
        api_paths = [
            "/api/live", "/api/current", "/api/today", "/api/now",
            "/data/live", "/data/current", "/data/today",
            "/result/live", "/result/current", "/result/today",
            "/json/live", "/json/current", "/json/today",
            "/ajax/live", "/ajax/current", "/ajax/today",
            "/api/v1/results", "/api/v2/results",
            "/rest/results", "/service/results"
        ]
        
        for base_url in base_urls:
            for path in api_paths:
                try:
                    url = f"{base_url}{path}"
                    response = self.session.get(url, timeout=5)
                    
                    if response.status_code == 200:
                        result = self.analyze_api_response(url, 'GET', response)
                        if result['has_data']:
                            hidden_data[url] = result
                            print(f"   ✅ Found hidden endpoint: {url}")
                
                except:
                    continue
                
                time.sleep(0.2)
        
        return hidden_data
    
    def test_api_parameters(self):
        """Test API endpoints with different parameters"""
        
        param_data = {}
        
        # Test common parameters
        test_params = [
            {'format': 'json'},
            {'type': 'json'},
            {'output': 'json'},
            {'game': 'gali'},
            {'game': 'dswr'},
            {'date': datetime.now().strftime('%Y-%m-%d')},
            {'today': '1'},
            {'live': '1'},
            {'current': '1'}
        ]
        
        working_endpoints = [
            "https://sattaking.com/api/results",
            "https://sattaking.com/api/data"
        ]
        
        for endpoint in working_endpoints:
            for params in test_params:
                try:
                    response = self.session.get(endpoint, params=params, timeout=5)
                    
                    if response.status_code == 200:
                        result = self.analyze_api_response(endpoint, 'GET_PARAMS', response)
                        if result['has_data']:
                            param_key = f"{endpoint}?{self.dict_to_query(params)}"
                            param_data[param_key] = result
                            print(f"   ✅ Parameters work: {param_key}")
                
                except:
                    continue
                
                time.sleep(0.3)
        
        return param_data
    
    def dict_to_query(self, params):
        """Convert dict to query string"""
        return "&".join([f"{k}={v}" for k, v in params.items()])
    
    def xml_to_dict(self, element):
        """Convert XML element to dictionary"""
        result = {}
        
        if element.text and element.text.strip():
            result['text'] = element.text.strip()
        
        for child in element:
            child_data = self.xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def save_api_data(self, api_data):
        """Save extracted API data"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_endpoints_tested': len(self.api_endpoints),
            'working_endpoints': len([d for d in api_data.values() if d.get('has_data', False)]),
            'api_data': api_data
        }
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/extracted_api_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 API data saved to: {report_file}")
        
        # Print summary
        self.print_api_summary(api_data)
        
        return report_file
    
    def print_api_summary(self, api_data):
        """Print summary of extracted API data"""
        
        print(f"\n📋 API EXTRACTION SUMMARY")
        print("=" * 60)
        
        working_apis = [k for k, v in api_data.items() if v.get('has_data', False)]
        
        print(f"Working APIs Found: {len(working_apis)}")
        print(f"Total Endpoints Tested: {len(self.api_endpoints)}")
        
        if working_apis:
            print(f"\n✅ WORKING API ENDPOINTS:")
            for api in working_apis:
                data = api_data[api]
                print(f"  • {api}")
                print(f"    Type: {data.get('data_type', 'unknown')}")
                print(f"    Status: {data.get('status_code', 'unknown')}")
                
                # Show satta results if found
                satta_results = data.get('satta_results', {})
                if satta_results:
                    print(f"    Results: {satta_results}")
                
                # Show sample data
                extracted = data.get('extracted_data', {})
                if extracted:
                    sample = str(extracted)[:100]
                    print(f"    Sample: {sample}...")
        
        else:
            print(f"\n❌ No working API endpoints found with structured data")
            print(f"   Most endpoints return HTML pages instead of API data")

if __name__ == "__main__":
    print("🚀 Starting Satta King API extraction...")
    
    extractor = SattaAPIExtractor()
    api_data = extractor.extract_api_data()
    
    print("\n✅ API extraction complete!")
    
    # Show any working APIs found
    working_apis = [k for k, v in api_data.items() if v.get('has_data', False)]
    
    if working_apis:
        print(f"\n🎯 FOUND {len(working_apis)} WORKING API ENDPOINTS!")
        print("You can now use these APIs to get Satta King data programmatically.")
    else:
        print(f"\n⚠️ No direct API access found.")
        print("The sites may be using internal APIs or different authentication methods.")
