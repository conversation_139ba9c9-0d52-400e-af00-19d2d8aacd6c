"""
Test discovered API endpoints to understand data format and sources
"""

import requests
import json
import time
from datetime import datetime

def test_api_endpoints():
    """Test the discovered API endpoints"""
    
    print("🔍 TESTING SATTA KING API ENDPOINTS")
    print("=" * 60)
    
    # Discovered API endpoints from investigation
    endpoints = [
        "https://sattaking.com/api/results",
        "https://sattaking.com/api/data", 
        "https://sattaking.com/api/latest",
        "https://sattaking.com/data.php",
        "https://sattaking.com/result.php",
        "https://sattaking.com/api.php",
        "https://sattaking.com/json/results",
        "https://sattaking.com/ajax/data",
        "https://sattamatka.com/api/results",
        "https://sattamatka.com/api/data",
        "https://sattamatka.com/data.php",
        "https://sattamatka.com/result.php"
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/html, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://sattaking.com/'
    })
    
    results = {}
    
    for endpoint in endpoints:
        print(f"\n🔍 Testing: {endpoint}")
        print("-" * 50)
        
        try:
            # Test GET request
            response = session.get(endpoint, timeout=10)
            
            endpoint_result = {
                'url': endpoint,
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'content_length': len(response.content),
                'response_time': response.elapsed.total_seconds(),
                'headers': dict(response.headers),
                'content_preview': '',
                'is_json': False,
                'is_html': False,
                'contains_results': False,
                'data_structure': 'unknown'
            }
            
            # Analyze content
            content = response.text[:2000]  # First 2000 chars
            endpoint_result['content_preview'] = content
            
            # Check if JSON
            try:
                json_data = response.json()
                endpoint_result['is_json'] = True
                endpoint_result['data_structure'] = 'json'
                endpoint_result['json_preview'] = json_data
                print(f"   ✅ JSON Response: {len(str(json_data))} chars")
                
                # Look for satta results in JSON
                if isinstance(json_data, dict):
                    for key, value in json_data.items():
                        if any(game in str(key).upper() for game in ['GALI', 'DSWR', 'FRBD', 'GZBD']):
                            endpoint_result['contains_results'] = True
                            print(f"   🎯 Contains game results: {key} = {value}")
                
            except:
                # Not JSON, check if HTML
                if 'text/html' in response.headers.get('content-type', ''):
                    endpoint_result['is_html'] = True
                    endpoint_result['data_structure'] = 'html'
                    
                    # Look for satta results in HTML
                    games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
                    for game in games:
                        if game in content.upper():
                            endpoint_result['contains_results'] = True
                            print(f"   🎯 Contains {game} results in HTML")
                            break
                
                print(f"   📄 HTML/Text Response: {len(content)} chars")
            
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
            print(f"   Size: {len(response.content)} bytes")
            
            # Show content preview
            if endpoint_result['is_json']:
                print(f"   Preview: {str(endpoint_result.get('json_preview', {}))[:200]}...")
            else:
                clean_content = content.replace('\n', ' ').replace('\r', ' ')[:200]
                print(f"   Preview: {clean_content}...")
            
            results[endpoint] = endpoint_result
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results[endpoint] = {
                'url': endpoint,
                'error': str(e),
                'accessible': False
            }
        
        time.sleep(1)  # Rate limiting
    
    # Analyze patterns
    print("\n📊 ANALYSIS SUMMARY")
    print("=" * 60)
    
    json_endpoints = [url for url, data in results.items() if data.get('is_json', False)]
    html_endpoints = [url for url, data in results.items() if data.get('is_html', False)]
    result_endpoints = [url for url, data in results.items() if data.get('contains_results', False)]
    
    print(f"JSON Endpoints: {len(json_endpoints)}")
    print(f"HTML Endpoints: {len(html_endpoints)}")
    print(f"Endpoints with Results: {len(result_endpoints)}")
    
    if json_endpoints:
        print("\n🔍 JSON ENDPOINTS:")
        for endpoint in json_endpoints:
            print(f"  • {endpoint}")
    
    if result_endpoints:
        print("\n🎯 ENDPOINTS WITH SATTA RESULTS:")
        for endpoint in result_endpoints:
            print(f"  • {endpoint}")
    
    # Save detailed results
    report = {
        'timestamp': datetime.now().isoformat(),
        'endpoints_tested': len(endpoints),
        'successful_responses': len([r for r in results.values() if not r.get('error')]),
        'json_endpoints': len(json_endpoints),
        'result_endpoints': len(result_endpoints),
        'detailed_results': results
    }
    
    import os
    os.makedirs('data', exist_ok=True)
    report_file = f'data/api_endpoint_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed test results saved to: {report_file}")
    
    return results

def test_specific_patterns():
    """Test for specific data patterns that might indicate shared sources"""
    
    print("\n🔍 TESTING FOR SHARED DATA PATTERNS")
    print("=" * 60)
    
    # Test multiple sites at the same time to see if they have identical data
    sites = [
        "https://satta-king-fast.com",
        "https://sattaking.com", 
        "https://sattakinggali.com",
        "https://sattakingdesawar.com"
    ]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    timestamp = datetime.now()
    site_data = {}
    
    for site in sites:
        try:
            print(f"📊 Fetching data from: {site}")
            response = session.get(site, timeout=10)
            
            # Extract any visible numbers that could be results
            import re
            content = response.text
            
            # Look for 2-digit numbers that could be satta results
            numbers = re.findall(r'\b\d{2}\b', content)
            
            # Look for specific game patterns
            games_found = {}
            games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
            
            for game in games:
                pattern = rf'{game}[:\s]*(\d{{1,2}})'
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    games_found[game] = match.group(1)
            
            site_data[site] = {
                'timestamp': timestamp.isoformat(),
                'all_numbers': numbers[:20],  # First 20 numbers found
                'game_results': games_found,
                'response_time': response.elapsed.total_seconds()
            }
            
            print(f"   Found {len(numbers)} numbers, {len(games_found)} game results")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            site_data[site] = {'error': str(e)}
        
        time.sleep(0.5)
    
    # Compare data across sites
    print("\n🔍 CROSS-SITE DATA COMPARISON")
    print("-" * 40)
    
    # Find common numbers
    all_numbers = []
    for site, data in site_data.items():
        if 'all_numbers' in data:
            all_numbers.extend(data['all_numbers'])
    
    from collections import Counter
    number_counts = Counter(all_numbers)
    common_numbers = {num: count for num, count in number_counts.items() if count > 1}
    
    print(f"Common numbers across sites: {len(common_numbers)}")
    if common_numbers:
        for num, count in sorted(common_numbers.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {num}: appears {count} times")
    
    # Compare game results
    all_games = set()
    for site, data in site_data.items():
        if 'game_results' in data:
            all_games.update(data['game_results'].keys())
    
    print(f"\nGame result comparison:")
    for game in all_games:
        game_results = {}
        for site, data in site_data.items():
            if 'game_results' in data and game in data['game_results']:
                game_results[site] = data['game_results'][game]
        
        if len(game_results) > 1:
            unique_results = set(game_results.values())
            if len(unique_results) == 1:
                print(f"  🎯 {game}: IDENTICAL across {len(game_results)} sites = {list(unique_results)[0]}")
            else:
                print(f"  ⚠️ {game}: DIFFERENT results = {game_results}")
    
    return site_data

if __name__ == "__main__":
    print("🚀 Starting API endpoint testing...")
    
    # Test discovered API endpoints
    api_results = test_api_endpoints()
    
    # Test for shared data patterns
    pattern_results = test_specific_patterns()
    
    print("\n✅ Testing complete!")
