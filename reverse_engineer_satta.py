"""
Reverse engineering analysis of Satta King website to understand data sources and number generation
"""

import requests
import time
import json
import re
import os
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import hashlib
from datetime import datetime
import config

class SattaReverseEngineer:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.base_url = "https://satta-king-fast.com"
        self.findings = {}
        
    def analyze_main_page(self):
        """Analyze the main page structure and functionality"""
        print("🔍 ANALYZING MAIN PAGE STRUCTURE")
        print("-" * 50)
        
        try:
            response = self.session.get(self.base_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract page structure
            self.findings['main_page'] = {
                'title': soup.title.string if soup.title else 'No title',
                'meta_description': self._get_meta_content(soup, 'description'),
                'meta_keywords': self._get_meta_content(soup, 'keywords'),
                'scripts': self._extract_scripts(soup),
                'external_resources': self._extract_external_resources(soup),
                'forms': self._analyze_forms(soup),
                'ajax_endpoints': self._find_ajax_endpoints(response.text)
            }
            
            print(f"✅ Main page analyzed")
            print(f"   Title: {self.findings['main_page']['title']}")
            print(f"   Scripts found: {len(self.findings['main_page']['scripts'])}")
            print(f"   External resources: {len(self.findings['main_page']['external_resources'])}")
            print(f"   Forms: {len(self.findings['main_page']['forms'])}")
            print(f"   AJAX endpoints: {len(self.findings['main_page']['ajax_endpoints'])}")
            
            return soup
            
        except Exception as e:
            print(f"❌ Error analyzing main page: {str(e)}")
            return None
    
    def analyze_chart_page(self):
        """Analyze the chart page where data is displayed"""
        print("\n🔍 ANALYZING CHART PAGE")
        print("-" * 50)
        
        chart_url = f"{self.base_url}/chart.php"
        
        try:
            response = self.session.get(chart_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Analyze chart page structure
            self.findings['chart_page'] = {
                'scripts': self._extract_scripts(soup),
                'data_sources': self._find_data_sources(soup, response.text),
                'javascript_functions': self._extract_js_functions(response.text),
                'api_calls': self._find_api_calls(response.text),
                'data_patterns': self._analyze_data_patterns(soup)
            }
            
            print(f"✅ Chart page analyzed")
            print(f"   JavaScript functions: {len(self.findings['chart_page']['javascript_functions'])}")
            print(f"   Data sources: {len(self.findings['chart_page']['data_sources'])}")
            print(f"   API calls: {len(self.findings['chart_page']['api_calls'])}")
            
            return soup
            
        except Exception as e:
            print(f"❌ Error analyzing chart page: {str(e)}")
            return None
    
    def discover_backend_endpoints(self):
        """Discover backend endpoints and APIs"""
        print("\n🔍 DISCOVERING BACKEND ENDPOINTS")
        print("-" * 50)
        
        endpoints_to_check = [
            '/api/',
            '/admin/',
            '/data/',
            '/results/',
            '/update/',
            '/generate/',
            '/random/',
            '/numbers/',
            '/chart.php',
            '/result.php',
            '/admin.php',
            '/api.php',
            '/data.php',
            '/update.php',
            '/ajax/',
            '/json/',
            '/xml/'
        ]
        
        discovered_endpoints = []
        
        for endpoint in endpoints_to_check:
            try:
                url = urljoin(self.base_url, endpoint)
                response = self.session.head(url, timeout=10)
                
                if response.status_code in [200, 301, 302, 403]:
                    discovered_endpoints.append({
                        'url': url,
                        'status': response.status_code,
                        'headers': dict(response.headers)
                    })
                    print(f"   ✅ Found: {url} (Status: {response.status_code})")
                
                time.sleep(0.5)  # Rate limiting
                
            except Exception as e:
                continue
        
        self.findings['endpoints'] = discovered_endpoints
        print(f"✅ Discovered {len(discovered_endpoints)} endpoints")
    
    def analyze_javascript_code(self):
        """Analyze JavaScript code for number generation logic"""
        print("\n🔍 ANALYZING JAVASCRIPT CODE")
        print("-" * 50)
        
        js_analysis = {
            'random_functions': [],
            'number_generation': [],
            'api_calls': [],
            'suspicious_patterns': [],
            'external_dependencies': []
        }
        
        # Get all JavaScript from main and chart pages
        all_js_code = ""
        
        for page_key in ['main_page', 'chart_page']:
            if page_key in self.findings:
                for script in self.findings[page_key].get('scripts', []):
                    if script.get('content'):
                        all_js_code += script['content'] + "\n"
        
        if all_js_code:
            # Look for random number generation patterns
            random_patterns = [
                r'Math\.random\(\)',
                r'Math\.floor\(',
                r'random\(',
                r'rand\(',
                r'generate.*number',
                r'number.*generate',
                r'lottery',
                r'satta',
                r'result'
            ]
            
            for pattern in random_patterns:
                matches = re.findall(pattern, all_js_code, re.IGNORECASE)
                if matches:
                    js_analysis['random_functions'].extend(matches)
            
            # Look for API calls
            api_patterns = [
                r'fetch\([\'"]([^\'"]+)[\'"]',
                r'ajax\([\'"]([^\'"]+)[\'"]',
                r'\.get\([\'"]([^\'"]+)[\'"]',
                r'\.post\([\'"]([^\'"]+)[\'"]',
                r'XMLHttpRequest'
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, all_js_code, re.IGNORECASE)
                if matches:
                    js_analysis['api_calls'].extend(matches)
            
            # Look for suspicious patterns
            suspicious_patterns = [
                r'admin',
                r'secret',
                r'key',
                r'token',
                r'password',
                r'auth',
                r'backdoor',
                r'cheat',
                r'fix',
                r'manipulate'
            ]
            
            for pattern in suspicious_patterns:
                matches = re.findall(f'\\b{pattern}\\b.*', all_js_code, re.IGNORECASE)
                if matches:
                    js_analysis['suspicious_patterns'].extend(matches[:5])  # Limit to 5 matches
        
        self.findings['javascript_analysis'] = js_analysis
        
        print(f"✅ JavaScript analysis complete")
        print(f"   Random functions found: {len(js_analysis['random_functions'])}")
        print(f"   API calls found: {len(js_analysis['api_calls'])}")
        print(f"   Suspicious patterns: {len(js_analysis['suspicious_patterns'])}")
    
    def analyze_network_traffic(self):
        """Analyze network requests to understand data flow"""
        print("\n🔍 ANALYZING NETWORK TRAFFIC PATTERNS")
        print("-" * 50)
        
        # Monitor requests during page loads
        traffic_analysis = {
            'requests_made': [],
            'response_patterns': [],
            'data_endpoints': [],
            'timing_patterns': []
        }
        
        # Test different chart requests
        test_requests = [
            f"{self.base_url}/chart.php?ResultFor=June-2025&month=06&year=2025",
            f"{self.base_url}/chart.php?ResultFor=May-2025&month=05&year=2025",
            f"{self.base_url}/result.php" if self._endpoint_exists("/result.php") else None
        ]
        
        for url in test_requests:
            if url:
                try:
                    start_time = time.time()
                    response = self.session.get(url)
                    end_time = time.time()
                    
                    traffic_analysis['requests_made'].append({
                        'url': url,
                        'status': response.status_code,
                        'response_time': end_time - start_time,
                        'content_length': len(response.content),
                        'headers': dict(response.headers)
                    })
                    
                    # Analyze response for data patterns
                    if response.status_code == 200:
                        self._analyze_response_data(response, traffic_analysis)
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"   ❌ Error with {url}: {str(e)}")
        
        self.findings['network_analysis'] = traffic_analysis
        print(f"✅ Network analysis complete")
        print(f"   Requests analyzed: {len(traffic_analysis['requests_made'])}")
    
    def analyze_data_generation_patterns(self):
        """Analyze patterns in the actual data to detect manipulation"""
        print("\n🔍 ANALYZING DATA GENERATION PATTERNS")
        print("-" * 50)
        
        # Load our scraped data for analysis
        data_file = os.path.join(config.RAW_DATA_DIR, 'extensive_historical_data.csv')

        try:
            import pandas as pd
            import numpy as np
        except ImportError:
            print("❌ Pandas/Numpy not available for data analysis")
            return

        if os.path.exists(data_file):
            
            df = pd.read_csv(data_file)
            df['date'] = pd.to_datetime(df['date'])
            
            pattern_analysis = {}
            
            for game in config.GAMES:
                if game in df.columns:
                    game_data = df[game].dropna()
                    
                    if len(game_data) > 100:
                        analysis = self._deep_randomness_analysis(game_data, game)
                        pattern_analysis[game] = analysis
            
            self.findings['data_patterns'] = pattern_analysis
            print(f"✅ Data pattern analysis complete for {len(pattern_analysis)} games")
        else:
            print("❌ No historical data found for pattern analysis")
    
    def _deep_randomness_analysis(self, data, game_name):
        """Perform deep analysis to detect if data is truly random"""
        import numpy as np
        from scipy import stats
        
        analysis = {
            'game': game_name,
            'sample_size': len(data),
            'randomness_tests': {},
            'suspicious_indicators': [],
            'manipulation_probability': 0
        }
        
        # Test 1: Chi-square test for uniform distribution
        expected_freq = len(data) / 100
        observed_freq = np.bincount(data.astype(int), minlength=100)
        chi2_stat, chi2_p = stats.chisquare(observed_freq)
        
        analysis['randomness_tests']['chi_square'] = {
            'statistic': chi2_stat,
            'p_value': chi2_p,
            'is_random': chi2_p > 0.05
        }
        
        # Test 2: Runs test for randomness
        median = np.median(data)
        runs, n1, n2 = self._runs_test(data > median)
        expected_runs = ((2 * n1 * n2) / (n1 + n2)) + 1
        variance = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2) ** 2 * (n1 + n2 - 1))
        z_score = (runs - expected_runs) / np.sqrt(variance) if variance > 0 else 0
        
        analysis['randomness_tests']['runs_test'] = {
            'runs': runs,
            'expected_runs': expected_runs,
            'z_score': z_score,
            'is_random': abs(z_score) < 1.96
        }
        
        # Test 3: Autocorrelation analysis
        autocorr_1 = data.autocorr(lag=1) if hasattr(data, 'autocorr') else np.corrcoef(data[:-1], data[1:])[0,1]
        autocorr_7 = data.autocorr(lag=7) if hasattr(data, 'autocorr') else 0
        
        analysis['randomness_tests']['autocorrelation'] = {
            'lag_1': autocorr_1,
            'lag_7': autocorr_7,
            'is_random': abs(autocorr_1) < 0.1 and abs(autocorr_7) < 0.1
        }
        
        # Test 4: Look for suspicious patterns
        # Check for repeated sequences
        sequences = []
        for i in range(len(data) - 2):
            seq = tuple(data.iloc[i:i+3])
            sequences.append(seq)
        
        seq_counts = pd.Series(sequences).value_counts()
        max_repeat = seq_counts.iloc[0] if len(seq_counts) > 0 else 0
        
        if max_repeat > 3:
            analysis['suspicious_indicators'].append(f"Sequence repeated {max_repeat} times")
        
        # Check for number clustering
        for num in range(0, 100, 10):
            range_count = ((data >= num) & (data < num + 10)).sum()
            expected_count = len(data) * 0.1
            if abs(range_count - expected_count) > expected_count * 0.5:
                analysis['suspicious_indicators'].append(f"Range {num}-{num+9} has unusual frequency")
        
        # Calculate manipulation probability
        failed_tests = sum(1 for test in analysis['randomness_tests'].values() if not test.get('is_random', True))
        analysis['manipulation_probability'] = (failed_tests / len(analysis['randomness_tests'])) * 100
        
        if len(analysis['suspicious_indicators']) > 0:
            analysis['manipulation_probability'] += 20
        
        return analysis
    
    def _runs_test(self, binary_data):
        """Perform runs test for randomness"""
        runs = 1
        n1 = sum(binary_data)
        n2 = len(binary_data) - n1
        
        for i in range(1, len(binary_data)):
            if binary_data[i] != binary_data[i-1]:
                runs += 1
        
        return runs, n1, n2
    
    def generate_comprehensive_report(self):
        """Generate comprehensive reverse engineering report"""
        print("\n📊 GENERATING COMPREHENSIVE REPORT")
        print("-" * 50)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'target_website': self.base_url,
            'analysis_summary': {},
            'findings': self.findings,
            'security_assessment': {},
            'manipulation_indicators': {},
            'recommendations': []
        }
        
        # Summarize findings
        if 'data_patterns' in self.findings:
            manipulation_scores = []
            for game, analysis in self.findings['data_patterns'].items():
                manipulation_scores.append(analysis['manipulation_probability'])
            
            avg_manipulation_prob = np.mean(manipulation_scores) if manipulation_scores else 0
            
            report['analysis_summary'] = {
                'games_analyzed': len(self.findings['data_patterns']),
                'average_manipulation_probability': avg_manipulation_prob,
                'randomness_verdict': 'SUSPICIOUS' if avg_manipulation_prob > 30 else 'LIKELY_RANDOM' if avg_manipulation_prob < 10 else 'UNCERTAIN'
            }
        
        # Security assessment
        report['security_assessment'] = {
            'endpoints_discovered': len(self.findings.get('endpoints', [])),
            'javascript_security': self._assess_js_security(),
            'data_exposure': self._assess_data_exposure()
        }
        
        # Save report
        os.makedirs(config.DATA_DIR, exist_ok=True)
        report_file = os.path.join(config.DATA_DIR, f'reverse_engineering_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"✅ Report saved to: {report_file}")
        return report
    
    # Helper methods
    def _get_meta_content(self, soup, name):
        meta = soup.find('meta', attrs={'name': name})
        return meta.get('content', '') if meta else ''
    
    def _extract_scripts(self, soup):
        scripts = []
        for script in soup.find_all('script'):
            script_info = {
                'src': script.get('src'),
                'content': script.string if script.string else '',
                'type': script.get('type', 'text/javascript')
            }
            scripts.append(script_info)
        return scripts
    
    def _extract_external_resources(self, soup):
        resources = []
        for link in soup.find_all('link'):
            if link.get('href'):
                resources.append({
                    'type': 'link',
                    'href': link.get('href'),
                    'rel': link.get('rel')
                })
        return resources
    
    def _analyze_forms(self, soup):
        forms = []
        for form in soup.find_all('form'):
            form_info = {
                'action': form.get('action'),
                'method': form.get('method', 'GET'),
                'inputs': [{'name': inp.get('name'), 'type': inp.get('type')} for inp in form.find_all('input')]
            }
            forms.append(form_info)
        return forms
    
    def _find_ajax_endpoints(self, content):
        patterns = [
            r'ajax.*?[\'"]([^\'"]+)[\'"]',
            r'fetch.*?[\'"]([^\'"]+)[\'"]',
            r'\.get.*?[\'"]([^\'"]+)[\'"]',
            r'\.post.*?[\'"]([^\'"]+)[\'"]'
        ]
        
        endpoints = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            endpoints.extend(matches)
        
        return list(set(endpoints))
    
    def _find_data_sources(self, soup, content):
        # Look for data sources in the HTML and JavaScript
        data_sources = []
        
        # Check for embedded JSON data
        json_pattern = r'var\s+\w+\s*=\s*(\{.*?\});'
        json_matches = re.findall(json_pattern, content, re.DOTALL)
        
        for match in json_matches:
            try:
                json.loads(match)
                data_sources.append({'type': 'embedded_json', 'content': match[:100] + '...'})
            except:
                pass
        
        return data_sources
    
    def _extract_js_functions(self, content):
        function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
        return re.findall(function_pattern, content)
    
    def _find_api_calls(self, content):
        api_patterns = [
            r'fetch\([\'"]([^\'"]+)[\'"]',
            r'ajax.*?url.*?[\'"]([^\'"]+)[\'"]',
            r'XMLHttpRequest.*?open.*?[\'"]([^\'"]+)[\'"]'
        ]
        
        api_calls = []
        for pattern in api_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            api_calls.extend(matches)
        
        return list(set(api_calls))
    
    def _analyze_data_patterns(self, soup):
        # Look for patterns in how data is structured
        tables = soup.find_all('table')
        return {
            'tables_found': len(tables),
            'data_structure': 'tabular' if tables else 'other'
        }
    
    def _endpoint_exists(self, endpoint):
        try:
            url = urljoin(self.base_url, endpoint)
            response = self.session.head(url, timeout=5)
            return response.status_code in [200, 301, 302]
        except:
            return False
    
    def _analyze_response_data(self, response, traffic_analysis):
        # Analyze response for data patterns
        content = response.text
        
        # Look for JSON data
        try:
            json_data = json.loads(content)
            traffic_analysis['response_patterns'].append({
                'type': 'json',
                'keys': list(json_data.keys()) if isinstance(json_data, dict) else 'array'
            })
        except:
            pass
        
        # Look for embedded data
        if 'satta' in content.lower() or 'result' in content.lower():
            traffic_analysis['data_endpoints'].append(response.url)
    
    def _assess_js_security(self):
        js_analysis = self.findings.get('javascript_analysis', {})
        
        security_score = 100
        issues = []
        
        if js_analysis.get('suspicious_patterns'):
            security_score -= 30
            issues.append("Suspicious patterns found in JavaScript")
        
        if len(js_analysis.get('api_calls', [])) > 10:
            security_score -= 20
            issues.append("Many API calls detected")
        
        return {
            'score': max(0, security_score),
            'issues': issues
        }
    
    def _assess_data_exposure(self):
        endpoints = self.findings.get('endpoints', [])
        
        exposed_data = []
        for endpoint in endpoints:
            if any(keyword in endpoint['url'].lower() for keyword in ['admin', 'api', 'data']):
                exposed_data.append(endpoint['url'])
        
        return {
            'exposed_endpoints': exposed_data,
            'risk_level': 'HIGH' if exposed_data else 'LOW'
        }

def main():
    """Main reverse engineering function"""
    print("🕵️ REVERSE ENGINEERING SATTA KING WEBSITE")
    print("=" * 70)
    print("Analyzing data sources, number generation, and potential manipulation...")
    print()
    
    engineer = SattaReverseEngineer()
    
    try:
        # Step 1: Analyze main page
        engineer.analyze_main_page()
        
        # Step 2: Analyze chart page
        engineer.analyze_chart_page()
        
        # Step 3: Discover backend endpoints
        engineer.discover_backend_endpoints()
        
        # Step 4: Analyze JavaScript
        engineer.analyze_javascript_code()
        
        # Step 5: Analyze network traffic
        engineer.analyze_network_traffic()
        
        # Step 6: Analyze data patterns
        engineer.analyze_data_generation_patterns()
        
        # Step 7: Generate report
        report = engineer.generate_comprehensive_report()
        
        # Display summary
        print(f"\n🎯 REVERSE ENGINEERING SUMMARY")
        print("=" * 50)
        
        if 'analysis_summary' in report:
            summary = report['analysis_summary']
            print(f"Games analyzed: {summary.get('games_analyzed', 0)}")
            print(f"Manipulation probability: {summary.get('average_manipulation_probability', 0):.1f}%")
            print(f"Randomness verdict: {summary.get('randomness_verdict', 'UNKNOWN')}")
        
        print(f"Endpoints discovered: {len(report['findings'].get('endpoints', []))}")
        print(f"Security assessment: {report['security_assessment'].get('javascript_security', {}).get('score', 0)}/100")
        
    except Exception as e:
        print(f"❌ Error during reverse engineering: {str(e)}")

if __name__ == "__main__":
    main()
