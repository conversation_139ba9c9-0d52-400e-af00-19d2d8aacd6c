"""
Backtest our LCG strategy on previous 20 results to validate accuracy
"""

import pandas as pd
import numpy as np
from datetime import datetime

def backtest_lcg_strategy():
    """Backtest our discovered LCG formulas on historical data"""
    
    print("🔬 LCG STRATEGY BACKTEST")
    print("=" * 60)
    print("Testing our discovered mathematical formulas on last 20 results")
    print("=" * 60)
    
    # Load data
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    print(f"✅ Loaded {len(df)} total records")
    print(f"📅 Data range: {df['date'].min()} to {df['date'].max()}")
    
    # Test each game
    overall_results = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            print(f"\n🎲 {game} LCG BACKTEST:")
            print("-" * 40)
            
            if len(game_data) >= 25:  # Need at least 25 for 20 predictions
                results = backtest_game_lcg(game_data, game)
                overall_results[game] = results
            else:
                print(f"   ❌ Insufficient data ({len(game_data)} records)")
    
    # Generate overall assessment
    generate_backtest_assessment(overall_results)

def backtest_game_lcg(data, game_name):
    """Backtest LCG predictions for a specific game"""
    
    # Use last 25 records (5 for seed, predict next 20)
    test_data = data.tail(25).reset_index(drop=True)
    
    print(f"   📊 Testing on last 25 records")
    print(f"   🔍 Using first 5 as seed, predicting next 20")
    
    # Get LCG parameters for this game
    lcg_params = get_lcg_parameters(game_name)
    
    if not lcg_params or lcg_params['a'] is None:
        print(f"   ❌ No LCG parameters found for {game_name}")
        print(f"   🔄 Using alternative strategy for this game")
        return test_alternative_prediction(test_data, game_name)

    a, c, m = lcg_params['a'], lcg_params['c'], lcg_params['m']
    
    print(f"   🔢 Using LCG: X(n+1) = ({a} * X(n) + {c}) mod {m}")
    
    # Start predictions from position 5
    predictions = []
    actuals = []
    
    # Use the 5th number as seed
    seed = int(test_data.iloc[4])
    x = seed
    
    print(f"   🌱 Seed value: {seed}")
    print(f"\n   📋 PREDICTION vs ACTUAL RESULTS:")
    print(f"   {'Day':<4} {'Predicted':<10} {'Actual':<8} {'Match':<6} {'Diff':<6}")
    print(f"   {'-'*4} {'-'*10} {'-'*8} {'-'*6} {'-'*6}")
    
    exact_matches = 0
    within_5_matches = 0
    within_10_matches = 0
    total_predictions = 0
    
    # Predict next 20 numbers
    for i in range(5, min(25, len(test_data))):
        # Generate next prediction using LCG
        x = (a * x + c) % m
        
        # Map to 0-99 range if needed
        if m == 100:
            predicted = x
        elif m == 101:
            predicted = x % 100
        elif m == 256:
            predicted = x % 100
        else:
            predicted = x % 100
        
        actual = int(test_data.iloc[i])
        
        predictions.append(predicted)
        actuals.append(actual)
        
        # Check accuracy
        exact_match = (predicted == actual)
        within_5 = abs(predicted - actual) <= 5
        within_10 = abs(predicted - actual) <= 10
        
        if exact_match:
            exact_matches += 1
            match_status = "✅ EXACT"
        elif within_5:
            match_status = "🟡 ±5"
        elif within_10:
            match_status = "🟠 ±10"
        else:
            match_status = "❌ MISS"
        
        if within_5:
            within_5_matches += 1
        if within_10:
            within_10_matches += 1
        
        total_predictions += 1
        
        diff = abs(predicted - actual)
        day_num = i - 4
        
        print(f"   {day_num:<4} {predicted:<10} {actual:<8} {match_status:<6} {diff:<6}")
        
        # Update seed for next prediction
        x = actual  # Use actual value as next seed (more realistic)
    
    # Calculate accuracies
    exact_accuracy = (exact_matches / total_predictions) * 100 if total_predictions > 0 else 0
    within_5_accuracy = (within_5_matches / total_predictions) * 100 if total_predictions > 0 else 0
    within_10_accuracy = (within_10_matches / total_predictions) * 100 if total_predictions > 0 else 0
    
    # Calculate average error
    avg_error = np.mean([abs(p - a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    print(f"\n   📊 BACKTEST RESULTS:")
    print(f"   Exact matches: {exact_matches}/{total_predictions} ({exact_accuracy:.1f}%)")
    print(f"   Within ±5: {within_5_matches}/{total_predictions} ({within_5_accuracy:.1f}%)")
    print(f"   Within ±10: {within_10_matches}/{total_predictions} ({within_10_accuracy:.1f}%)")
    print(f"   Average error: {avg_error:.1f}")
    
    # Performance assessment
    if exact_accuracy >= 20:
        performance = "🏆 EXCELLENT"
    elif exact_accuracy >= 10:
        performance = "🥈 GOOD"
    elif exact_accuracy >= 5:
        performance = "🥉 FAIR"
    elif within_10_accuracy >= 50:
        performance = "💡 MODERATE"
    else:
        performance = "❌ POOR"
    
    print(f"   Performance: {performance}")
    
    return {
        'total_predictions': total_predictions,
        'exact_matches': exact_matches,
        'within_5_matches': within_5_matches,
        'within_10_matches': within_10_matches,
        'exact_accuracy': exact_accuracy,
        'within_5_accuracy': within_5_accuracy,
        'within_10_accuracy': within_10_accuracy,
        'average_error': avg_error,
        'performance': performance,
        'predictions': predictions,
        'actuals': actuals,
        'lcg_params': lcg_params
    }

def get_lcg_parameters(game_name):
    """Get LCG parameters discovered for each game"""
    
    lcg_params = {
        'DSWR': {
            'a': 1103515245,
            'c': 1013904223,
            'm': 2**32,
            'correlation': 0.3469
        },
        'FRBD': {
            'a': 48271,
            'c': 0,
            'm': 101,
            'correlation': 0.3627
        },
        'GZBD': {
            'a': 1664525,
            'c': 1013904223,
            'm': 256,
            'correlation': 0.3958
        },
        'GALI': {
            'a': None,  # No clear LCG pattern
            'c': None,
            'm': None,
            'correlation': 0.2518
        }
    }
    
    return lcg_params.get(game_name)

def test_alternative_prediction(data, game_name):
    """Test alternative prediction for games without LCG patterns"""

    print(f"   🔄 Using pattern-based prediction for {game_name}")

    predictions = []
    actuals = []

    exact_matches = 0
    within_5_matches = 0
    within_10_matches = 0
    total_predictions = 0

    # Use recent avoidance + mean reversion strategy
    for i in range(5, len(data)):
        # Recent avoidance
        recent_numbers = set(data.iloc[i-5:i])
        all_numbers = set(range(100))
        available_numbers = list(all_numbers - recent_numbers)

        # Simple prediction: choose middle of available range
        if available_numbers:
            predicted = sorted(available_numbers)[len(available_numbers)//2]
        else:
            predicted = 50  # Fallback

        actual = int(data.iloc[i])

        predictions.append(predicted)
        actuals.append(actual)

        # Check accuracy
        exact_match = (predicted == actual)
        within_5 = abs(predicted - actual) <= 5
        within_10 = abs(predicted - actual) <= 10

        if exact_match:
            exact_matches += 1
            match_status = "✅ EXACT"
        elif within_5:
            match_status = "🟡 ±5"
        elif within_10:
            match_status = "🟠 ±10"
        else:
            match_status = "❌ MISS"

        if within_5:
            within_5_matches += 1
        if within_10:
            within_10_matches += 1

        total_predictions += 1

        diff = abs(predicted - actual)
        day_num = i - 4

        print(f"   {day_num:<4} {predicted:<10} {actual:<8} {match_status:<6} {diff:<6}")

    # Calculate accuracies
    exact_accuracy = (exact_matches / total_predictions) * 100 if total_predictions > 0 else 0
    within_5_accuracy = (within_5_matches / total_predictions) * 100 if total_predictions > 0 else 0
    within_10_accuracy = (within_10_matches / total_predictions) * 100 if total_predictions > 0 else 0
    avg_error = np.mean([abs(p - a) for p, a in zip(predictions, actuals)]) if predictions else 0

    print(f"\n   📊 BACKTEST RESULTS:")
    print(f"   Exact matches: {exact_matches}/{total_predictions} ({exact_accuracy:.1f}%)")
    print(f"   Within ±5: {within_5_matches}/{total_predictions} ({within_5_accuracy:.1f}%)")
    print(f"   Within ±10: {within_10_matches}/{total_predictions} ({within_10_accuracy:.1f}%)")
    print(f"   Average error: {avg_error:.1f}")

    if exact_accuracy >= 10:
        performance = "🥈 GOOD"
    elif exact_accuracy >= 5:
        performance = "🥉 FAIR"
    else:
        performance = "❌ POOR"

    print(f"   Performance: {performance}")

    return {
        'total_predictions': total_predictions,
        'exact_matches': exact_matches,
        'within_5_matches': within_5_matches,
        'within_10_matches': within_10_matches,
        'exact_accuracy': exact_accuracy,
        'within_5_accuracy': within_5_accuracy,
        'within_10_accuracy': within_10_accuracy,
        'average_error': avg_error,
        'performance': performance,
        'predictions': predictions,
        'actuals': actuals,
        'lcg_params': {'method': 'pattern_based'}
    }

def test_alternative_strategies(data, game_name):
    """Test alternative prediction strategies for comparison"""
    
    print(f"\n   🔄 TESTING ALTERNATIVE STRATEGIES:")
    
    test_data = data.tail(25).reset_index(drop=True)
    
    strategies = {}
    
    # Strategy 1: Recent avoidance
    recent_avoid_accuracy = test_recent_avoidance_strategy(test_data)
    strategies['Recent Avoidance'] = recent_avoid_accuracy
    
    # Strategy 2: Mean reversion
    mean_reversion_accuracy = test_mean_reversion_strategy(test_data)
    strategies['Mean Reversion'] = mean_reversion_accuracy
    
    # Strategy 3: Random baseline
    random_accuracy = 1.0  # 1% expected for exact match
    strategies['Random Baseline'] = random_accuracy
    
    print(f"   📊 Strategy Comparison:")
    for strategy, accuracy in strategies.items():
        print(f"      {strategy}: {accuracy:.1f}%")
    
    return strategies

def test_recent_avoidance_strategy(data):
    """Test recent avoidance strategy accuracy"""
    
    correct = 0
    total = 0
    
    for i in range(10, len(data)):
        recent_numbers = set(data.iloc[i-10:i])
        actual = data.iloc[i]
        
        total += 1
        # Strategy: predict number NOT in recent 10
        if actual not in recent_numbers:
            correct += 1
    
    return (correct / total * 100) if total > 0 else 0

def test_mean_reversion_strategy(data):
    """Test mean reversion strategy accuracy"""
    
    correct = 0
    total = 0
    
    for i in range(10, len(data)):
        recent_mean = data.iloc[i-10:i].mean()
        current = data.iloc[i-1]
        actual = data.iloc[i]
        
        # Only predict when far from mean
        if abs(current - recent_mean) > 10:
            total += 1
            
            # Predict direction toward mean
            if current > recent_mean and actual < current:
                correct += 1
            elif current < recent_mean and actual > current:
                correct += 1
    
    return (correct / total * 100) if total > 0 else 0

def generate_backtest_assessment(overall_results):
    """Generate overall assessment of LCG strategy"""
    
    print(f"\n🎯 OVERALL LCG STRATEGY ASSESSMENT")
    print("=" * 50)
    
    if not overall_results:
        print("❌ No valid backtest results")
        return
    
    # Calculate averages
    total_exact = sum(r['exact_accuracy'] for r in overall_results.values())
    total_within_5 = sum(r['within_5_accuracy'] for r in overall_results.values())
    total_within_10 = sum(r['within_10_accuracy'] for r in overall_results.values())
    total_avg_error = sum(r['average_error'] for r in overall_results.values())
    
    num_games = len(overall_results)
    
    avg_exact = total_exact / num_games
    avg_within_5 = total_within_5 / num_games
    avg_within_10 = total_within_10 / num_games
    avg_error = total_avg_error / num_games
    
    print(f"📊 AVERAGE PERFORMANCE ACROSS ALL GAMES:")
    print(f"   Exact accuracy: {avg_exact:.1f}%")
    print(f"   Within ±5: {avg_within_5:.1f}%")
    print(f"   Within ±10: {avg_within_10:.1f}%")
    print(f"   Average error: {avg_error:.1f}")
    
    # Rank games by performance
    ranked_games = sorted(overall_results.items(), 
                         key=lambda x: x[1]['exact_accuracy'], 
                         reverse=True)
    
    print(f"\n🏆 GAME PERFORMANCE RANKING:")
    for i, (game, results) in enumerate(ranked_games, 1):
        exact_acc = results['exact_accuracy']
        performance = results['performance']
        
        print(f"   {i}. {game}: {exact_acc:.1f}% exact {performance}")
    
    # Overall strategy assessment
    print(f"\n🎯 LCG STRATEGY VERDICT:")
    
    if avg_exact >= 15:
        verdict = "🏆 HIGHLY EFFECTIVE"
        recommendation = "STRONG BUY - Use with confidence"
    elif avg_exact >= 10:
        verdict = "🥈 MODERATELY EFFECTIVE"
        recommendation = "MODERATE BUY - Use with caution"
    elif avg_exact >= 5:
        verdict = "🥉 MARGINALLY EFFECTIVE"
        recommendation = "WEAK BUY - Very small stakes only"
    elif avg_within_10 >= 40:
        verdict = "💡 DIRECTIONALLY USEFUL"
        recommendation = "PAPER TRADE - Not for real money"
    else:
        verdict = "❌ INEFFECTIVE"
        recommendation = "DO NOT USE - No edge detected"
    
    print(f"   {verdict}")
    print(f"   📋 Recommendation: {recommendation}")
    
    # Compare to random
    random_expected = 1.0  # 1% for exact match
    improvement = avg_exact / random_expected
    
    print(f"\n📈 IMPROVEMENT OVER RANDOM:")
    print(f"   Random expected: {random_expected:.1f}%")
    print(f"   Our strategy: {avg_exact:.1f}%")
    print(f"   Improvement factor: {improvement:.1f}x")
    
    if improvement >= 5:
        print(f"   🚀 SIGNIFICANT IMPROVEMENT!")
    elif improvement >= 2:
        print(f"   ✅ MODERATE IMPROVEMENT")
    else:
        print(f"   ⚠️ MINIMAL IMPROVEMENT")
    
    # Risk assessment
    print(f"\n⚠️ RISK ASSESSMENT:")
    
    if avg_exact < 20:
        print(f"   🚨 HIGH RISK: {100-avg_exact:.1f}% chance of being wrong")
    
    if avg_error > 15:
        print(f"   ⚠️ HIGH VARIANCE: Average error {avg_error:.1f}")
    
    print(f"   💰 RECOMMENDED STAKES:")
    if avg_exact >= 15:
        print(f"      Maximum 2% of bankroll per bet")
    elif avg_exact >= 10:
        print(f"      Maximum 1% of bankroll per bet")
    elif avg_exact >= 5:
        print(f"      Maximum 0.5% of bankroll per bet")
    else:
        print(f"      DO NOT BET REAL MONEY")
    
    # Save backtest results
    save_backtest_results(overall_results, {
        'avg_exact': avg_exact,
        'avg_within_5': avg_within_5,
        'avg_within_10': avg_within_10,
        'verdict': verdict,
        'recommendation': recommendation
    })

def save_backtest_results(results, summary):
    """Save backtest results to file"""
    
    import json
    import os
    
    backtest_report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'LCG Strategy Backtest',
        'test_period': '20 predictions per game',
        'summary': summary,
        'detailed_results': results
    }
    
    os.makedirs('data', exist_ok=True)
    filename = f'data/lcg_backtest_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(filename, 'w') as f:
        json.dump(backtest_report, f, indent=2, default=str)
    
    print(f"\n💾 Backtest results saved to: {filename}")

if __name__ == "__main__":
    backtest_lcg_strategy()
