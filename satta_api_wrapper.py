"""
Satta King API Wrapper
Since direct APIs are not publicly accessible, this creates a wrapper
that scrapes the websites and provides clean API-like access to the data
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup
from flask import Flask, jsonify, request
import threading
import os

class SattaKingAPI:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.sites = {
            'primary': 'https://satta-king-fast.com',
            'backup1': 'https://sattaking.com',
            'backup2': 'https://sattakinggali.com',
            'backup3': 'https://sattakingdesawar.com'
        }
        
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
        
    def get_live_results(self):
        """Get current live results from all games"""
        
        cache_key = 'live_results'
        
        # Check cache first
        if self.is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'source': 'multiple_sites',
            'games': {},
            'status': 'success'
        }
        
        # Try each site until we get data
        for site_name, site_url in self.sites.items():
            try:
                print(f"Fetching from {site_name}: {site_url}")
                
                response = self.session.get(site_url, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                site_results = self.extract_results_from_site(soup, site_url)
                
                if site_results:
                    results['games'].update(site_results)
                    results['primary_source'] = site_name
                    break
                    
            except Exception as e:
                print(f"Error fetching from {site_name}: {str(e)}")
                continue
        
        # Cache the results
        self.cache[cache_key] = {
            'data': results,
            'timestamp': time.time()
        }
        
        return results
    
    def extract_results_from_site(self, soup, site_url):
        """Extract satta results from a website"""
        
        results = {}
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        # Method 1: Look in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    game_text = cells[0].get_text().strip()
                    
                    # Check if this row contains a game
                    for game in games:
                        if game.lower() in game_text.lower():
                            # Look for result in subsequent cells
                            for cell in cells[1:]:
                                cell_text = cell.get_text().strip()
                                # Look for 2-digit numbers
                                if re.match(r'^\d{1,2}$', cell_text):
                                    results[game] = {
                                        'result': cell_text,
                                        'game_name': game,
                                        'source': site_url,
                                        'extracted_at': datetime.now().isoformat()
                                    }
                                    break
        
        # Method 2: Look for specific patterns in text
        page_text = soup.get_text()
        for game in games:
            pattern = rf'{game}[:\s]*(\d{{1,2}})'
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match and game not in results:
                results[game] = {
                    'result': match.group(1),
                    'game_name': game,
                    'source': site_url,
                    'extracted_at': datetime.now().isoformat()
                }
        
        # Method 3: Look in divs with specific classes
        result_divs = soup.find_all(['div', 'span'], class_=re.compile(r'result|number', re.I))
        for div in result_divs:
            text = div.get_text()
            for game in games:
                if game.lower() in text.lower():
                    numbers = re.findall(r'\d{1,2}', text)
                    if numbers and game not in results:
                        results[game] = {
                            'result': numbers[0],
                            'game_name': game,
                            'source': site_url,
                            'extracted_at': datetime.now().isoformat()
                        }
        
        return results
    
    def get_game_result(self, game_name):
        """Get result for a specific game"""
        
        game_name = game_name.upper()
        all_results = self.get_live_results()
        
        if game_name in all_results.get('games', {}):
            return {
                'status': 'success',
                'game': game_name,
                'result': all_results['games'][game_name],
                'timestamp': all_results['timestamp']
            }
        else:
            return {
                'status': 'not_found',
                'game': game_name,
                'message': f'No result found for {game_name}',
                'available_games': list(all_results.get('games', {}).keys())
            }
    
    def get_historical_data(self, game_name=None, days=7):
        """Get historical data (if available)"""
        
        # This would require scraping historical pages
        # For now, return current data
        current_results = self.get_live_results()
        
        return {
            'status': 'limited',
            'message': 'Historical data requires additional scraping',
            'current_data': current_results,
            'note': 'Use get_live_results() for current data'
        }
    
    def is_cache_valid(self, cache_key):
        """Check if cached data is still valid"""
        
        if cache_key not in self.cache:
            return False
        
        cache_age = time.time() - self.cache[cache_key]['timestamp']
        return cache_age < self.cache_timeout
    
    def get_api_status(self):
        """Get API status and available endpoints"""
        
        return {
            'status': 'operational',
            'version': '1.0',
            'endpoints': {
                '/api/live': 'Get current live results for all games',
                '/api/game/<game_name>': 'Get result for specific game',
                '/api/status': 'Get API status',
                '/api/games': 'List available games'
            },
            'available_games': ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD'],
            'cache_timeout': f'{self.cache_timeout} seconds',
            'sources': list(self.sites.values())
        }
    
    def get_available_games(self):
        """Get list of available games"""
        
        return {
            'status': 'success',
            'games': [
                {'code': 'GALI', 'name': 'Gali', 'description': 'Gali Satta'},
                {'code': 'DSWR', 'name': 'Desawar', 'description': 'Desawar Satta'},
                {'code': 'FRBD', 'name': 'Faridabad', 'description': 'Faridabad Satta'},
                {'code': 'GZBD', 'name': 'Ghaziabad', 'description': 'Ghaziabad Satta'}
            ],
            'total_games': 4
        }

# Flask API wrapper
app = Flask(__name__)
satta_api = SattaKingAPI()

@app.route('/api/live', methods=['GET'])
def api_live_results():
    """API endpoint for live results"""
    try:
        results = satta_api.get_live_results()
        return jsonify(results)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/game/<game_name>', methods=['GET'])
def api_game_result(game_name):
    """API endpoint for specific game result"""
    try:
        result = satta_api.get_game_result(game_name)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/status', methods=['GET'])
def api_status():
    """API status endpoint"""
    return jsonify(satta_api.get_api_status())

@app.route('/api/games', methods=['GET'])
def api_games():
    """Available games endpoint"""
    return jsonify(satta_api.get_available_games())

@app.route('/api/historical/<game_name>', methods=['GET'])
def api_historical(game_name):
    """Historical data endpoint"""
    days = request.args.get('days', 7, type=int)
    result = satta_api.get_historical_data(game_name, days)
    return jsonify(result)

@app.route('/', methods=['GET'])
def api_docs():
    """API documentation"""
    return jsonify({
        'message': 'Satta King API Wrapper',
        'version': '1.0',
        'documentation': {
            'GET /api/live': 'Get current live results for all games',
            'GET /api/game/<game_name>': 'Get result for specific game (GALI, DSWR, etc.)',
            'GET /api/status': 'Get API status and information',
            'GET /api/games': 'List all available games',
            'GET /api/historical/<game_name>?days=7': 'Get historical data (limited)'
        },
        'example_usage': {
            'live_results': 'GET /api/live',
            'gali_result': 'GET /api/game/GALI',
            'desawar_result': 'GET /api/game/DSWR'
        }
    })

def test_api():
    """Test the API functionality"""
    
    print("🧪 TESTING SATTA KING API WRAPPER")
    print("=" * 60)
    
    api = SattaKingAPI()
    
    # Test live results
    print("\n📊 Testing live results...")
    live_results = api.get_live_results()
    print(f"Status: {live_results.get('status')}")
    print(f"Games found: {len(live_results.get('games', {}))}")
    
    for game, data in live_results.get('games', {}).items():
        print(f"  {game}: {data.get('result')}")
    
    # Test specific game
    print("\n🎯 Testing specific game (GALI)...")
    gali_result = api.get_game_result('GALI')
    print(f"Status: {gali_result.get('status')}")
    if gali_result.get('status') == 'success':
        print(f"GALI result: {gali_result['result']['result']}")
    
    # Test API status
    print("\n📋 Testing API status...")
    status = api.get_api_status()
    print(f"API Status: {status.get('status')}")
    print(f"Available endpoints: {len(status.get('endpoints', {}))}")
    
    return live_results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # Test mode
        test_api()
    elif len(sys.argv) > 1 and sys.argv[1] == 'server':
        # Server mode
        print("🚀 Starting Satta King API Server...")
        print("API will be available at: http://localhost:5000")
        print("\nAvailable endpoints:")
        print("  GET /api/live - Live results")
        print("  GET /api/game/<game> - Specific game result")
        print("  GET /api/status - API status")
        print("  GET /api/games - Available games")
        
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        # Default: just get live results
        api = SattaKingAPI()
        results = api.get_live_results()
        print(json.dumps(results, indent=2))
