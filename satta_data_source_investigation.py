"""
Deep investigation into how actual Satta King sites get their data
Focus on data flow, APIs, databases, and update mechanisms
"""

import requests
import json
import re
import time
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import os

class SattaDataSourceInvestigator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        # Actual Satta King sites only
        self.actual_satta_sites = [
            "https://satta-king-fast.com",
            "https://sattakinggali.com", 
            "https://sattakingdesawar.com",
            "https://sattamatka.com"
        ]
        
    def investigate_data_sources(self):
        """Comprehensive investigation of how Satta sites get their data"""
        
        print("🔍 SATTA KING DATA SOURCE INVESTIGATION")
        print("=" * 60)
        print("Investigating how actual Satta sites get their data...")
        
        investigation = {
            'timestamp': datetime.now().isoformat(),
            'sites_analyzed': len(self.actual_satta_sites),
            'data_flow_analysis': {},
            'api_discovery': {},
            'database_investigation': {},
            'update_mechanism_analysis': {},
            'cross_site_comparison': {},
            'data_source_conclusions': {}
        }
        
        # 1. Analyze each site's data flow
        print(f"\n📊 DATA FLOW ANALYSIS")
        print("-" * 40)
        
        for site in self.actual_satta_sites:
            print(f"\n🔍 Analyzing: {site}")
            site_analysis = self.analyze_site_data_flow(site)
            investigation['data_flow_analysis'][site] = site_analysis
        
        # 2. API Discovery
        print(f"\n🌐 API DISCOVERY")
        print("-" * 40)
        investigation['api_discovery'] = self.discover_apis()
        
        # 3. Database Investigation
        print(f"\n💾 DATABASE INVESTIGATION")
        print("-" * 40)
        investigation['database_investigation'] = self.investigate_databases()
        
        # 4. Update Mechanism Analysis
        print(f"\n🔄 UPDATE MECHANISM ANALYSIS")
        print("-" * 40)
        investigation['update_mechanism_analysis'] = self.analyze_update_mechanisms()
        
        # 5. Cross-site Comparison
        print(f"\n🔍 CROSS-SITE COMPARISON")
        print("-" * 40)
        investigation['cross_site_comparison'] = self.compare_data_sources()
        
        # 6. Generate Conclusions
        print(f"\n📋 GENERATING CONCLUSIONS")
        print("-" * 40)
        investigation['data_source_conclusions'] = self.generate_data_source_conclusions(investigation)
        
        # Save comprehensive report
        self.save_investigation_report(investigation)
        
        return investigation
    
    def analyze_site_data_flow(self, site_url):
        """Analyze how a specific site gets and serves its data"""
        
        site_analysis = {
            'site': site_url,
            'server_technology': {},
            'content_generation': 'unknown',
            'data_embedding': {},
            'javascript_analysis': {},
            'network_requests': {},
            'caching_strategy': {},
            'real_time_updates': False
        }
        
        try:
            print(f"   📊 Analyzing data flow for {site_url}")
            
            # Get main page and analyze
            response = self.session.get(site_url, timeout=15)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 1. Server Technology Analysis
            site_analysis['server_technology'] = self.analyze_server_tech(response)
            
            # 2. Content Generation Analysis
            site_analysis['content_generation'] = self.analyze_content_generation(response, soup)
            
            # 3. Data Embedding Analysis
            site_analysis['data_embedding'] = self.analyze_data_embedding(soup)
            
            # 4. JavaScript Analysis
            site_analysis['javascript_analysis'] = self.analyze_javascript_data_handling(soup, site_url)
            
            # 5. Network Requests Analysis
            site_analysis['network_requests'] = self.analyze_network_requests(soup, site_url)
            
            # 6. Caching Strategy
            site_analysis['caching_strategy'] = self.analyze_caching_strategy(response)
            
            # 7. Real-time Updates Test
            site_analysis['real_time_updates'] = self.test_real_time_updates(site_url)
            
            print(f"   ✅ Analysis complete for {site_url}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing {site_url}: {str(e)}")
            site_analysis['error'] = str(e)
        
        return site_analysis
    
    def analyze_server_tech(self, response):
        """Analyze server technology and infrastructure"""
        
        tech_info = {
            'server_software': 'unknown',
            'backend_language': 'unknown',
            'database_hints': [],
            'cdn_usage': False,
            'hosting_provider': 'unknown',
            'response_time': response.elapsed.total_seconds()
        }
        
        headers = response.headers
        
        # Server identification
        server = headers.get('server', '').lower()
        if 'cloudflare' in server:
            tech_info['server_software'] = 'Cloudflare'
            tech_info['cdn_usage'] = True
        elif 'apache' in server:
            tech_info['server_software'] = 'Apache'
        elif 'nginx' in server:
            tech_info['server_software'] = 'Nginx'
        
        # Backend language detection
        content = response.text.lower()
        if '.php' in content or 'php' in headers.get('x-powered-by', ''):
            tech_info['backend_language'] = 'PHP'
        elif '.asp' in content:
            tech_info['backend_language'] = 'ASP.NET'
        elif 'django' in content:
            tech_info['backend_language'] = 'Python/Django'
        
        # Database hints
        if any(db in content for db in ['mysql', 'database', 'sql']):
            tech_info['database_hints'].append('Database references found')
        
        print(f"     Server: {tech_info['server_software']}, Language: {tech_info['backend_language']}")
        
        return tech_info
    
    def analyze_content_generation(self, response, soup):
        """Determine how content is generated (server-side vs client-side)"""
        
        # Check if results are in initial HTML
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        results_in_html = False
        
        page_text = soup.get_text()
        for game in games:
            if re.search(rf'{game}[:\s]*\d{{1,2}}', page_text, re.IGNORECASE):
                results_in_html = True
                break
        
        if results_in_html:
            return 'server_side_rendering'
        else:
            # Check for JavaScript loading
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and any(keyword in script.string.lower() for keyword in ['result', 'data', 'ajax', 'fetch']):
                    return 'client_side_loading'
            
            return 'static_content'
    
    def analyze_data_embedding(self, soup):
        """Analyze how data is embedded in the page"""
        
        embedding = {
            'html_tables': 0,
            'json_in_scripts': [],
            'data_attributes': [],
            'hidden_inputs': [],
            'meta_data': []
        }
        
        # Count data tables
        tables = soup.find_all('table')
        embedding['html_tables'] = len(tables)
        
        # Look for JSON in scripts
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Look for JSON objects
                json_matches = re.findall(r'{[^{}]*(?:"result"|"data"|"satta")[^{}]*}', script.string)
                for match in json_matches:
                    try:
                        json_obj = json.loads(match)
                        embedding['json_in_scripts'].append(json_obj)
                    except:
                        continue
        
        # Look for data attributes
        elements_with_data = soup.find_all(attrs=lambda x: x and any(k.startswith('data-') for k in x.keys()))
        for element in elements_with_data[:5]:  # First 5
            for attr, value in element.attrs.items():
                if attr.startswith('data-'):
                    embedding['data_attributes'].append({attr: value})
        
        # Look for hidden inputs
        hidden_inputs = soup.find_all('input', type='hidden')
        for inp in hidden_inputs[:5]:  # First 5
            embedding['hidden_inputs'].append({
                'name': inp.get('name'),
                'value': inp.get('value')
            })
        
        print(f"     Data embedding: {embedding['html_tables']} tables, {len(embedding['json_in_scripts'])} JSON objects")
        
        return embedding
    
    def analyze_javascript_data_handling(self, soup, site_url):
        """Analyze JavaScript for data handling patterns"""
        
        js_analysis = {
            'ajax_calls': [],
            'fetch_requests': [],
            'websocket_connections': [],
            'polling_intervals': [],
            'data_manipulation': [],
            'external_api_calls': []
        }
        
        scripts = soup.find_all('script')
        
        for script in scripts:
            if script.string:
                content = script.string
                
                # Look for AJAX calls
                ajax_patterns = [
                    r'\.ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)["\']',
                    r'\.get\s*\(\s*["\']([^"\']+)["\']',
                    r'\.post\s*\(\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in ajax_patterns:
                    matches = re.findall(pattern, content)
                    js_analysis['ajax_calls'].extend(matches)
                
                # Look for fetch requests
                fetch_matches = re.findall(r'fetch\s*\(\s*["\']([^"\']+)["\']', content)
                js_analysis['fetch_requests'].extend(fetch_matches)
                
                # Look for WebSocket connections
                ws_matches = re.findall(r'new\s+WebSocket\s*\(\s*["\']([^"\']+)["\']', content)
                js_analysis['websocket_connections'].extend(ws_matches)
                
                # Look for polling intervals
                interval_matches = re.findall(r'setInterval\s*\([^,]+,\s*(\d+)', content)
                js_analysis['polling_intervals'].extend(interval_matches)
                
                # Look for data manipulation
                if any(keyword in content.lower() for keyword in ['result', 'generate', 'random', 'update']):
                    js_analysis['data_manipulation'].append("Data manipulation keywords found")
        
        # Also check external scripts
        external_scripts = soup.find_all('script', src=True)
        for script in external_scripts:
            src = script.get('src')
            if src and not src.startswith(site_url):
                js_analysis['external_api_calls'].append(src)
        
        print(f"     JavaScript: {len(js_analysis['ajax_calls'])} AJAX, {len(js_analysis['fetch_requests'])} fetch calls")
        
        return js_analysis
    
    def analyze_network_requests(self, soup, site_url):
        """Analyze network requests made by the page"""
        
        network = {
            'external_domains': set(),
            'api_endpoints': [],
            'cdn_resources': [],
            'third_party_services': []
        }
        
        # Check all external resources
        for tag in soup.find_all(['script', 'link', 'img', 'iframe']):
            src = tag.get('src') or tag.get('href')
            if src and src.startswith('http'):
                domain = urlparse(src).netloc
                network['external_domains'].add(domain)
                
                # Categorize
                if any(keyword in src.lower() for keyword in ['api', 'data', 'result']):
                    network['api_endpoints'].append(src)
                elif any(keyword in src.lower() for keyword in ['cdn', 'static', 'assets']):
                    network['cdn_resources'].append(src)
                elif domain not in site_url:
                    network['third_party_services'].append(src)
        
        network['external_domains'] = list(network['external_domains'])
        
        print(f"     Network: {len(network['external_domains'])} external domains, {len(network['api_endpoints'])} API endpoints")
        
        return network
    
    def analyze_caching_strategy(self, response):
        """Analyze caching strategy from headers"""
        
        caching = {
            'cache_control': response.headers.get('cache-control', ''),
            'expires': response.headers.get('expires', ''),
            'last_modified': response.headers.get('last-modified', ''),
            'etag': response.headers.get('etag', ''),
            'is_dynamic': False,
            'cache_duration': 'unknown'
        }
        
        cache_control = caching['cache_control'].lower()
        
        if 'no-cache' in cache_control or 'no-store' in cache_control:
            caching['is_dynamic'] = True
            caching['cache_duration'] = 'no_cache'
        elif 'max-age' in cache_control:
            max_age_match = re.search(r'max-age=(\d+)', cache_control)
            if max_age_match:
                caching['cache_duration'] = f"{max_age_match.group(1)} seconds"
        
        print(f"     Caching: {'Dynamic' if caching['is_dynamic'] else 'Static'} content")
        
        return caching
    
    def test_real_time_updates(self, site_url):
        """Test if the site updates in real-time"""
        
        try:
            # Get initial content
            initial_response = self.session.get(site_url, timeout=10)
            initial_content = initial_response.text
            
            # Wait 10 seconds
            time.sleep(10)
            
            # Get content again
            second_response = self.session.get(site_url, timeout=10)
            second_content = second_response.text
            
            # Compare
            if initial_content != second_content:
                print(f"     Real-time updates: YES (content changed)")
                return True
            else:
                print(f"     Real-time updates: NO (content static)")
                return False
                
        except:
            print(f"     Real-time updates: UNKNOWN (test failed)")
            return False
    
    def discover_apis(self):
        """Discover API endpoints across all sites"""
        
        api_discovery = {
            'discovered_endpoints': [],
            'working_endpoints': [],
            'api_patterns': [],
            'shared_apis': []
        }
        
        # Common API patterns to test
        api_patterns = [
            '/api/results', '/api/data', '/api/live', '/api/current',
            '/data.php', '/result.php', '/api.php', '/live.php',
            '/json/results', '/json/data', '/ajax/data', '/ajax/results'
        ]
        
        for site in self.actual_satta_sites:
            print(f"   Testing API endpoints for {site}")
            
            for pattern in api_patterns:
                try:
                    api_url = site + pattern
                    response = self.session.get(api_url, timeout=5)
                    
                    api_discovery['discovered_endpoints'].append({
                        'url': api_url,
                        'status': response.status_code,
                        'content_type': response.headers.get('content-type', ''),
                        'size': len(response.content)
                    })
                    
                    if response.status_code == 200:
                        api_discovery['working_endpoints'].append(api_url)
                        print(f"     ✅ Working API: {pattern}")
                
                except:
                    continue
                
                time.sleep(0.2)
        
        print(f"   Found {len(api_discovery['working_endpoints'])} working API endpoints")
        
        return api_discovery
    
    def investigate_databases(self):
        """Investigate database usage and connections"""
        
        db_investigation = {
            'database_evidence': [],
            'connection_strings': [],
            'sql_errors': [],
            'database_type': 'unknown',
            'shared_database': False
        }
        
        # Test for database errors
        test_params = ["'", "1'", "1 OR 1=1"]
        
        for site in self.actual_satta_sites:
            print(f"   Testing database for {site}")
            
            for param in test_params:
                try:
                    test_url = f"{site}?id={param}"
                    response = self.session.get(test_url, timeout=5)
                    content = response.text.lower()
                    
                    # Look for database errors
                    db_errors = ['mysql', 'sql', 'database', 'table', 'query', 'syntax error']
                    
                    for error in db_errors:
                        if error in content:
                            db_investigation['sql_errors'].append({
                                'site': site,
                                'param': param,
                                'error_type': error
                            })
                            
                            if error == 'mysql':
                                db_investigation['database_type'] = 'MySQL'
                            
                            print(f"     🚨 Database error found: {error}")
                            break
                
                except:
                    continue
                
                time.sleep(0.3)
        
        # Check for shared database evidence
        if len(db_investigation['sql_errors']) > 1:
            db_investigation['shared_database'] = True
            print(f"   Evidence suggests shared database system")
        
        return db_investigation
    
    def analyze_update_mechanisms(self):
        """Analyze how sites update their data"""
        
        update_analysis = {
            'update_methods': [],
            'update_frequency': 'unknown',
            'manual_control': False,
            'automated_updates': False,
            'real_time_capability': False
        }
        
        # Analyze each site's update mechanism
        for site in self.actual_satta_sites:
            try:
                response = self.session.get(site, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for auto-refresh
                refresh_meta = soup.find('meta', attrs={'http-equiv': 'refresh'})
                if refresh_meta:
                    update_analysis['update_methods'].append('auto_refresh')
                    update_analysis['automated_updates'] = True
                
                # Check for JavaScript polling
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string:
                        content = script.string.lower()
                        
                        if 'setinterval' in content:
                            update_analysis['update_methods'].append('javascript_polling')
                            update_analysis['automated_updates'] = True
                        
                        if any(keyword in content for keyword in ['admin', 'update', 'control']):
                            update_analysis['manual_control'] = True
                
                # Check cache headers for real-time capability
                cache_control = response.headers.get('cache-control', '')
                if 'no-cache' in cache_control:
                    update_analysis['real_time_capability'] = True
            
            except:
                continue
        
        print(f"   Update mechanisms: {len(set(update_analysis['update_methods']))} types found")
        
        return update_analysis
    
    def compare_data_sources(self):
        """Compare data sources across sites"""
        
        comparison = {
            'identical_results': {},
            'timing_synchronization': {},
            'infrastructure_similarity': {},
            'shared_backend_evidence': []
        }
        
        # Get current results from all sites
        site_results = {}
        
        for site in self.actual_satta_sites:
            try:
                response = self.session.get(site, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract results
                results = self.extract_current_results(soup)
                site_results[site] = {
                    'results': results,
                    'timestamp': datetime.now().isoformat(),
                    'response_time': response.elapsed.total_seconds()
                }
                
            except:
                continue
        
        # Compare results
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        for game in games:
            game_results = {}
            for site, data in site_results.items():
                if game in data['results']:
                    game_results[site] = data['results'][game]
            
            if len(game_results) > 1:
                unique_results = set(game_results.values())
                if len(unique_results) == 1:
                    comparison['identical_results'][game] = {
                        'result': list(unique_results)[0],
                        'sites': list(game_results.keys()),
                        'match_count': len(game_results)
                    }
                    print(f"   🎯 {game}: Identical result '{list(unique_results)[0]}' across {len(game_results)} sites")
        
        # Evidence of shared backend
        if comparison['identical_results']:
            comparison['shared_backend_evidence'].append("Identical results across multiple sites")
        
        return comparison
    
    def extract_current_results(self, soup):
        """Extract current results from HTML"""
        
        results = {}
        games = ['GALI', 'DSWR', 'DESAWAR', 'FRBD', 'FARIDABAD', 'GZBD', 'GHAZIABAD']
        
        # Look in tables
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    text = ' '.join([cell.get_text() for cell in cells])
                    
                    for game in games:
                        pattern = rf'{game}[:\s]*(\d{{1,2}})'
                        match = re.search(pattern, text, re.IGNORECASE)
                        if match:
                            results[game] = match.group(1)
        
        # Look in general text
        page_text = soup.get_text()
        for game in games:
            if game not in results:
                pattern = rf'{game}[:\s]*(\d{{1,2}})'
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    results[game] = match.group(1)
        
        return results
    
    def generate_data_source_conclusions(self, investigation):
        """Generate conclusions about data sources"""
        
        conclusions = {
            'primary_data_source': 'unknown',
            'data_flow_method': 'unknown',
            'backend_architecture': 'unknown',
            'update_mechanism': 'unknown',
            'shared_infrastructure': False,
            'api_usage': False,
            'database_driven': False,
            'evidence_summary': [],
            'confidence_level': 'unknown'
        }
        
        # Analyze evidence
        evidence_count = 0
        
        # Check for database evidence
        db_investigation = investigation.get('database_investigation', {})
        if db_investigation.get('sql_errors'):
            conclusions['database_driven'] = True
            conclusions['primary_data_source'] = 'database'
            conclusions['evidence_summary'].append("Database errors confirm database usage")
            evidence_count += 1
        
        # Check for API evidence
        api_discovery = investigation.get('api_discovery', {})
        if api_discovery.get('working_endpoints'):
            conclusions['api_usage'] = True
            conclusions['evidence_summary'].append("Working API endpoints found")
            evidence_count += 1
        
        # Check for shared infrastructure
        cross_site = investigation.get('cross_site_comparison', {})
        if cross_site.get('identical_results'):
            conclusions['shared_infrastructure'] = True
            conclusions['backend_architecture'] = 'centralized'
            conclusions['evidence_summary'].append("Identical results indicate shared backend")
            evidence_count += 1
        
        # Determine data flow method
        server_side_count = 0
        for site, analysis in investigation.get('data_flow_analysis', {}).items():
            if analysis.get('content_generation') == 'server_side_rendering':
                server_side_count += 1
        
        if server_side_count > len(self.actual_satta_sites) / 2:
            conclusions['data_flow_method'] = 'server_side_rendering'
            conclusions['evidence_summary'].append("Server-side rendering detected")
            evidence_count += 1
        
        # Determine update mechanism
        update_analysis = investigation.get('update_mechanism_analysis', {})
        if update_analysis.get('real_time_capability'):
            conclusions['update_mechanism'] = 'real_time'
            conclusions['evidence_summary'].append("Real-time update capability confirmed")
            evidence_count += 1
        
        # Set confidence level
        if evidence_count >= 4:
            conclusions['confidence_level'] = 'high'
        elif evidence_count >= 2:
            conclusions['confidence_level'] = 'medium'
        else:
            conclusions['confidence_level'] = 'low'
        
        return conclusions
    
    def save_investigation_report(self, investigation):
        """Save comprehensive investigation report"""
        
        os.makedirs('data', exist_ok=True)
        report_file = f'data/satta_data_source_investigation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w') as f:
            json.dump(investigation, f, indent=2, default=str)
        
        print(f"\n💾 Investigation report saved to: {report_file}")
        
        # Print summary
        self.print_investigation_summary(investigation)
    
    def print_investigation_summary(self, investigation):
        """Print investigation summary"""
        
        print(f"\n📋 DATA SOURCE INVESTIGATION SUMMARY")
        print("=" * 60)
        
        conclusions = investigation.get('data_source_conclusions', {})
        
        print(f"Primary Data Source: {conclusions.get('primary_data_source', 'unknown').upper()}")
        print(f"Data Flow Method: {conclusions.get('data_flow_method', 'unknown').upper()}")
        print(f"Backend Architecture: {conclusions.get('backend_architecture', 'unknown').upper()}")
        print(f"Update Mechanism: {conclusions.get('update_mechanism', 'unknown').upper()}")
        print(f"Shared Infrastructure: {conclusions.get('shared_infrastructure', False)}")
        print(f"Database Driven: {conclusions.get('database_driven', False)}")
        print(f"API Usage: {conclusions.get('api_usage', False)}")
        print(f"Confidence Level: {conclusions.get('confidence_level', 'unknown').upper()}")
        
        print(f"\nEvidence Summary:")
        for evidence in conclusions.get('evidence_summary', []):
            print(f"  • {evidence}")
        
        # Show key findings
        api_discovery = investigation.get('api_discovery', {})
        db_investigation = investigation.get('database_investigation', {})
        cross_site = investigation.get('cross_site_comparison', {})
        
        print(f"\nKey Findings:")
        print(f"  • Working API endpoints: {len(api_discovery.get('working_endpoints', []))}")
        print(f"  • Database errors found: {len(db_investigation.get('sql_errors', []))}")
        print(f"  • Identical results: {len(cross_site.get('identical_results', {}))}")

if __name__ == "__main__":
    investigator = SattaDataSourceInvestigator()
    investigation = investigator.investigate_data_sources()
