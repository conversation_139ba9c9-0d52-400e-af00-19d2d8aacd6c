"""
Data processing and feature engineering for Satta King prediction
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
from sklearn.preprocessing import StandardScaler, LabelEncoder
import config

logger = logging.getLogger(__name__)

class SattaDataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
    def load_raw_data(self, file_path=None):
        """
        Load raw scraped data
        """
        if file_path is None:
            # Find the most recent raw data file
            raw_files = [f for f in os.listdir(config.RAW_DATA_DIR) if f.endswith('.csv')]
            if not raw_files:
                raise FileNotFoundError("No raw data files found")
            file_path = os.path.join(config.RAW_DATA_DIR, sorted(raw_files)[-1])
        
        logger.info(f"Loading data from {file_path}")
        df = pd.read_csv(file_path)
        return df
    
    def clean_data(self, df):
        """
        Clean and validate the data
        """
        logger.info("Cleaning data...")
        
        # Convert date column to datetime
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Remove rows with invalid dates
        df = df.dropna(subset=['date'])
        
        # Sort by date
        df = df.sort_values('date').reset_index(drop=True)
        
        # Convert game results to numeric, handling 'XX' and missing values
        for game in config.GAMES:
            if game in df.columns:
                df[game] = pd.to_numeric(df[game], errors='coerce')
        
        # Add day of week and other time features
        df['day_of_week'] = df['date'].dt.dayofweek
        df['day_of_month'] = df['date'].dt.day
        df['month_num'] = df['date'].dt.month
        df['year_num'] = df['date'].dt.year
        
        logger.info(f"Cleaned data shape: {df.shape}")
        return df
    
    def create_features(self, df):
        """
        Create features for machine learning
        """
        logger.info("Creating features...")
        
        features_df = df.copy()
        
        for game in config.GAMES:
            if game not in df.columns:
                continue
                
            # Rolling statistics
            for window in [3, 7, 15, 30]:
                features_df[f'{game}_mean_{window}d'] = df[game].rolling(window=window, min_periods=1).mean()
                features_df[f'{game}_std_{window}d'] = df[game].rolling(window=window, min_periods=1).std()
                features_df[f'{game}_min_{window}d'] = df[game].rolling(window=window, min_periods=1).min()
                features_df[f'{game}_max_{window}d'] = df[game].rolling(window=window, min_periods=1).max()
            
            # Lag features
            for lag in [1, 2, 3, 7, 14]:
                features_df[f'{game}_lag_{lag}'] = df[game].shift(lag)
            
            # Difference features
            features_df[f'{game}_diff_1'] = df[game].diff(1)
            features_df[f'{game}_diff_7'] = df[game].diff(7)
            
            # Pattern features
            features_df[f'{game}_is_even'] = (df[game] % 2 == 0).astype(int)
            features_df[f'{game}_digit_sum'] = df[game].apply(lambda x: sum(int(d) for d in str(int(x))) if pd.notna(x) else np.nan)
            
            # Frequency features
            features_df[f'{game}_freq_last_30'] = df[game].rolling(window=30, min_periods=1).apply(
                lambda x: (x == x.iloc[-1]).sum() if len(x) > 0 and pd.notna(x.iloc[-1]) else 0
            )
        
        # Cross-game features
        if all(game in df.columns for game in config.GAMES):
            # Correlations between games
            features_df['games_mean'] = df[config.GAMES].mean(axis=1)
            features_df['games_std'] = df[config.GAMES].std(axis=1)
            
            # Same number patterns
            for i, game1 in enumerate(config.GAMES):
                for game2 in config.GAMES[i+1:]:
                    features_df[f'{game1}_{game2}_same'] = (df[game1] == df[game2]).astype(int)
        
        logger.info(f"Features created. Shape: {features_df.shape}")
        return features_df
    
    def create_sequences(self, df, target_game, sequence_length=None):
        """
        Create sequences for time series prediction
        """
        if sequence_length is None:
            sequence_length = config.SEQUENCE_LENGTH
            
        logger.info(f"Creating sequences for {target_game} with length {sequence_length}")
        
        # Get the target values
        target_values = df[target_game].dropna().values
        
        if len(target_values) < sequence_length + 1:
            raise ValueError(f"Not enough data for sequences. Need at least {sequence_length + 1} records")
        
        X, y = [], []
        
        for i in range(len(target_values) - sequence_length):
            X.append(target_values[i:(i + sequence_length)])
            y.append(target_values[i + sequence_length])
        
        return np.array(X), np.array(y)
    
    def prepare_ml_data(self, df, target_game):
        """
        Prepare data for machine learning models
        """
        logger.info(f"Preparing ML data for {target_game}")
        
        # Select feature columns (exclude non-numeric and target)
        feature_cols = []
        for col in df.columns:
            if col not in ['date', 'day', 'month', 'year'] and pd.api.types.is_numeric_dtype(df[col]):
                feature_cols.append(col)
        
        # Remove target from features
        if target_game in feature_cols:
            feature_cols.remove(target_game)
        
        X = df[feature_cols].copy()
        y = df[target_game].copy()
        
        # Remove rows where target is missing
        mask = ~y.isna()
        X = X[mask]
        y = y[mask]
        
        # Fill missing values in features
        X = X.fillna(X.mean())
        
        logger.info(f"ML data prepared. X shape: {X.shape}, y shape: {y.shape}")
        return X, y, feature_cols
    
    def save_processed_data(self, df, filename):
        """
        Save processed data
        """
        filepath = os.path.join(config.PROCESSED_DATA_DIR, filename)
        df.to_csv(filepath, index=False)
        logger.info(f"Saved processed data to {filepath}")
        return filepath
    
    def process_all(self, raw_data_path=None):
        """
        Complete data processing pipeline
        """
        # Load raw data
        df = self.load_raw_data(raw_data_path)
        
        # Clean data
        df_clean = self.clean_data(df)
        
        # Create features
        df_features = self.create_features(df_clean)
        
        # Save processed data
        processed_file = self.save_processed_data(df_features, 'processed_satta_data.csv')
        
        return df_features, processed_file

if __name__ == "__main__":
    processor = SattaDataProcessor()
    df, file_path = processor.process_all()
    print(f"Processed data saved to: {file_path}")
    print(f"Data shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
