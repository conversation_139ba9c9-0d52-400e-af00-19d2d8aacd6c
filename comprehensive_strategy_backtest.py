"""
Comprehensive backtest of multiple strategies on previous 20 results
Test all possible approaches to find what actually works
"""

import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter
import json
import os

def comprehensive_strategy_backtest():
    """Test multiple strategies comprehensively"""
    
    print("🔬 COMPREHENSIVE STRATEGY BACKTEST")
    print("=" * 70)
    print("Testing ALL possible strategies on last 20 actual results")
    print("=" * 70)
    
    # Load data
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    print(f"✅ Loaded {len(df)} total records")
    
    all_results = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            print(f"\n🎲 {game} COMPREHENSIVE STRATEGY TEST:")
            print("=" * 50)
            
            if len(game_data) >= 50:  # Need enough data for testing
                results = test_all_strategies(game_data, game)
                all_results[game] = results
            else:
                print(f"   ❌ Insufficient data ({len(game_data)} records)")
    
    # Analyze results and find best strategies
    analyze_comprehensive_results(all_results)

def test_all_strategies(data, game_name):
    """Test all possible prediction strategies"""
    
    # Use last 30 records (10 for setup, predict next 20)
    test_data = data.tail(30).reset_index(drop=True)
    
    print(f"📊 Testing {len(test_data)} records (predict last 20)")
    
    strategies = {}
    
    # Strategy 1: Recent Avoidance
    strategies['Recent_Avoidance'] = test_recent_avoidance(test_data)
    
    # Strategy 2: Frequency Analysis
    strategies['Frequency_Analysis'] = test_frequency_analysis(test_data)
    
    # Strategy 3: Mean Reversion
    strategies['Mean_Reversion'] = test_mean_reversion(test_data)
    
    # Strategy 4: Range Rotation
    strategies['Range_Rotation'] = test_range_rotation(test_data)
    
    # Strategy 5: Pattern Breaking
    strategies['Pattern_Breaking'] = test_pattern_breaking(test_data)
    
    # Strategy 6: Modular Analysis
    strategies['Modular_Analysis'] = test_modular_analysis(test_data)
    
    # Strategy 7: Time-based Patterns
    strategies['Time_Based'] = test_time_based_patterns(test_data)
    
    # Strategy 8: Digit Analysis
    strategies['Digit_Analysis'] = test_digit_analysis(test_data)
    
    # Strategy 9: Hot/Cold Numbers
    strategies['Hot_Cold_Numbers'] = test_hot_cold_numbers(test_data)
    
    # Strategy 10: Consecutive Analysis
    strategies['Consecutive_Analysis'] = test_consecutive_analysis(test_data)
    
    # Strategy 11: Sum Analysis
    strategies['Sum_Analysis'] = test_sum_analysis(test_data)
    
    # Strategy 12: Random Baseline
    strategies['Random_Baseline'] = test_random_baseline(test_data)
    
    # Display results
    display_strategy_results(strategies, game_name)
    
    return strategies

def test_recent_avoidance(data):
    """Test recent number avoidance strategy"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(10, len(data)):
        # Avoid numbers from last 10 draws
        recent_numbers = set(data.iloc[i-10:i])
        available = list(set(range(100)) - recent_numbers)
        
        # Predict middle of available range
        if available:
            predicted = sorted(available)[len(available)//2]
        else:
            predicted = 50
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Avoid numbers from last 10 draws'
    }

def test_frequency_analysis(data):
    """Test frequency-based prediction"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(20, len(data)):
        # Analyze frequency in last 20 draws
        recent_data = data.iloc[i-20:i]
        value_counts = recent_data.value_counts()
        
        # Predict least frequent number (due for appearance)
        all_numbers = set(range(100))
        recent_numbers = set(recent_data)
        missing_numbers = all_numbers - recent_numbers
        
        if missing_numbers:
            predicted = min(missing_numbers)
        elif len(value_counts) > 0:
            predicted = value_counts.index[-1]  # Least frequent
        else:
            predicted = 50
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Predict least frequent numbers'
    }

def test_mean_reversion(data):
    """Test mean reversion strategy"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(20, len(data)):
        # Calculate recent mean
        recent_mean = data.iloc[i-20:i].mean()
        current_value = data.iloc[i-1]
        
        # Predict reversion to mean
        if current_value > recent_mean + 10:
            predicted = int(recent_mean - 5)  # Expect drop
        elif current_value < recent_mean - 10:
            predicted = int(recent_mean + 5)  # Expect rise
        else:
            predicted = int(recent_mean)  # Stay near mean
        
        predicted = max(0, min(99, predicted))  # Keep in range
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Predict reversion to mean'
    }

def test_range_rotation(data):
    """Test range rotation strategy"""
    
    ranges = [(0, 33), (34, 66), (67, 99)]
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(10, len(data)):
        # Count recent range usage
        recent_data = data.iloc[i-10:i]
        range_counts = [0, 0, 0]
        
        for value in recent_data:
            for j, (start, end) in enumerate(ranges):
                if start <= value <= end:
                    range_counts[j] += 1
                    break
        
        # Predict least used range
        least_used_range = range_counts.index(min(range_counts))
        target_range = ranges[least_used_range]
        
        # Predict middle of target range
        predicted = (target_range[0] + target_range[1]) // 2
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Target underused ranges'
    }

def test_pattern_breaking(data):
    """Test pattern breaking strategy"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(3, len(data)):
        # Look for patterns in last 3 numbers
        last_three = data.iloc[i-3:i].values
        
        # Check for arithmetic progression
        diff1 = last_three[1] - last_three[0]
        diff2 = last_three[2] - last_three[1]
        
        if diff1 == diff2 and diff1 != 0:
            # Pattern detected, predict break
            expected_next = last_three[2] + diff1
            if 0 <= expected_next <= 99:
                # Predict something different
                predicted = int((expected_next + 50) % 100)
            else:
                predicted = 50
        else:
            # No pattern, predict middle
            predicted = 50
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Break arithmetic patterns'
    }

def test_modular_analysis(data):
    """Test modular arithmetic patterns"""
    
    predictions = []
    actuals = []
    correct = 0
    
    # Test mod 7 (best performing modulus from our analysis)
    mod = 7
    
    for i in range(10, len(data)):
        # Analyze modular patterns
        recent_remainders = [int(x) % mod for x in data.iloc[i-10:i]]
        remainder_counts = Counter(recent_remainders)
        
        # Predict least frequent remainder
        if remainder_counts:
            least_frequent_remainder = remainder_counts.most_common()[-1][0]
        else:
            least_frequent_remainder = 0
        
        # Find number with this remainder closest to recent mean
        recent_mean = data.iloc[i-10:i].mean()
        candidates = [x for x in range(100) if x % mod == least_frequent_remainder]
        
        if candidates:
            predicted = min(candidates, key=lambda x: abs(x - recent_mean))
        else:
            predicted = 50
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': f'Modular analysis (mod {mod})'
    }

def test_time_based_patterns(data):
    """Test time-based patterns (position-based since we don't have timestamps)"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(10, len(data)):
        # Use position as proxy for time
        position_mod = i % 7  # Weekly cycle
        
        # Predict based on position
        if position_mod == 0:  # "Monday"
            predicted = 25
        elif position_mod == 1:  # "Tuesday"
            predicted = 35
        elif position_mod == 2:  # "Wednesday"
            predicted = 45
        elif position_mod == 3:  # "Thursday"
            predicted = 55
        elif position_mod == 4:  # "Friday"
            predicted = 65
        elif position_mod == 5:  # "Saturday"
            predicted = 75
        else:  # "Sunday"
            predicted = 15
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Time-based cycles'
    }

def test_digit_analysis(data):
    """Test digit-based predictions"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(10, len(data)):
        # Analyze digit frequency in recent numbers
        recent_data = data.iloc[i-10:i]
        all_digits = []
        
        for num in recent_data:
            for digit in str(int(num)):
                all_digits.append(int(digit))
        
        digit_counts = Counter(all_digits)
        
        # Find least frequent digit
        if digit_counts:
            least_frequent_digit = digit_counts.most_common()[-1][0]
        else:
            least_frequent_digit = 5
        
        # Create number with this digit
        predicted = least_frequent_digit * 11  # 00, 11, 22, etc.
        predicted = min(99, predicted)
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Digit frequency analysis'
    }

def test_hot_cold_numbers(data):
    """Test hot/cold number strategy"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(30, len(data)):
        # Analyze last 30 numbers
        recent_data = data.iloc[i-30:i]
        value_counts = recent_data.value_counts()
        
        # Predict "hot" number (most frequent)
        if len(value_counts) > 0:
            predicted = value_counts.index[0]  # Most frequent
        else:
            predicted = 50
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Hot numbers (most frequent)'
    }

def test_consecutive_analysis(data):
    """Test consecutive number patterns"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(5, len(data)):
        # Look at last number
        last_number = int(data.iloc[i-1])
        
        # Predict consecutive number
        predicted = (last_number + 1) % 100
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Consecutive numbers'
    }

def test_sum_analysis(data):
    """Test sum-based predictions"""
    
    predictions = []
    actuals = []
    correct = 0
    
    for i in range(5, len(data)):
        # Calculate sum of last 3 numbers
        last_three_sum = sum(data.iloc[i-3:i])
        
        # Predict based on sum modulo
        predicted = int(last_three_sum % 100)
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Sum of last 3 numbers'
    }

def test_random_baseline(data):
    """Test random prediction baseline"""
    
    predictions = []
    actuals = []
    correct = 0
    
    np.random.seed(42)  # For reproducible results
    
    for i in range(10, len(data)):
        # Random prediction
        predicted = np.random.randint(0, 100)
        
        actual = int(data.iloc[i])
        predictions.append(predicted)
        actuals.append(actual)
        
        if predicted == actual:
            correct += 1
    
    accuracy = (correct / len(predictions)) * 100 if predictions else 0
    avg_error = np.mean([abs(p-a) for p, a in zip(predictions, actuals)]) if predictions else 0
    
    return {
        'accuracy': accuracy,
        'avg_error': avg_error,
        'predictions': predictions,
        'actuals': actuals,
        'description': 'Random baseline'
    }

def display_strategy_results(strategies, game_name):
    """Display results for all strategies"""
    
    print(f"\n📊 STRATEGY RESULTS FOR {game_name}:")
    print("-" * 60)
    print(f"{'Strategy':<20} {'Accuracy':<10} {'Avg Error':<10} {'Performance'}")
    print("-" * 60)
    
    # Sort by accuracy
    sorted_strategies = sorted(strategies.items(), 
                              key=lambda x: x[1]['accuracy'], 
                              reverse=True)
    
    for strategy_name, results in sorted_strategies:
        accuracy = results['accuracy']
        avg_error = results['avg_error']
        
        if accuracy >= 10:
            performance = "🏆 EXCELLENT"
        elif accuracy >= 5:
            performance = "🥈 GOOD"
        elif accuracy >= 2:
            performance = "🥉 FAIR"
        else:
            performance = "❌ POOR"
        
        print(f"{strategy_name:<20} {accuracy:<10.1f} {avg_error:<10.1f} {performance}")

def analyze_comprehensive_results(all_results):
    """Analyze results across all games and strategies"""
    
    print(f"\n🎯 COMPREHENSIVE ANALYSIS ACROSS ALL GAMES")
    print("=" * 70)
    
    # Aggregate results by strategy
    strategy_aggregates = {}
    
    for game, strategies in all_results.items():
        for strategy_name, results in strategies.items():
            if strategy_name not in strategy_aggregates:
                strategy_aggregates[strategy_name] = []
            strategy_aggregates[strategy_name].append(results['accuracy'])
    
    # Calculate average performance per strategy
    strategy_averages = {}
    for strategy, accuracies in strategy_aggregates.items():
        strategy_averages[strategy] = np.mean(accuracies)
    
    # Sort by average performance
    sorted_strategies = sorted(strategy_averages.items(), 
                              key=lambda x: x[1], 
                              reverse=True)
    
    print(f"📊 STRATEGY RANKING (Average Accuracy Across All Games):")
    print("-" * 70)
    print(f"{'Rank':<5} {'Strategy':<25} {'Avg Accuracy':<15} {'Assessment'}")
    print("-" * 70)
    
    for i, (strategy, avg_accuracy) in enumerate(sorted_strategies, 1):
        if avg_accuracy >= 10:
            assessment = "🏆 HIGHLY EFFECTIVE"
        elif avg_accuracy >= 5:
            assessment = "🥈 MODERATELY EFFECTIVE"
        elif avg_accuracy >= 2:
            assessment = "🥉 MARGINALLY EFFECTIVE"
        elif avg_accuracy > 1:
            assessment = "💡 SLIGHTLY BETTER THAN RANDOM"
        else:
            assessment = "❌ NO BETTER THAN RANDOM"
        
        print(f"{i:<5} {strategy:<25} {avg_accuracy:<15.1f} {assessment}")
    
    # Find best overall strategy
    best_strategy, best_accuracy = sorted_strategies[0]
    
    print(f"\n🏆 BEST PERFORMING STRATEGY:")
    print(f"   Strategy: {best_strategy}")
    print(f"   Average Accuracy: {best_accuracy:.1f}%")
    
    # Check for manipulation signs
    check_manipulation_signs(all_results, strategy_averages)
    
    # Generate final recommendations
    generate_final_recommendations(best_strategy, best_accuracy, sorted_strategies)
    
    # Save comprehensive results
    save_comprehensive_results(all_results, strategy_averages, sorted_strategies)

def check_manipulation_signs(all_results, strategy_averages):
    """Check for signs of manipulation or exploitable patterns"""
    
    print(f"\n🔍 MANIPULATION DETECTION:")
    print("-" * 40)
    
    manipulation_signs = []
    
    # Check if any strategy performs significantly better than random
    random_baseline = strategy_averages.get('Random_Baseline', 1.0)
    
    for strategy, accuracy in strategy_averages.items():
        if strategy != 'Random_Baseline' and accuracy > random_baseline * 3:
            manipulation_signs.append(f"{strategy}: {accuracy:.1f}% (3x better than random)")
    
    # Check for consistent patterns across games
    consistent_strategies = []
    for strategy, accuracies in strategy_averages.items():
        if strategy != 'Random_Baseline':
            # Check if strategy works consistently across games
            game_accuracies = []
            for game, strategies in all_results.items():
                if strategy in strategies:
                    game_accuracies.append(strategies[strategy]['accuracy'])
            
            if len(game_accuracies) >= 3 and min(game_accuracies) > 3:
                consistent_strategies.append(f"{strategy}: Works across {len(game_accuracies)} games")
    
    if manipulation_signs:
        print("🚨 POTENTIAL MANIPULATION DETECTED:")
        for sign in manipulation_signs:
            print(f"   • {sign}")
    else:
        print("✅ NO CLEAR MANIPULATION DETECTED")
    
    if consistent_strategies:
        print("\n🎯 CONSISTENT PATTERNS FOUND:")
        for pattern in consistent_strategies:
            print(f"   • {pattern}")
    else:
        print("\n❌ NO CONSISTENT EXPLOITABLE PATTERNS")

def generate_final_recommendations(best_strategy, best_accuracy, sorted_strategies):
    """Generate final recommendations based on results"""
    
    print(f"\n💡 FINAL RECOMMENDATIONS:")
    print("-" * 40)
    
    if best_accuracy >= 10:
        print(f"🎯 STRONG RECOMMENDATION:")
        print(f"   Use {best_strategy} strategy")
        print(f"   Expected accuracy: {best_accuracy:.1f}%")
        print(f"   Risk level: MODERATE")
        print(f"   Max stake: 1-2% of bankroll")
        
    elif best_accuracy >= 5:
        print(f"⚠️ WEAK RECOMMENDATION:")
        print(f"   Consider {best_strategy} strategy")
        print(f"   Expected accuracy: {best_accuracy:.1f}%")
        print(f"   Risk level: HIGH")
        print(f"   Max stake: 0.5% of bankroll")
        
    elif best_accuracy > 2:
        print(f"💡 MARGINAL OPPORTUNITY:")
        print(f"   {best_strategy} shows slight edge")
        print(f"   Expected accuracy: {best_accuracy:.1f}%")
        print(f"   Risk level: VERY HIGH")
        print(f"   Recommendation: PAPER TRADE ONLY")
        
    else:
        print(f"❌ NO RELIABLE STRATEGY FOUND:")
        print(f"   Best strategy: {best_strategy} ({best_accuracy:.1f}%)")
        print(f"   Recommendation: DO NOT GAMBLE")
        print(f"   All strategies perform at random level")
    
    # Overall gambling advice
    print(f"\n⚠️ GENERAL ADVICE:")
    print(f"   • Even best strategy has {100-best_accuracy:.1f}% failure rate")
    print(f"   • Past performance ≠ future results")
    print(f"   • Gambling always involves significant risk")
    print(f"   • Never bet money you cannot afford to lose")

def save_comprehensive_results(all_results, strategy_averages, sorted_strategies):
    """Save comprehensive backtest results"""
    
    comprehensive_report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'Comprehensive Strategy Backtest',
        'games_tested': list(all_results.keys()),
        'strategies_tested': list(strategy_averages.keys()),
        'strategy_rankings': [{'strategy': s, 'avg_accuracy': a} for s, a in sorted_strategies],
        'detailed_results': all_results,
        'best_strategy': sorted_strategies[0][0] if sorted_strategies else None,
        'best_accuracy': sorted_strategies[0][1] if sorted_strategies else 0
    }
    
    os.makedirs('data', exist_ok=True)
    filename = f'data/comprehensive_backtest_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(filename, 'w') as f:
        json.dump(comprehensive_report, f, indent=2, default=str)
    
    print(f"\n💾 Comprehensive results saved to: {filename}")

if __name__ == "__main__":
    comprehensive_strategy_backtest()
