"""
Advanced ML system with much better algorithms and extensive data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
import joblib
import warnings
warnings.filterwarnings('ignore')

import config

class AdvancedAccuratePredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.results = {}
        
    def load_extensive_data(self):
        """Load the extensive historical data"""
        print("📊 Loading extensive historical data...")
        
        data_file = os.path.join(config.RAW_DATA_DIR, 'extensive_historical_data.csv')
        if not os.path.exists(data_file):
            print("❌ Extensive data file not found")
            return None
        
        df = pd.read_csv(data_file)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # Convert to numeric
        for game in config.GAMES:
            if game in df.columns:
                df[game] = pd.to_numeric(df[game], errors='coerce')
        
        print(f"✅ Loaded {len(df)} records from {df['date'].min()} to {df['date'].max()}")
        return df
    
    def create_advanced_features(self, df, target_game):
        """Create comprehensive advanced features"""
        print(f"🔧 Creating advanced features for {target_game}...")
        
        if target_game not in df.columns:
            return None, None, None
        
        features_df = df.copy()
        
        # Time-based features
        features_df['year'] = features_df['date'].dt.year
        features_df['month'] = features_df['date'].dt.month
        features_df['day'] = features_df['date'].dt.day
        features_df['day_of_week'] = features_df['date'].dt.dayofweek
        features_df['day_of_year'] = features_df['date'].dt.dayofyear
        features_df['week_of_year'] = features_df['date'].dt.isocalendar().week
        features_df['quarter'] = features_df['date'].dt.quarter
        features_df['is_weekend'] = (features_df['day_of_week'] >= 5).astype(int)
        features_df['is_month_start'] = (features_df['day'] <= 5).astype(int)
        features_df['is_month_end'] = (features_df['day'] >= 25).astype(int)
        
        # Cyclical encoding
        features_df['day_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['day_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['month_sin'] = np.sin(2 * np.pi * features_df['month'] / 12)
        features_df['month_cos'] = np.cos(2 * np.pi * features_df['month'] / 12)
        features_df['year_sin'] = np.sin(2 * np.pi * (features_df['year'] - features_df['year'].min()) / 
                                        (features_df['year'].max() - features_df['year'].min() + 1))
        features_df['year_cos'] = np.cos(2 * np.pi * (features_df['year'] - features_df['year'].min()) / 
                                        (features_df['year'].max() - features_df['year'].min() + 1))
        
        # Lag features (multiple horizons)
        for lag in [1, 2, 3, 4, 5, 7, 14, 21, 30]:
            features_df[f'{target_game}_lag_{lag}'] = features_df[target_game].shift(lag)
        
        # Rolling statistics (multiple windows)
        for window in [3, 5, 7, 10, 14, 21, 30, 60, 90]:
            features_df[f'{target_game}_mean_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).mean()
            features_df[f'{target_game}_std_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).std()
            features_df[f'{target_game}_min_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).min()
            features_df[f'{target_game}_max_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).max()
            features_df[f'{target_game}_median_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).median()
            features_df[f'{target_game}_q25_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).quantile(0.25)
            features_df[f'{target_game}_q75_{window}'] = features_df[target_game].rolling(window=window, min_periods=1).quantile(0.75)
            if window >= 3:
                features_df[f'{target_game}_skew_{window}'] = features_df[target_game].rolling(window=window, min_periods=3).skew()
            if window >= 4:
                features_df[f'{target_game}_kurt_{window}'] = features_df[target_game].rolling(window=window, min_periods=4).kurtosis()
        
        # Difference and momentum features
        for period in [1, 2, 3, 7, 14, 30]:
            features_df[f'{target_game}_diff_{period}'] = features_df[target_game].diff(period)
            features_df[f'{target_game}_pct_change_{period}'] = features_df[target_game].pct_change(period)
        
        # Technical indicators
        # RSI-like indicator
        delta = features_df[target_game].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        features_df[f'{target_game}_rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        for window in [20, 50]:
            rolling_mean = features_df[target_game].rolling(window=window).mean()
            rolling_std = features_df[target_game].rolling(window=window).std()
            features_df[f'{target_game}_bb_upper_{window}'] = rolling_mean + (rolling_std * 2)
            features_df[f'{target_game}_bb_lower_{window}'] = rolling_mean - (rolling_std * 2)
            features_df[f'{target_game}_bb_position_{window}'] = (features_df[target_game] - rolling_mean) / (rolling_std * 2)
        
        # Pattern features
        features_df[f'{target_game}_is_even'] = (features_df[target_game] % 2 == 0).astype(int)
        features_df[f'{target_game}_digit_sum'] = features_df[target_game].apply(
            lambda x: sum(int(d) for d in str(int(x))) if pd.notna(x) else np.nan
        )
        features_df[f'{target_game}_range_0_33'] = (features_df[target_game] <= 33).astype(int)
        features_df[f'{target_game}_range_34_66'] = ((features_df[target_game] > 33) & (features_df[target_game] <= 66)).astype(int)
        features_df[f'{target_game}_range_67_99'] = (features_df[target_game] > 66).astype(int)
        
        # Frequency features
        for window in [30, 60, 90, 180]:
            features_df[f'{target_game}_freq_{window}'] = features_df[target_game].rolling(
                window=window, min_periods=10
            ).apply(lambda x: (x == x.iloc[-1]).sum() if len(x) > 0 and pd.notna(x.iloc[-1]) else 0)
        
        # Cross-game features
        other_games = [g for g in config.GAMES if g != target_game and g in features_df.columns]
        for other_game in other_games:
            # Correlations
            for window in [14, 30, 60]:
                features_df[f'{target_game}_{other_game}_corr_{window}'] = features_df[target_game].rolling(
                    window=window, min_periods=10
                ).corr(features_df[other_game])
            
            # Differences
            features_df[f'{target_game}_{other_game}_diff'] = features_df[target_game] - features_df[other_game]
            features_df[f'{target_game}_{other_game}_ratio'] = features_df[target_game] / (features_df[other_game] + 1)
            
            # Same number patterns
            features_df[f'{target_game}_{other_game}_same'] = (features_df[target_game] == features_df[other_game]).astype(int)
        
        # Seasonal features
        features_df[f'{target_game}_month_mean'] = features_df.groupby('month')[target_game].transform('mean')
        features_df[f'{target_game}_dow_mean'] = features_df.groupby('day_of_week')[target_game].transform('mean')
        features_df[f'{target_game}_quarter_mean'] = features_df.groupby('quarter')[target_game].transform('mean')
        
        # Volatility features
        for window in [7, 14, 30]:
            features_df[f'{target_game}_volatility_{window}'] = features_df[target_game].rolling(window=window).std()
            features_df[f'{target_game}_range_{window}'] = (
                features_df[target_game].rolling(window=window).max() - 
                features_df[target_game].rolling(window=window).min()
            )
        
        # Select feature columns
        feature_cols = [col for col in features_df.columns 
                       if col not in ['date', target_game] and pd.api.types.is_numeric_dtype(features_df[col])]
        
        X = features_df[feature_cols].copy()
        y = features_df[target_game].copy()
        
        # Remove rows with missing target
        mask = ~y.isna()
        X = X[mask]
        y = y[mask]
        dates = features_df.loc[mask, 'date']
        
        # Fill missing features with forward fill then median
        X = X.ffill().fillna(X.median())
        
        print(f"✅ Created {X.shape[1]} features, {len(X)} samples")
        return X, y, dates
    
    def train_advanced_models(self, X, y, dates, game_name):
        """Train advanced ML models with hyperparameter tuning"""
        print(f"🤖 Training advanced models for {game_name}...")
        
        # Time series split for validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        # Use last 80% for training, 20% for final testing
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        dates_test = dates.iloc[split_idx:]
        
        print(f"   Training: {len(X_train)} samples")
        print(f"   Testing: {len(X_test)} samples")
        
        # Feature selection
        print("   Selecting best features...")
        selector = SelectKBest(score_func=f_regression, k=min(50, X_train.shape[1]))
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # Scaling
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # Define advanced models with hyperparameter grids
        models = {
            'random_forest': {
                'model': RandomForestRegressor(random_state=42, n_jobs=-1),
                'params': {
                    'n_estimators': [100, 200],
                    'max_depth': [10, 15, 20],
                    'min_samples_split': [2, 5],
                    'min_samples_leaf': [1, 2]
                },
                'use_scaled': False
            },
            'extra_trees': {
                'model': ExtraTreesRegressor(random_state=42, n_jobs=-1),
                'params': {
                    'n_estimators': [100, 200],
                    'max_depth': [10, 15, 20],
                    'min_samples_split': [2, 5]
                },
                'use_scaled': False
            },
            'gradient_boosting': {
                'model': GradientBoostingRegressor(random_state=42),
                'params': {
                    'n_estimators': [100, 200],
                    'max_depth': [4, 6, 8],
                    'learning_rate': [0.05, 0.1, 0.15]
                },
                'use_scaled': False
            },
            'ridge': {
                'model': Ridge(),
                'params': {
                    'alpha': [0.1, 1.0, 10.0, 100.0]
                },
                'use_scaled': True
            },
            'elastic_net': {
                'model': ElasticNet(random_state=42),
                'params': {
                    'alpha': [0.1, 1.0, 10.0],
                    'l1_ratio': [0.1, 0.5, 0.9]
                },
                'use_scaled': True
            },
            'svr': {
                'model': SVR(),
                'params': {
                    'C': [0.1, 1.0, 10.0],
                    'gamma': ['scale', 'auto'],
                    'kernel': ['rbf', 'poly']
                },
                'use_scaled': True
            },
            'mlp': {
                'model': MLPRegressor(random_state=42, max_iter=500),
                'params': {
                    'hidden_layer_sizes': [(50,), (100,), (50, 25)],
                    'alpha': [0.001, 0.01, 0.1],
                    'learning_rate': ['adaptive']
                },
                'use_scaled': True
            }
        }
        
        results = {}
        
        for name, model_config in models.items():
            print(f"   Training {name}...")
            
            try:
                # Select data based on scaling requirement
                if model_config['use_scaled']:
                    X_train_model = X_train_scaled
                    X_test_model = X_test_scaled
                else:
                    X_train_model = X_train_selected
                    X_test_model = X_test_selected
                
                # Grid search with time series cross-validation
                grid_search = GridSearchCV(
                    model_config['model'],
                    model_config['params'],
                    cv=tscv,
                    scoring='neg_mean_absolute_error',
                    n_jobs=-1,
                    verbose=0
                )
                
                grid_search.fit(X_train_model, y_train)
                best_model = grid_search.best_estimator_
                
                # Predict on test set
                y_pred = best_model.predict(X_test_model)
                y_pred = np.clip(y_pred, 0, 99)
                
                # Calculate metrics
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                
                # Accuracy metrics
                accuracy_5 = np.mean(np.abs(y_test - y_pred) <= 5) * 100
                accuracy_10 = np.mean(np.abs(y_test - y_pred) <= 10) * 100
                accuracy_15 = np.mean(np.abs(y_test - y_pred) <= 15) * 100
                
                # Directional accuracy
                if len(y_test) > 1:
                    y_test_diff = np.diff(y_test)
                    y_pred_diff = np.diff(y_pred)
                    directional_acc = np.mean(np.sign(y_test_diff) == np.sign(y_pred_diff)) * 100
                else:
                    directional_acc = 0
                
                results[name] = {
                    'model': best_model,
                    'best_params': grid_search.best_params_,
                    'mae': mae,
                    'rmse': rmse,
                    'accuracy_5': accuracy_5,
                    'accuracy_10': accuracy_10,
                    'accuracy_15': accuracy_15,
                    'directional_accuracy': directional_acc,
                    'use_scaled': model_config['use_scaled'],
                    'predictions': y_pred.tolist(),
                    'actual': y_test.tolist(),
                    'dates': dates_test.tolist()
                }
                
                print(f"     Best params: {grid_search.best_params_}")
                print(f"     MAE: {mae:.2f}, RMSE: {rmse:.2f}")
                print(f"     Accuracy ±5: {accuracy_5:.1f}%, ±10: {accuracy_10:.1f}%")
                print(f"     Directional: {directional_acc:.1f}%")
                
            except Exception as e:
                print(f"     ❌ Error: {str(e)}")
                continue
        
        if results:
            # Select best model based on accuracy_10
            best_name = max(results.keys(), key=lambda x: results[x]['accuracy_10'])
            best_result = results[best_name]
            
            self.models[game_name] = {
                'best_model': best_result['model'],
                'best_name': best_name,
                'scaler': scaler,
                'selector': selector,
                'use_scaled': best_result['use_scaled'],
                'feature_names': selector.get_feature_names_out()
            }
            
            self.results[game_name] = results
            
            print(f"   ✅ Best model: {best_name} (Accuracy ±10: {best_result['accuracy_10']:.1f}%)")
            return results
        
        return None
    
    def predict_next_value(self, X, game_name):
        """Predict next value using the best trained model"""
        if game_name not in self.models:
            return None
        
        model_info = self.models[game_name]
        model = model_info['best_model']
        scaler = model_info['scaler']
        selector = model_info['selector']
        use_scaled = model_info['use_scaled']
        
        # Prepare features
        last_features = X.iloc[-1:].copy()
        last_features_selected = selector.transform(last_features)
        
        if use_scaled:
            last_features_final = scaler.transform(last_features_selected)
        else:
            last_features_final = last_features_selected
        
        # Make prediction
        prediction = model.predict(last_features_final)[0]
        prediction = np.clip(prediction, 0, 99)
        
        return int(round(prediction))
    
    def save_models(self, game_name):
        """Save trained models"""
        if game_name in self.models:
            os.makedirs(config.MODELS_DIR, exist_ok=True)
            model_file = os.path.join(config.MODELS_DIR, f'{game_name}_advanced.joblib')
            joblib.dump(self.models[game_name], model_file)
            print(f"💾 Saved advanced model for {game_name}")

def main():
    """Main training and prediction pipeline"""
    print("🚀 ADVANCED ACCURATE PREDICTION SYSTEM")
    print("=" * 60)
    print("Using extensive data and advanced algorithms...")
    
    predictor = AdvancedAccuratePredictor()
    
    # Load extensive data
    df = predictor.load_extensive_data()
    if df is None or len(df) < 500:
        print("❌ Insufficient data for advanced training")
        return
    
    print(f"\n🤖 TRAINING ADVANCED MODELS")
    print("-" * 40)
    
    all_predictions = {}
    all_results = {}
    
    for game in config.GAMES:
        if game in df.columns:
            game_data = df[game].dropna()
            if len(game_data) >= 500:  # Need substantial data
                print(f"\n🎯 Training {config.GAME_NAMES.get(game, game)}...")
                
                # Create advanced features
                X, y, dates = predictor.create_advanced_features(df, game)
                
                if X is not None and len(X) >= 500:
                    # Train advanced models
                    results = predictor.train_advanced_models(X, y, dates, game)
                    
                    if results:
                        all_results[game] = results
                        
                        # Make prediction
                        prediction = predictor.predict_next_value(X, game)
                        if prediction is not None:
                            best_accuracy = max(r['accuracy_10'] for r in results.values())
                            all_predictions[game] = {
                                'game_name': config.GAME_NAMES.get(game, game),
                                'prediction': prediction,
                                'accuracy': best_accuracy,
                                'model': predictor.models[game]['best_name']
                            }
                        
                        # Save model
                        predictor.save_models(game)
                else:
                    print(f"   ❌ Insufficient processed data for {game}")
            else:
                print(f"❌ Insufficient raw data for {game}: {len(game_data)} records")
    
    # Display final predictions
    print(f"\n🔮 ADVANCED ML PREDICTIONS")
    print("=" * 50)
    
    if all_predictions:
        for game, pred_info in all_predictions.items():
            accuracy = pred_info['accuracy']
            confidence_emoji = "🟢" if accuracy > 60 else "🟡" if accuracy > 40 else "🔴"
            print(f"{confidence_emoji} {pred_info['game_name']}: {pred_info['prediction']:02d} "
                  f"(Accuracy: {accuracy:.1f}%, Model: {pred_info['model']})")
        
        # Save comprehensive report
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'model_type': 'Advanced ML with Hyperparameter Tuning',
            'data_size': len(df),
            'predictions': all_predictions,
            'detailed_results': all_results,
            'summary': {
                'total_games': len(all_predictions),
                'avg_accuracy': np.mean([p['accuracy'] for p in all_predictions.values()]),
                'features_used': 'Advanced feature engineering with 100+ features',
                'validation': 'Time series cross-validation'
            }
        }
        
        os.makedirs(config.DATA_DIR, exist_ok=True)
        report_file = os.path.join(config.DATA_DIR, f'advanced_ml_predictions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(report_file, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        
        print(f"\n💾 Advanced predictions saved to: {report_file}")
        print(f"\n📊 AVERAGE ACCURACY: {np.mean([p['accuracy'] for p in all_predictions.values()]):.1f}%")
        
    else:
        print("❌ No advanced predictions generated")

if __name__ == "__main__":
    main()
