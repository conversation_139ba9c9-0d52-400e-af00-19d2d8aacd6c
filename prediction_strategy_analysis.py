"""
Analysis of exact prediction strategies based on discovered patterns
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
from collections import Counter
import matplotlib.pyplot as plt

def analyze_prediction_strategies():
    """Analyze specific prediction strategies based on discovered patterns"""
    
    print("🎯 PREDICTION STRATEGY ANALYSIS")
    print("=" * 60)
    print("Based on deep randomness investigation findings...")
    print()
    
    # Load investigation results
    investigation_files = [f for f in os.listdir('data') if f.startswith('deep_randomness_investigation_')]
    if not investigation_files:
        print("❌ No investigation results found")
        return
    
    latest_file = max(investigation_files, key=lambda x: os.path.getmtime(os.path.join('data', x)))
    with open(os.path.join('data', latest_file), 'r') as f:
        investigation_data = json.load(f)
    
    # Load historical data
    data_file = 'data/raw/extensive_historical_data.csv'
    if not os.path.exists(data_file):
        print("❌ No historical data found")
        return
    
    df = pd.read_csv(data_file)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    # Analyze each discovered pattern type
    analyze_mathematical_sequences(investigation_data, df)
    analyze_modular_patterns(investigation_data, df)
    analyze_cross_game_correlations(investigation_data, df)
    analyze_date_patterns(investigation_data, df)
    
    # Generate specific prediction methods
    prediction_methods = generate_prediction_methods(investigation_data, df)
    
    # Test prediction accuracy
    test_prediction_accuracy(prediction_methods, df)
    
    # Generate final strategy
    generate_final_strategy(prediction_methods, investigation_data)

def analyze_mathematical_sequences(investigation_data, df):
    """Analyze mathematical sequences for prediction potential"""
    
    print("🔢 MATHEMATICAL SEQUENCE ANALYSIS")
    print("-" * 50)
    
    hidden_patterns = investigation_data.get('findings', {}).get('hidden_patterns', {})
    
    sequence_analysis = {}
    
    for game, patterns in hidden_patterns.items():
        sequences = patterns.get('math_sequences', [])
        
        if sequences:
            print(f"\n📊 {game} Mathematical Sequences:")
            
            # Analyze arithmetic sequences
            arithmetic_seqs = [s for s in sequences if s['type'] == 'arithmetic']
            geometric_seqs = [s for s in sequences if s['type'] == 'geometric']
            
            print(f"   Arithmetic sequences: {len(arithmetic_seqs)}")
            print(f"   Geometric sequences: {len(geometric_seqs)}")
            
            # Find most common differences/ratios
            if arithmetic_seqs:
                differences = [s['difference'] for s in arithmetic_seqs]
                common_diff = Counter(differences).most_common(3)
                print(f"   Common differences: {common_diff}")
            
            if geometric_seqs:
                ratios = [round(s['ratio'], 2) for s in geometric_seqs]
                common_ratios = Counter(ratios).most_common(3)
                print(f"   Common ratios: {common_ratios}")
            
            sequence_analysis[game] = {
                'arithmetic_count': len(arithmetic_seqs),
                'geometric_count': len(geometric_seqs),
                'total_sequences': len(sequences),
                'prediction_potential': 'LOW' if len(sequences) < 5 else 'MEDIUM'
            }
    
    return sequence_analysis

def analyze_modular_patterns(investigation_data, df):
    """Analyze modular arithmetic patterns"""
    
    print("\n🔄 MODULAR PATTERN ANALYSIS")
    print("-" * 50)
    
    modular_analysis = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            print(f"\n📊 {game} Modular Analysis:")
            
            # Test various moduli for patterns
            significant_moduli = []
            
            for mod in [2, 3, 5, 7, 10, 11, 13]:
                remainders = game_data % mod
                remainder_counts = remainders.value_counts().sort_index()
                
                # Calculate expected vs actual distribution
                expected = len(game_data) / mod
                chi_square = sum((count - expected)**2 / expected for count in remainder_counts)
                
                # Check for significant bias
                if chi_square > mod * 1.5:  # Threshold for significance
                    significant_moduli.append({
                        'modulus': mod,
                        'chi_square': chi_square,
                        'distribution': remainder_counts.to_dict(),
                        'bias_strength': 'HIGH' if chi_square > mod * 3 else 'MEDIUM'
                    })
                    
                    print(f"   Mod {mod}: Chi-square = {chi_square:.2f} (Biased)")
                    print(f"   Distribution: {remainder_counts.to_dict()}")
            
            if not significant_moduli:
                print("   ✅ No significant modular biases detected")
            
            modular_analysis[game] = {
                'significant_moduli': significant_moduli,
                'prediction_potential': 'HIGH' if len(significant_moduli) >= 3 else 'MEDIUM' if significant_moduli else 'LOW'
            }
    
    return modular_analysis

def analyze_cross_game_correlations(investigation_data, df):
    """Analyze cross-game correlations for prediction"""
    
    print("\n🔗 CROSS-GAME CORRELATION ANALYSIS")
    print("-" * 50)
    
    correlation_analysis = {}
    games = ['DSWR', 'FRBD', 'GZBD', 'GALI']
    
    # Calculate correlation matrix
    game_data = {}
    for game in games:
        if game in df.columns:
            game_data[game] = pd.to_numeric(df[game], errors='coerce')
    
    if len(game_data) >= 2:
        correlation_matrix = pd.DataFrame(game_data).corr()
        
        print("📊 Correlation Matrix:")
        print(correlation_matrix.round(3))
        
        # Find strong correlations
        strong_correlations = []
        for i, game1 in enumerate(games):
            for j, game2 in enumerate(games):
                if i < j and game1 in correlation_matrix.columns and game2 in correlation_matrix.columns:
                    corr = correlation_matrix.loc[game1, game2]
                    if abs(corr) > 0.3:  # Significant correlation
                        strong_correlations.append({
                            'game1': game1,
                            'game2': game2,
                            'correlation': corr,
                            'strength': 'STRONG' if abs(corr) > 0.5 else 'MEDIUM'
                        })
        
        if strong_correlations:
            print(f"\n🔗 Strong Correlations Found:")
            for corr in strong_correlations:
                print(f"   {corr['game1']} ↔ {corr['game2']}: {corr['correlation']:.3f} ({corr['strength']})")
        else:
            print("\n✅ No strong cross-game correlations detected")
        
        # Test lead-lag relationships
        lead_lag_analysis = test_lead_lag_relationships(game_data)
        
        correlation_analysis = {
            'correlation_matrix': correlation_matrix.to_dict(),
            'strong_correlations': strong_correlations,
            'lead_lag_relationships': lead_lag_analysis,
            'prediction_potential': 'HIGH' if strong_correlations else 'LOW'
        }
    
    return correlation_analysis

def test_lead_lag_relationships(game_data):
    """Test for lead-lag relationships between games"""
    
    print(f"\n🕐 Lead-Lag Relationship Analysis:")
    
    lead_lag_relationships = []
    games = list(game_data.keys())
    
    for i, leader in enumerate(games):
        for j, follower in enumerate(games):
            if i != j:
                leader_data = game_data[leader].dropna()
                follower_data = game_data[follower].dropna()
                
                # Test different lag periods
                for lag in [1, 2, 3, 7]:
                    if len(leader_data) > lag and len(follower_data) > lag:
                        # Align data with lag
                        min_len = min(len(leader_data) - lag, len(follower_data))
                        
                        if min_len > 10:
                            leader_lagged = leader_data.iloc[:-lag].values
                            follower_current = follower_data.iloc[lag:lag+len(leader_lagged)].values
                            
                            if len(leader_lagged) == len(follower_current):
                                corr = np.corrcoef(leader_lagged, follower_current)[0, 1]
                                
                                if abs(corr) > 0.4:  # Significant lead-lag relationship
                                    lead_lag_relationships.append({
                                        'leader': leader,
                                        'follower': follower,
                                        'lag': lag,
                                        'correlation': corr,
                                        'strength': 'STRONG' if abs(corr) > 0.6 else 'MEDIUM'
                                    })
    
    if lead_lag_relationships:
        for rel in lead_lag_relationships:
            print(f"   {rel['leader']} leads {rel['follower']} by {rel['lag']} days: {rel['correlation']:.3f}")
    else:
        print("   ✅ No significant lead-lag relationships detected")
    
    return lead_lag_relationships

def analyze_date_patterns(investigation_data, df):
    """Analyze date-based patterns"""
    
    print("\n📅 DATE PATTERN ANALYSIS")
    print("-" * 50)
    
    date_analysis = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_df = df[['date', game]].dropna()
            game_df['day_of_week'] = game_df['date'].dt.dayofweek
            game_df['day_of_month'] = game_df['date'].dt.day
            game_df['month'] = game_df['date'].dt.month
            
            print(f"\n📊 {game} Date Patterns:")
            
            # Day of week analysis
            dow_means = game_df.groupby('day_of_week')[game].mean()
            overall_mean = game_df[game].mean()
            
            significant_days = []
            for dow, mean_val in dow_means.items():
                deviation = abs(mean_val - overall_mean)
                if deviation > 3:  # Significant deviation
                    day_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    significant_days.append({
                        'day': day_names[dow],
                        'mean': mean_val,
                        'deviation': mean_val - overall_mean
                    })
            
            if significant_days:
                print(f"   Significant day-of-week patterns:")
                for day_info in significant_days:
                    print(f"   {day_info['day']}: {day_info['mean']:.1f} (deviation: {day_info['deviation']:+.1f})")
            else:
                print("   ✅ No significant day-of-week patterns")
            
            # Month analysis
            month_means = game_df.groupby('month')[game].mean()
            significant_months = []
            
            for month, mean_val in month_means.items():
                deviation = abs(mean_val - overall_mean)
                if deviation > 3:
                    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                    significant_months.append({
                        'month': month_names[month-1],
                        'mean': mean_val,
                        'deviation': mean_val - overall_mean
                    })
            
            if significant_months:
                print(f"   Significant monthly patterns:")
                for month_info in significant_months:
                    print(f"   {month_info['month']}: {month_info['mean']:.1f} (deviation: {month_info['deviation']:+.1f})")
            else:
                print("   ✅ No significant monthly patterns")
            
            date_analysis[game] = {
                'day_of_week_patterns': significant_days,
                'monthly_patterns': significant_months,
                'prediction_potential': 'MEDIUM' if (significant_days or significant_months) else 'LOW'
            }
    
    return date_analysis

def generate_prediction_methods(investigation_data, df):
    """Generate specific prediction methods based on discovered patterns"""
    
    print("\n🎯 GENERATING PREDICTION METHODS")
    print("-" * 50)
    
    prediction_methods = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            methods = {}
            
            # Method 1: Last value + trend
            if len(game_data) >= 10:
                recent_trend = np.mean(np.diff(game_data.tail(5)))
                last_value = game_data.iloc[-1]
                trend_prediction = int(np.clip(last_value + recent_trend, 0, 99))
                methods['trend_based'] = trend_prediction
            
            # Method 2: Modular pattern prediction
            # Find the most biased modulus
            best_mod = None
            best_bias = 0
            
            for mod in [3, 5, 7, 11]:
                remainders = game_data % mod
                remainder_counts = remainders.value_counts()
                
                # Find most frequent remainder
                if len(remainder_counts) > 0:
                    most_frequent_remainder = remainder_counts.index[0]
                    frequency = remainder_counts.iloc[0] / len(game_data)
                    
                    if frequency > best_bias:
                        best_bias = frequency
                        best_mod = mod
                        best_remainder = most_frequent_remainder
            
            if best_mod and best_bias > 0.15:  # Significant bias
                # Predict next number with this remainder
                last_value = game_data.iloc[-1]
                target_remainder = best_remainder
                
                # Find closest number to last_value with target remainder
                candidates = [i for i in range(100) if i % best_mod == target_remainder]
                modular_prediction = min(candidates, key=lambda x: abs(x - last_value))
                methods['modular_based'] = modular_prediction
            
            # Method 3: Frequency-based prediction
            # Predict least frequent numbers (due for appearance)
            value_counts = game_data.value_counts()
            all_numbers = set(range(100))
            missing_numbers = all_numbers - set(value_counts.index)
            
            if missing_numbers:
                # Choose missing number closest to recent average
                recent_avg = game_data.tail(10).mean()
                frequency_prediction = min(missing_numbers, key=lambda x: abs(x - recent_avg))
            else:
                # Choose least frequent number
                frequency_prediction = value_counts.index[-1]
            
            methods['frequency_based'] = frequency_prediction
            
            # Method 4: Range-based prediction
            # Avoid recently used ranges
            recent_values = set(game_data.tail(5))
            
            # Divide into ranges
            ranges = [(0, 33), (34, 66), (67, 99)]
            recent_ranges = []
            
            for val in recent_values:
                for i, (start, end) in enumerate(ranges):
                    if start <= val <= end:
                        recent_ranges.append(i)
                        break
            
            # Choose least used range
            range_counts = Counter(recent_ranges)
            if range_counts:
                least_used_range = min(range(3), key=lambda x: range_counts.get(x, 0))
                range_start, range_end = ranges[least_used_range]
                
                # Choose middle of range
                range_prediction = (range_start + range_end) // 2
            else:
                range_prediction = 50  # Default middle
            
            methods['range_based'] = range_prediction
            
            # Method 5: Ensemble prediction
            method_values = list(methods.values())
            if method_values:
                ensemble_prediction = int(np.median(method_values))
                methods['ensemble'] = ensemble_prediction
            
            prediction_methods[game] = methods
            
            print(f"\n🎲 {game} Prediction Methods:")
            for method, prediction in methods.items():
                print(f"   {method}: {prediction:02d}")
    
    return prediction_methods

def test_prediction_accuracy(prediction_methods, df):
    """Test prediction accuracy using historical data"""
    
    print("\n📊 TESTING PREDICTION ACCURACY")
    print("-" * 50)
    
    # Use last 30 days for testing
    test_data = df.tail(30).copy()
    
    accuracy_results = {}
    
    for game in prediction_methods.keys():
        if game in test_data.columns:
            game_test_data = pd.to_numeric(test_data[game], errors='coerce').dropna()
            
            if len(game_test_data) >= 10:
                # Test each method
                method_accuracies = {}
                
                for method_name in prediction_methods[game].keys():
                    correct_predictions = 0
                    total_predictions = 0
                    
                    # Simulate predictions for each day in test period
                    for i in range(5, len(game_test_data)):  # Start from day 5 to have history
                        # Use data up to day i-1 to predict day i
                        historical_data = game_test_data.iloc[:i]
                        actual_value = game_test_data.iloc[i]
                        
                        # Generate prediction using the method (simplified)
                        if method_name == 'trend_based':
                            if len(historical_data) >= 5:
                                trend = np.mean(np.diff(historical_data.tail(3)))
                                prediction = int(np.clip(historical_data.iloc[-1] + trend, 0, 99))
                            else:
                                prediction = int(historical_data.mean())
                        
                        elif method_name == 'frequency_based':
                            value_counts = historical_data.value_counts()
                            if len(value_counts) > 0:
                                prediction = value_counts.index[-1]  # Least frequent
                            else:
                                prediction = 50
                        
                        else:
                            # Use current method prediction as approximation
                            prediction = prediction_methods[game][method_name]
                        
                        # Check accuracy (within ±5)
                        if abs(prediction - actual_value) <= 5:
                            correct_predictions += 1
                        
                        total_predictions += 1
                    
                    if total_predictions > 0:
                        accuracy = (correct_predictions / total_predictions) * 100
                        method_accuracies[method_name] = accuracy
                
                accuracy_results[game] = method_accuracies
                
                print(f"\n📈 {game} Method Accuracies (±5 range):")
                for method, accuracy in method_accuracies.items():
                    print(f"   {method}: {accuracy:.1f}%")
    
    return accuracy_results

def generate_final_strategy(prediction_methods, investigation_data):
    """Generate final prediction strategy"""
    
    print("\n🎯 FINAL PREDICTION STRATEGY")
    print("=" * 50)
    
    # Analyze overall findings
    hidden_patterns = investigation_data.get('findings', {}).get('hidden_patterns', {})
    
    total_predictable_score = 0
    game_count = 0
    
    for game, patterns in hidden_patterns.items():
        total_predictable_score += patterns.get('predictable_score', 0)
        game_count += 1
    
    if game_count > 0:
        avg_predictable_score = total_predictable_score / game_count
    else:
        avg_predictable_score = 0
    
    print(f"📊 OVERALL ANALYSIS:")
    print(f"   Average predictable score: {avg_predictable_score:.1f}%")
    print(f"   Mathematical sequences found: {sum(len(p.get('math_sequences', [])) for p in hidden_patterns.values())}")
    
    if avg_predictable_score > 50:
        strategy_level = "AGGRESSIVE"
        confidence = "HIGH"
    elif avg_predictable_score > 30:
        strategy_level = "MODERATE"
        confidence = "MEDIUM"
    else:
        strategy_level = "CONSERVATIVE"
        confidence = "LOW"
    
    print(f"\n🎯 RECOMMENDED STRATEGY: {strategy_level}")
    print(f"🔍 CONFIDENCE LEVEL: {confidence}")
    
    print(f"\n💡 SPECIFIC RECOMMENDATIONS:")
    
    if strategy_level == "AGGRESSIVE":
        print("   1. 🎯 Focus on games with highest predictable scores")
        print("   2. 📊 Use ensemble predictions combining multiple methods")
        print("   3. 🔄 Monitor modular patterns for bias exploitation")
        print("   4. 📅 Consider date-based patterns for timing")
        print("   5. ⚠️ Start with small stakes to validate patterns")
    
    elif strategy_level == "MODERATE":
        print("   1. 🎲 Use trend-based predictions as primary method")
        print("   2. 📊 Combine with frequency analysis")
        print("   3. 🔍 Monitor for pattern changes over time")
        print("   4. ⚠️ Use conservative position sizing")
        print("   5. 📈 Track prediction accuracy continuously")
    
    else:
        print("   1. ❌ No reliable prediction patterns detected")
        print("   2. 🎲 Data appears genuinely random")
        print("   3. 💰 Focus on risk management over prediction")
        print("   4. ⚠️ Avoid gambling-based strategies")
        print("   5. 🔍 Continue monitoring for pattern emergence")
    
    # Generate next predictions
    print(f"\n🔮 NEXT DRAW PREDICTIONS:")
    print("-" * 30)
    
    for game, methods in prediction_methods.items():
        if 'ensemble' in methods:
            prediction = methods['ensemble']
            game_patterns = hidden_patterns.get(game, {})
            predictable_score = game_patterns.get('predictable_score', 0)
            
            confidence_emoji = "🟢" if predictable_score > 50 else "🟡" if predictable_score > 30 else "🔴"
            
            print(f"{confidence_emoji} {game}: {prediction:02d} (Predictability: {predictable_score:.1f}%)")
    
    print(f"\n⚠️ IMPORTANT DISCLAIMERS:")
    print("• These predictions are based on statistical analysis")
    print("• Past patterns do not guarantee future results")
    print("• Gambling involves significant financial risk")
    print("• Use only for educational/research purposes")
    print("• Ensure compliance with local gambling laws")

if __name__ == "__main__":
    analyze_prediction_strategies()
