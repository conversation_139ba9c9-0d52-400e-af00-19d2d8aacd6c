"""
Working demo with cleaned data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import config

def clean_scraped_data(df):
    """Clean the scraped data"""
    print("Cleaning scraped data...")
    
    # Convert numeric columns
    for game in config.GAMES:
        if game in df.columns:
            # Convert to string first, then to numeric
            df[game] = df[game].astype(str)
            df[game] = df[game].replace('', np.nan)
            df[game] = pd.to_numeric(df[game], errors='coerce')
    
    # Remove rows where all game values are NaN
    game_cols = [col for col in config.GAMES if col in df.columns]
    df = df.dropna(subset=game_cols, how='all')
    
    # Remove duplicate dates
    df = df.drop_duplicates(subset=['date'], keep='first')
    
    # Sort by date
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    print(f"Cleaned data: {len(df)} records")
    return df

def demo_with_real_data():
    """Demo using real scraped data"""
    print("SATTA KING PREDICTION BOT - WORKING DEMO")
    print("="*60)
    
    # Load the scraped data
    data_file = os.path.join(config.RAW_DATA_DIR, 'demo_scraped_data.csv')
    
    if os.path.exists(data_file):
        print("Loading scraped data...")
        df = pd.read_csv(data_file)
        
        # Clean the data
        df_clean = clean_scraped_data(df)
        
        print(f"Data loaded: {len(df_clean)} records")
        print(f"Date range: {df_clean['date'].min()} to {df_clean['date'].max()}")
        
        # Analysis
        print("\nDATA ANALYSIS")
        print("-" * 30)
        
        for game in config.GAMES:
            if game in df_clean.columns:
                game_data = df_clean[game].dropna()
                if len(game_data) > 0:
                    game_name = config.GAME_NAMES.get(game, game)
                    print(f"\n{game_name}:")
                    print(f"  Records: {len(game_data)}")
                    print(f"  Range: {int(game_data.min())} - {int(game_data.max())}")
                    print(f"  Average: {game_data.mean():.1f}")
                    print(f"  Last 5 values: {game_data.tail(5).tolist()}")
                    
                    # Most frequent
                    freq = game_data.value_counts().head(3)
                    print(f"  Most frequent: {list(freq.index)}")
        
        # Simple predictions
        print("\nSIMPLE PREDICTIONS")
        print("-" * 30)
        
        predictions = {}
        
        for game in config.GAMES:
            if game in df_clean.columns:
                game_data = df_clean[game].dropna()
                if len(game_data) >= 5:
                    game_name = config.GAME_NAMES.get(game, game)
                    
                    # Get recent values
                    recent = game_data.tail(10).values
                    
                    # Simple prediction methods
                    avg_pred = int(np.mean(recent))
                    median_pred = int(np.median(recent))
                    last_pred = int(recent[-1])
                    
                    # Trend prediction
                    if len(recent) >= 3:
                        trend = np.mean(np.diff(recent[-3:]))
                        trend_pred = int(last_pred + trend)
                        trend_pred = max(0, min(99, trend_pred))
                    else:
                        trend_pred = avg_pred
                    
                    # Ensemble
                    ensemble_pred = int(np.mean([avg_pred, median_pred, trend_pred]))
                    ensemble_pred = max(0, min(99, ensemble_pred))
                    
                    predictions[game] = {
                        'name': game_name,
                        'prediction': ensemble_pred,
                        'methods': {
                            'average': avg_pred,
                            'median': median_pred,
                            'trend': trend_pred,
                            'last': last_pred
                        },
                        'recent_values': recent.tolist()
                    }
                    
                    print(f"\n{game_name}:")
                    print(f"  Prediction: {ensemble_pred:02d}")
                    print(f"  Based on: Avg={avg_pred}, Median={median_pred}, Trend={trend_pred}")
                    print(f"  Recent: {recent[-5:].tolist()}")
        
        # Generate report
        print("\nGENERATING REPORT")
        print("-" * 30)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "data_summary": {
                "total_records": len(df_clean),
                "date_range": {
                    "start": df_clean['date'].min().isoformat(),
                    "end": df_clean['date'].max().isoformat()
                },
                "games_analyzed": len(predictions)
            },
            "predictions": {}
        }
        
        for game, pred_info in predictions.items():
            report["predictions"][game] = {
                "game_name": pred_info['name'],
                "prediction": pred_info['prediction'],
                "confidence": "Medium",
                "recent_values": pred_info['recent_values'][-10:]
            }
        
        # Save report
        os.makedirs(config.DATA_DIR, exist_ok=True)
        report_file = os.path.join(config.DATA_DIR, 'working_demo_report.json')
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"✓ Report saved to: {report_file}")
        
        # Summary
        print("\nSUMMARY")
        print("="*30)
        print(f"✓ Scraped and analyzed {len(df_clean)} records")
        print(f"✓ Generated predictions for {len(predictions)} games")
        print(f"✓ Date range: {df_clean['date'].min().strftime('%Y-%m-%d')} to {df_clean['date'].max().strftime('%Y-%m-%d')}")
        
        print("\nPREDICTIONS SUMMARY:")
        for game, pred_info in predictions.items():
            print(f"  {pred_info['name']}: {pred_info['prediction']:02d}")
        
        return True
        
    else:
        print(f"No data file found at {data_file}")
        print("Run the scraper first: python test_scraper.py")
        return False

def create_sample_predictions():
    """Create sample predictions for demo"""
    print("\nCREATING SAMPLE PREDICTIONS")
    print("="*40)
    
    # Sample recent data for each game
    sample_data = {
        'DSWR': [24, 59, 96, 32, 79, 48, 19, 93, 9, 26],
        'FRBD': [83, 15, 27, 2, 28, 1, 71, 94, 3, 79],
        'GZBD': [7, 60, 17, 4, 9, 84, 74, 43, 53, 18],
        'GALI': [74, 92, 6, 48, 50, 30, 3, 7, 19, 85]
    }
    
    predictions = {}
    
    for game, values in sample_data.items():
        game_name = config.GAME_NAMES.get(game, game)
        
        # Simple prediction methods
        avg_pred = int(np.mean(values))
        median_pred = int(np.median(values))
        trend = np.mean(np.diff(values[-3:]))
        trend_pred = int(values[-1] + trend)
        trend_pred = max(0, min(99, trend_pred))
        
        # Most frequent
        freq_pred = int(pd.Series(values).mode().iloc[0])
        
        # Ensemble
        ensemble_pred = int(np.mean([avg_pred, median_pred, trend_pred, freq_pred]))
        ensemble_pred = max(0, min(99, ensemble_pred))
        
        predictions[game] = {
            'name': game_name,
            'prediction': ensemble_pred,
            'confidence': np.random.uniform(0.6, 0.8),  # Random confidence for demo
            'recent_values': values
        }
        
        print(f"{game_name}: {ensemble_pred:02d} (Recent: {values[-5:]})")
    
    # Create report
    report = {
        "timestamp": datetime.now().isoformat(),
        "predictions": {},
        "summary": {
            "total_predictions": len(predictions),
            "average_confidence": np.mean([p['confidence'] for p in predictions.values()]),
            "method": "Simple Statistical Ensemble"
        }
    }
    
    for game, pred_info in predictions.items():
        report["predictions"][game] = {
            "game_name": pred_info['name'],
            "prediction": pred_info['prediction'],
            "confidence": f"{pred_info['confidence']:.1%}",
            "recent_values": pred_info['recent_values']
        }
    
    # Save sample report
    report_file = os.path.join(config.DATA_DIR, 'sample_predictions.json')
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n✓ Sample predictions saved to: {report_file}")
    return predictions

def main():
    """Main demo function"""
    print("SATTA KING PREDICTION BOT")
    print("="*50)
    print("Demonstrating core functionality...")
    
    # Try to use real scraped data first
    success = demo_with_real_data()
    
    if not success:
        # Fall back to sample predictions
        print("\nFalling back to sample predictions...")
        create_sample_predictions()
    
    print("\n" + "="*50)
    print("DEMO COMPLETED!")
    print("="*50)
    print("\nFeatures demonstrated:")
    print("✓ Web scraping from Satta King website")
    print("✓ Data cleaning and processing")
    print("✓ Statistical analysis")
    print("✓ Simple prediction algorithms")
    print("✓ Report generation")
    
    print("\nNext steps:")
    print("1. Install TensorFlow for advanced ML: pip install tensorflow")
    print("2. Run full pipeline: python main.py full")
    print("3. Try web interface: streamlit run web_app.py")
    print("4. Generate predictions: python main.py predict")

if __name__ == "__main__":
    main()
