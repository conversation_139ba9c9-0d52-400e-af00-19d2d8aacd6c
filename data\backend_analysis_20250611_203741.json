{"timestamp": "2025-06-11T20:36:37.306636", "server_analysis": {}, "database_evidence": {}, "admin_systems": {}, "manipulation_evidence": {}, "data_flow_analysis": {}, "conclusions": {}, "https://satta-king-fast.com": {"server_technology": {"server_software": "Cloudflare", "programming_language": "PHP", "framework": "unknown", "database_type": "unknown", "hosting_provider": "unknown", "cdn_usage": true, "evidence": ["PHP detected in content/headers", "CDN detected: cf-ray"]}, "database_hints": [{"type": "Database table reference", "evidence": "Found \"table\" in response to ?id='", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1'", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1 AND 1=1", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1 UNION SELECT 1", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?game=test'", "confidence": "medium"}], "admin_interfaces": [], "update_mechanisms": {"method": "unknown", "frequency": "unknown", "automation": false, "manual_control": false, "evidence": ["No-cache headers suggest real-time updates"]}, "security_analysis": {"https_enabled": true, "security_headers": [], "vulnerabilities": ["Potential XSS vulnerability"], "protection_level": "low"}, "manipulation_indicators": [{"type": "Admin controls detected", "evidence": "Admin-related keywords found in page content", "risk_level": "medium"}]}, "https://sattaking.com": {"server_technology": {"server_software": "Cloudflare", "programming_language": "unknown", "framework": "unknown", "database_type": "unknown", "hosting_provider": "unknown", "cdn_usage": true, "evidence": ["CDN detected: cf-ray"]}, "database_hints": [{"type": "Database query error", "evidence": "Found \"query\" in response to ?id='", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1'", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1 AND 1=1", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1 UNION SELECT 1", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?game=test'", "confidence": "medium"}], "admin_interfaces": [{"path": "/admin.php", "url": "https://sattaking.com/admin.php", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/administrator", "url": "https://sattaking.com/administrator", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/manage", "url": "https://sattaking.com/manage", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/control", "url": "https://sattaking.com/control", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/panel", "url": "https://sattaking.com/panel", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/dashboard", "url": "https://sattaking.com/dashboard", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/wp-admin", "url": "https://sattaking.com/wp-admin", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/login", "url": "https://sattaking.com/login", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/login.php", "url": "https://sattaking.com/login.php", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/signin", "url": "https://sattaking.com/signin", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/backend", "url": "https://sattaking.com/backend", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/cms", "url": "https://sattaking.com/cms", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/control-panel", "url": "https://sattaking.com/control-panel", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/satta-admin", "url": "https://sattaking.com/satta-admin", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/result-admin", "url": "https://sattaking.com/result-admin", "status": 200, "accessible": true, "protected": false, "redirect": false}, {"path": "/game-admin", "url": "https://sattaking.com/game-admin", "status": 200, "accessible": true, "protected": false, "redirect": false}], "update_mechanisms": {"method": "unknown", "frequency": "unknown", "automation": false, "manual_control": true, "evidence": ["Manual control keywords in JavaScript", "No-cache headers suggest real-time updates"]}, "security_analysis": {"https_enabled": true, "security_headers": [], "vulnerabilities": ["Potential XSS vulnerability", "Potential SQL injection vulnerability"], "protection_level": "low"}, "manipulation_indicators": [{"type": "Admin controls detected", "evidence": "Admin-related keywords found in page content", "risk_level": "medium"}]}, "https://sattakinggali.com": {"server_technology": {"server_software": "Cloudflare", "programming_language": "unknown", "framework": "unknown", "database_type": "unknown", "hosting_provider": "unknown", "cdn_usage": true, "evidence": ["CDN detected: cf-ray"]}, "database_hints": [{"type": "Database table reference", "evidence": "Found \"table\" in response to ?id='", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id='", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1'", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1'", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1 AND 1=1", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1 AND 1=1", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?id=1 UNION SELECT 1", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?id=1 UNION SELECT 1", "confidence": "medium"}, {"type": "Database table reference", "evidence": "Found \"table\" in response to ?game=test'", "confidence": "medium"}, {"type": "Database query error", "evidence": "Found \"query\" in response to ?game=test'", "confidence": "medium"}], "admin_interfaces": [{"path": "/admin", "url": "https://sattakinggali.com/admin", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/admin.php", "url": "https://sattakinggali.com/admin.php", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/admin/", "url": "https://sattakinggali.com/admin/", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/administrator", "url": "https://sattakinggali.com/administrator", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/manage", "url": "https://sattakinggali.com/manage", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/control", "url": "https://sattakinggali.com/control", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/panel", "url": "https://sattakinggali.com/panel", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/dashboard", "url": "https://sattakinggali.com/dashboard", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/wp-admin", "url": "https://sattakinggali.com/wp-admin", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/login", "url": "https://sattakinggali.com/login", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/login.php", "url": "https://sattakinggali.com/login.php", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/signin", "url": "https://sattakinggali.com/signin", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/backend", "url": "https://sattakinggali.com/backend", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/cms", "url": "https://sattakinggali.com/cms", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/control-panel", "url": "https://sattakinggali.com/control-panel", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/satta-admin", "url": "https://sattakinggali.com/satta-admin", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/result-admin", "url": "https://sattakinggali.com/result-admin", "status": 301, "accessible": false, "protected": false, "redirect": true}, {"path": "/game-admin", "url": "https://sattakinggali.com/game-admin", "status": 301, "accessible": false, "protected": false, "redirect": true}], "update_mechanisms": {"method": "unknown", "frequency": "unknown", "automation": false, "manual_control": false, "evidence": ["No-cache headers suggest real-time updates"]}, "security_analysis": {"https_enabled": true, "security_headers": [], "vulnerabilities": ["Potential XSS vulnerability"], "protection_level": "low"}, "manipulation_indicators": []}, "cross_site_analysis": {"shared_technologies": ["unknown"], "common_vulnerabilities": ["Potential XSS vulnerability"], "similar_admin_systems": ["/admin.php", "/administrator", "/manage", "/control", "/panel", "/dashboard", "/wp-admin", "/login", "/login.php", "/signin", "/backend", "/cms", "/control-panel", "/satta-admin", "/result-admin", "/game-admin"], "centralized_backend": true, "evidence": ["Similar technologies and admin systems suggest centralized backend"]}, "manipulation_analysis": {"manipulation_possible": false, "manipulation_methods": [], "risk_level": "low", "evidence": [], "protection_measures": [], "recommendations": []}}