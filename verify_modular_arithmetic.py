"""
Verify modular arithmetic claims with rigorous statistical testing
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from collections import Counter
import os
from datetime import datetime

def verify_modular_arithmetic():
    """Rigorously verify if modular arithmetic is actually being used"""

    print("🔬 RIGOROUS VERIFICATION: MODULAR ARITHMETIC CLAIMS")
    print("=" * 70)

    # Load data
    data_file = 'data/raw/extensive_historical_data.csv'
    if not os.path.exists(data_file):
        print("❌ No data file found")
        return False

    df = pd.read_csv(data_file)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    print(f"✅ Loaded {len(df)} records for verification")

    verification_results = {}

    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()

            print(f"\n🔍 VERIFYING {game}")
            print("-" * 40)

            # Test 1: Chi-square test for modular uniformity
            modular_results = test_modular_uniformity(game_data, game)

            # Test 2: Kolmogorov-Smirnov test for true randomness
            ks_results = test_true_randomness(game_data, game)

            # Test 3: Autocorrelation analysis
            autocorr_results = test_autocorrelation(game_data, game)

            # Test 4: Runs test
            runs_results = test_runs(game_data, game)

            # Test 5: Frequency analysis
            freq_results = test_frequency_distribution(game_data, game)

            verification_results[game] = {
                'modular_tests': modular_results,
                'randomness_tests': ks_results,
                'autocorrelation': autocorr_results,
                'runs_test': runs_results,
                'frequency_test': freq_results,
                'is_modular_arithmetic': determine_if_modular(modular_results, ks_results, autocorr_results)
            }

    # Generate final verification report
    generate_verification_report(verification_results)

    return verification_results

def test_modular_uniformity(data, game_name):
    """Test if data shows modular arithmetic patterns"""

    print(f"   🔢 Testing modular uniformity...")

    modular_results = {}

    for modulus in [2, 3, 5, 7, 11, 13, 17, 19]:
        remainders = data % modulus
        observed_freq = remainders.value_counts().sort_index()

        # Expected frequency for uniform distribution
        expected_freq = len(data) / modulus

        # Chi-square test
        chi2_stat = sum((observed_freq - expected_freq) ** 2 / expected_freq)

        # Critical value for significance (alpha = 0.05)
        critical_value = stats.chi2.ppf(0.95, modulus - 1)

        # P-value
        p_value = 1 - stats.chi2.cdf(chi2_stat, modulus - 1)

        is_uniform = chi2_stat < critical_value

        modular_results[modulus] = {
            'chi2_statistic': chi2_stat,
            'critical_value': critical_value,
            'p_value': p_value,
            'is_uniform': is_uniform,
            'observed_frequencies': observed_freq.to_dict(),
            'expected_frequency': expected_freq
        }

        if not is_uniform:
            print(f"      Mod {modulus}: NON-UNIFORM (χ²={chi2_stat:.2f}, p={p_value:.4f})")
        else:
            print(f"      Mod {modulus}: Uniform (χ²={chi2_stat:.2f}, p={p_value:.4f})")

    return modular_results

def test_true_randomness(data, game_name):
    """Test if data is truly random using Kolmogorov-Smirnov test"""

    print(f"   📊 Testing true randomness...")

    # Test against uniform distribution (0-99)
    uniform_data = np.random.uniform(0, 100, len(data))

    # Kolmogorov-Smirnov test
    ks_statistic, ks_p_value = stats.ks_2samp(data, uniform_data)

    # Anderson-Darling test for normality
    ad_statistic, ad_critical_values, ad_significance_levels = stats.anderson(data, dist='norm')

    # Test against theoretical uniform distribution
    theoretical_uniform = np.linspace(0, 99, len(data))
    ks_theoretical, ks_theoretical_p = stats.ks_2samp(data, theoretical_uniform)

    results = {
        'ks_vs_random': {
            'statistic': ks_statistic,
            'p_value': ks_p_value,
            'is_random': ks_p_value > 0.05
        },
        'ks_vs_theoretical': {
            'statistic': ks_theoretical,
            'p_value': ks_theoretical_p,
            'is_uniform': ks_theoretical_p > 0.05
        },
        'anderson_darling': {
            'statistic': ad_statistic,
            'critical_values': ad_critical_values.tolist(),
            'significance_levels': ad_significance_levels.tolist()
        }
    }

    print(f"      KS vs random: p={ks_p_value:.4f} ({'Random' if ks_p_value > 0.05 else 'Non-random'})")
    print(f"      KS vs uniform: p={ks_theoretical_p:.4f} ({'Uniform' if ks_theoretical_p > 0.05 else 'Non-uniform'})")

    return results

def test_autocorrelation(data, game_name):
    """Test for autocorrelation patterns"""

    print(f"   🔄 Testing autocorrelation...")

    autocorr_results = {}

    for lag in [1, 2, 3, 7, 14, 30]:
        if len(data) > lag:
            correlation = np.corrcoef(data[:-lag], data[lag:])[0, 1]

            # Test significance
            n = len(data) - lag
            t_stat = correlation * np.sqrt((n - 2) / (1 - correlation**2))
            p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n - 2))

            is_significant = p_value < 0.05

            autocorr_results[lag] = {
                'correlation': correlation,
                'p_value': p_value,
                'is_significant': is_significant
            }

            if is_significant:
                print(f"      Lag {lag}: r={correlation:.4f} (SIGNIFICANT, p={p_value:.4f})")
            else:
                print(f"      Lag {lag}: r={correlation:.4f} (Not significant)")

    return autocorr_results

def test_runs(data, game_name):
    """Perform runs test for randomness"""

    print(f"   🏃 Testing runs...")

    # Convert to binary based on median
    median = np.median(data)
    binary_sequence = (data > median).astype(int)

    # Count runs
    runs = 1
    n1 = sum(binary_sequence)  # Number of 1s
    n2 = len(binary_sequence) - n1  # Number of 0s

    for i in range(1, len(binary_sequence)):
        if binary_sequence.iloc[i] != binary_sequence.iloc[i-1]:
            runs += 1

    # Expected runs and variance
    expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
    variance_runs = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2)**2 * (n1 + n2 - 1))

    # Z-score
    if variance_runs > 0:
        z_score = (runs - expected_runs) / np.sqrt(variance_runs)
        p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
    else:
        z_score = 0
        p_value = 1

    is_random = abs(z_score) < 1.96  # 95% confidence

    results = {
        'runs_observed': runs,
        'runs_expected': expected_runs,
        'z_score': z_score,
        'p_value': p_value,
        'is_random': is_random
    }

    print(f"      Runs: {runs} (expected: {expected_runs:.1f}, z={z_score:.2f}, p={p_value:.4f})")
    print(f"      Verdict: {'Random' if is_random else 'Non-random'}")

    return results

def test_frequency_distribution(data, game_name):
    """Test frequency distribution patterns"""

    print(f"   📈 Testing frequency distribution...")

    # Count frequencies
    value_counts = data.value_counts().sort_index()

    # Expected frequency for uniform distribution
    expected_freq = len(data) / 100

    # Chi-square test for uniformity across all values
    observed_frequencies = np.zeros(100)
    for value, count in value_counts.items():
        if 0 <= value < 100:
            observed_frequencies[int(value)] = count

    chi2_stat = sum((observed_frequencies - expected_freq) ** 2 / expected_freq)
    p_value = 1 - stats.chi2.cdf(chi2_stat, 99)

    is_uniform = p_value > 0.05

    # Find most and least frequent values
    most_frequent = value_counts.index[0] if len(value_counts) > 0 else None
    least_frequent = value_counts.index[-1] if len(value_counts) > 0 else None

    results = {
        'chi2_statistic': chi2_stat,
        'p_value': p_value,
        'is_uniform': is_uniform,
        'most_frequent_value': most_frequent,
        'most_frequent_count': value_counts.iloc[0] if len(value_counts) > 0 else 0,
        'least_frequent_value': least_frequent,
        'least_frequent_count': value_counts.iloc[-1] if len(value_counts) > 0 else 0,
        'expected_frequency': expected_freq
    }

    print(f"      Frequency uniformity: χ²={chi2_stat:.2f}, p={p_value:.4f}")
    print(f"      Most frequent: {most_frequent} ({value_counts.iloc[0]} times)")
    print(f"      Least frequent: {least_frequent} ({value_counts.iloc[-1]} times)")

    return results

def determine_if_modular(modular_results, randomness_results, autocorr_results):
    """Determine if the data shows modular arithmetic patterns"""

    # Count non-uniform modular distributions
    non_uniform_moduli = sum(1 for result in modular_results.values() if not result['is_uniform'])

    # Check for significant autocorrelations
    significant_autocorrs = sum(1 for result in autocorr_results.values() if result['is_significant'])

    # Check randomness test results
    appears_random = randomness_results['ks_vs_random']['is_random']
    appears_uniform = randomness_results['ks_vs_theoretical']['is_uniform']

    # Decision logic
    if non_uniform_moduli >= 3 and significant_autocorrs >= 1:
        return "LIKELY_MODULAR_ARITHMETIC"
    elif non_uniform_moduli >= 2 or significant_autocorrs >= 2:
        return "POSSIBLE_MODULAR_ARITHMETIC"
    elif not appears_random or not appears_uniform:
        return "NON_RANDOM_BUT_NOT_MODULAR"
    else:
        return "APPEARS_TRULY_RANDOM"

def generate_verification_report(verification_results):
    """Generate comprehensive verification report"""

    print(f"\n📊 VERIFICATION REPORT")
    print("=" * 50)

    for game, results in verification_results.items():
        print(f"\n🎲 {game} VERIFICATION:")
        print(f"   Modular Arithmetic: {results['is_modular_arithmetic']}")

        # Count non-uniform moduli
        non_uniform = sum(1 for r in results['modular_tests'].values() if not r['is_uniform'])
        print(f"   Non-uniform moduli: {non_uniform}/8")

        # Significant autocorrelations
        sig_autocorr = sum(1 for r in results['autocorrelation'].values() if r['is_significant'])
        print(f"   Significant autocorrelations: {sig_autocorr}/6")

        # Randomness verdict
        is_random = results['randomness_tests']['ks_vs_random']['is_random']
        print(f"   Appears random: {is_random}")

        # Runs test
        runs_random = results['runs_test']['is_random']
        print(f"   Runs test random: {runs_random}")

    # Overall conclusion
    print(f"\n🎯 OVERALL CONCLUSION:")

    modular_count = sum(1 for r in verification_results.values()
                       if 'MODULAR' in r['is_modular_arithmetic'])

    if modular_count >= 3:
        print("   🚨 STRONG EVIDENCE of modular arithmetic usage")
        print("   📊 Multiple games show non-uniform modular distributions")
        print("   🔄 Autocorrelation patterns detected")
        conclusion = "MODULAR_ARITHMETIC_CONFIRMED"
    elif modular_count >= 1:
        print("   ⚠️ WEAK EVIDENCE of modular arithmetic usage")
        print("   📊 Some games show suspicious patterns")
        conclusion = "MODULAR_ARITHMETIC_POSSIBLE"
    else:
        print("   ✅ NO EVIDENCE of modular arithmetic usage")
        print("   📊 Data appears genuinely random")
        conclusion = "APPEARS_TRULY_RANDOM"

    # Save verification results
    save_verification_results(verification_results, conclusion)

    return conclusion

def save_verification_results(verification_results, conclusion):
    """Save verification results to file"""

    report = {
        'timestamp': datetime.now().isoformat(),
        'analysis_type': 'Modular Arithmetic Verification',
        'conclusion': conclusion,
        'verification_results': verification_results,
        'methodology': [
            'Chi-square tests for modular uniformity',
            'Kolmogorov-Smirnov tests for randomness',
            'Autocorrelation analysis',
            'Runs tests',
            'Frequency distribution analysis'
        ]
    }

    os.makedirs('data', exist_ok=True)
    report_file = f'data/modular_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

    import json
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)

    print(f"\n💾 Verification report saved to: {report_file}")

if __name__ == "__main__":
    verify_modular_arithmetic()