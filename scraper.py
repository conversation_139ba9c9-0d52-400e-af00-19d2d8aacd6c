"""
Web scraper for Satta King data
"""

import requests
import time
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import logging
import json
import os
from tqdm import tqdm
import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(config.LOGS_DIR, 'scraper.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SattaKingScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
        
    def get_month_data(self, month, year):
        """
        Scrape data for a specific month and year
        """
        month_names = {
            1: 'January', 2: 'February', 3: 'March', 4: 'April',
            5: 'May', 6: 'June', 7: 'July', 8: 'August',
            9: 'September', 10: 'October', 11: 'November', 12: 'December'
        }
        
        if isinstance(month, int):
            month_name = month_names[month]
            month_num = f"{month:02d}"
        else:
            month_name = month
            month_num = f"{list(month_names.values()).index(month) + 1:02d}"
        
        url = f"{config.BASE_URL}?ResultFor={month_name}-{year}&month={month_num}&year={year}#mix-chart"
        
        for attempt in range(config.MAX_RETRIES):
            try:
                logger.info(f"Scraping {month_name} {year} (attempt {attempt + 1})")
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                return self.parse_chart_data(soup, month_name, year)
                
            except Exception as e:
                logger.error(f"Error scraping {month_name} {year}: {str(e)}")
                if attempt < config.MAX_RETRIES - 1:
                    time.sleep(config.REQUEST_DELAY * (attempt + 1))
                else:
                    return None
        
        return None
    
    def parse_chart_data(self, soup, month, year):
        """
        Parse the chart data from HTML
        """
        data = []

        try:
            # Get all text content and split into lines
            text_content = soup.get_text()
            lines = text_content.split('\n')

            # Look for the monthly chart data
            # The data appears after "Monthly Satta King Result Chart"
            in_data_section = False
            found_header = False

            for i, line in enumerate(lines):
                line = line.strip()

                # Look for the chart header
                if 'Monthly Satta King Result Chart' in line or ('DATE' in line and any(game in line for game in ['DSWR', 'FRBD', 'GZBD', 'GALI'])):
                    in_data_section = True
                    found_header = True
                    continue

                # Skip empty lines
                if not line:
                    continue

                # If we found the header, look for data rows
                if in_data_section and found_header:
                    # Check if this looks like a data row (starts with a number)
                    if line.isdigit() and len(line) <= 2:
                        # This is likely a date, look for the next few lines for the game results
                        try:
                            date = int(line)
                            if 1 <= date <= 31:  # Valid date
                                # Look for the next lines that contain the game results
                                dswr = frbd = gzbd = gali = None

                                # Check the next few lines for game results
                                for j in range(1, 6):  # Check next 5 lines
                                    if i + j < len(lines):
                                        next_line = lines[i + j].strip()
                                        if next_line and (next_line.isdigit() or next_line == 'XX'):
                                            if dswr is None:
                                                dswr = next_line if next_line != 'XX' else None
                                            elif frbd is None:
                                                frbd = next_line if next_line != 'XX' else None
                                            elif gzbd is None:
                                                gzbd = next_line if next_line != 'XX' else None
                                            elif gali is None:
                                                gali = next_line if next_line != 'XX' else None
                                                break  # Found all four values

                                # If we found at least one game result, add the record
                                if any([dswr, frbd, gzbd, gali]):
                                    data.append({
                                        'date': f"{year}-{self._get_month_number(month):02d}-{date:02d}",
                                        'day': date,
                                        'month': month,
                                        'year': year,
                                        'DSWR': dswr,
                                        'FRBD': frbd,
                                        'GZBD': gzbd,
                                        'GALI': gali
                                    })
                        except (ValueError, IndexError):
                            continue

                    # Alternative parsing: look for lines with multiple numbers
                    elif len(line.split()) >= 5:
                        parts = line.split()
                        if parts[0].isdigit() and len(parts[0]) <= 2:
                            try:
                                date = int(parts[0])
                                if 1 <= date <= 31:
                                    dswr = parts[1] if len(parts) > 1 and parts[1] != 'XX' else None
                                    frbd = parts[2] if len(parts) > 2 and parts[2] != 'XX' else None
                                    gzbd = parts[3] if len(parts) > 3 and parts[3] != 'XX' else None
                                    gali = parts[4] if len(parts) > 4 and parts[4] != 'XX' else None

                                    data.append({
                                        'date': f"{year}-{self._get_month_number(month):02d}-{date:02d}",
                                        'day': date,
                                        'month': month,
                                        'year': year,
                                        'DSWR': dswr,
                                        'FRBD': frbd,
                                        'GZBD': gzbd,
                                        'GALI': gali
                                    })
                            except (ValueError, IndexError):
                                continue

                    # Stop if we hit navigation or other sections
                    elif any(nav in line for nav in ['May 2025', 'Jul 2025', 'Previous', 'Next']):
                        break

            logger.info(f"Parsed {len(data)} records for {month} {year}")
            return data

        except Exception as e:
            logger.error(f"Error parsing data for {month} {year}: {str(e)}")
            return []

    def _get_month_number(self, month_name):
        """Convert month name to number"""
        months = {
            'January': 1, 'February': 2, 'March': 3, 'April': 4,
            'May': 5, 'June': 6, 'July': 7, 'August': 8,
            'September': 9, 'October': 10, 'November': 11, 'December': 12
        }
        return months.get(month_name, 1)
    
    def scrape_all_data(self, start_year=None, end_year=None):
        """
        Scrape data for all months in the specified year range
        """
        if start_year is None:
            start_year = config.START_YEAR
        if end_year is None:
            end_year = config.END_YEAR
            
        all_data = []
        
        total_months = (end_year - start_year + 1) * 12
        progress_bar = tqdm(total=total_months, desc="Scraping data")
        
        for year in range(start_year, end_year + 1):
            for month in range(1, 13):
                # Skip future months
                current_date = datetime.now()
                if year == current_date.year and month > current_date.month:
                    progress_bar.update(1)
                    continue
                
                month_data = self.get_month_data(month, year)
                if month_data:
                    all_data.extend(month_data)
                
                # Rate limiting
                time.sleep(config.REQUEST_DELAY)
                progress_bar.update(1)
        
        progress_bar.close()
        
        # Save raw data
        if all_data:
            df = pd.DataFrame(all_data)
            raw_file = os.path.join(config.RAW_DATA_DIR, f'satta_data_{start_year}_{end_year}.csv')
            df.to_csv(raw_file, index=False)
            logger.info(f"Saved {len(all_data)} records to {raw_file}")
            
            # Also save as JSON for backup
            json_file = os.path.join(config.RAW_DATA_DIR, f'satta_data_{start_year}_{end_year}.json')
            with open(json_file, 'w') as f:
                json.dump(all_data, f, indent=2)
        
        return all_data

if __name__ == "__main__":
    scraper = SattaKingScraper()
    data = scraper.scrape_all_data()
    print(f"Scraped {len(data)} total records")
