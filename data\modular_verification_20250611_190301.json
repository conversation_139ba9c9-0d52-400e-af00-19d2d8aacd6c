{"timestamp": "2025-06-11T19:03:01.456126", "analysis_type": "Modular Arithmetic Verification", "conclusion": "MODULAR_ARITHMETIC_CONFIRMED", "verification_results": {"DSWR": {"modular_tests": {"2": {"chi2_statistic": 1.6836836836836837, "critical_value": 3.841458820694124, "p_value": 0.19443569583642184, "is_uniform": "True", "observed_frequencies": {"0": 970, "1": 1028}, "expected_frequency": 999.0}, "3": {"chi2_statistic": 3.156156156156156, "critical_value": 5.991464547107979, "p_value": 0.20637134691906722, "is_uniform": "True", "observed_frequencies": {"0": 695, "1": 672, "2": 631}, "expected_frequency": 666.0}, "5": {"chi2_statistic": 6.924924924924925, "critical_value": 9.487729036781154, "p_value": 0.13990918755502624, "is_uniform": "True", "observed_frequencies": {"0": 387, "1": 391, "2": 378, "3": 397, "4": 445}, "expected_frequency": 399.6}, "7": {"chi2_statistic": 2.570570570570571, "critical_value": 12.591587243743977, "p_value": 0.8604875974847188, "is_uniform": "True", "observed_frequencies": {"0": 277, "1": 303, "2": 268, "3": 291, "4": 288, "5": 288, "6": 283}, "expected_frequency": 285.42857142857144}, "11": {"chi2_statistic": 12.775775775775777, "critical_value": 18.307038053275146, "p_value": 0.23648006735126048, "is_uniform": "True", "observed_frequencies": {"0": 158, "1": 156, "2": 169, "3": 178, "4": 199, "5": 201, "6": 192, "7": 193, "8": 185, "9": 183, "10": 184}, "expected_frequency": 181.63636363636363}, "13": {"chi2_statistic": 31.496496496496498, "critical_value": 21.02606981748307, "p_value": 0.0016540009016366986, "is_uniform": "False", "observed_frequencies": {"0": 134, "1": 156, "2": 177, "3": 156, "4": 158, "5": 175, "6": 172, "7": 153, "8": 183, "9": 145, "10": 116, "11": 145, "12": 128}, "expected_frequency": 153.69230769230768}, "17": {"chi2_statistic": 15.385385385385385, "critical_value": 26.29622760486423, "p_value": 0.49661393150867994, "is_uniform": "True", "observed_frequencies": {"0": 101, "1": 120, "2": 118, "3": 120, "4": 118, "5": 123, "6": 131, "7": 116, "8": 132, "9": 132, "10": 108, "11": 122, "12": 120, "13": 114, "14": 125, "15": 102, "16": 96}, "expected_frequency": 117.52941176470588}, "19": {"chi2_statistic": 30.929929929929926, "critical_value": 28.869299430392623, "p_value": 0.02932887700318898, "is_uniform": "False", "observed_frequencies": {"0": 112, "1": 103, "2": 116, "3": 134, "4": 133, "5": 116, "6": 113, "7": 92, "8": 109, "9": 109, "10": 88, "11": 99, "12": 93, "13": 92, "14": 89, "15": 105, "16": 94, "17": 98, "18": 103}, "expected_frequency": 105.15789473684211}}, "randomness_tests": {"ks_vs_random": {"statistic": 0.06906906906906907, "p_value": 0.00014430424238542872, "is_random": "False"}, "ks_vs_theoretical": {"statistic": 0.05405405405405406, "p_value": 0.005822217555108694, "is_uniform": "False"}, "anderson_darling": {"statistic": 27.00426723062833, "critical_values": [0.575, 0.655, 0.785, 0.916, 1.09], "significance_levels": [15.0, 10.0, 5.0, 2.5, 1.0]}}, "autocorrelation": {"1": {"correlation": -0.06525090654001317, "p_value": 0.003531855320073385, "is_significant": "True"}, "2": {"correlation": -0.00465705017578568, "p_value": 0.8352820374070997, "is_significant": "False"}, "3": {"correlation": -0.0355476873765696, "p_value": 0.1124539957692765, "is_significant": "False"}, "7": {"correlation": 0.00655467856617946, "p_value": 0.7700623768122989, "is_significant": "False"}, "14": {"correlation": -0.05409553135551963, "p_value": 0.01596234545543318, "is_significant": "True"}, "30": {"correlation": -0.01924729797904199, "p_value": 0.3934441746600559, "is_significant": "False"}}, "runs_test": {"runs_observed": 1047, "runs_expected": 1000.0, "z_score": 2.1034823662010163, "p_value": 0.035423625145300175, "is_random": "False"}, "frequency_test": {"chi2_statistic": 120.41841841841833, "p_value": 0.07064115475007471, "is_uniform": "True", "most_frequent_value": "0", "most_frequent_count": "7", "least_frequent_value": "99", "least_frequent_count": "26", "expected_frequency": 19.98}, "is_modular_arithmetic": "POSSIBLE_MODULAR_ARITHMETIC"}, "FRBD": {"modular_tests": {"2": {"chi2_statistic": 7.089613034623218, "critical_value": 3.841458820694124, "p_value": 0.007753198635330194, "is_uniform": "False", "observed_frequencies": {"0.0": 1041, "1.0": 923}, "expected_frequency": 982.0}, "3": {"chi2_statistic": 2.029531568228106, "critical_value": 5.991464547107979, "p_value": 0.36248732017960883, "is_uniform": "True", "observed_frequencies": {"0.0": 659, "1.0": 678, "2.0": 627}, "expected_frequency": 654.6666666666666}, "5": {"chi2_statistic": 2.211812627291242, "critical_value": 9.487729036781154, "p_value": 0.6968672214981015, "is_uniform": "True", "observed_frequencies": {"0.0": 391, "1.0": 393, "2.0": 417, "3.0": 385, "4.0": 378}, "expected_frequency": 392.8}, "7": {"chi2_statistic": 7.9969450101833, "critical_value": 12.591587243743977, "p_value": 0.23832720739517366, "is_uniform": "True", "observed_frequencies": {"0.0": 285, "1.0": 286, "2.0": 285, "3.0": 298, "4.0": 254, "5.0": 302, "6.0": 254}, "expected_frequency": 280.57142857142856}, "11": {"chi2_statistic": 4.910386965376783, "critical_value": 18.307038053275146, "p_value": 0.8970832362035939, "is_uniform": "True", "observed_frequencies": {"0.0": 180, "1.0": 164, "2.0": 181, "3.0": 174, "4.0": 188, "5.0": 196, "6.0": 189, "7.0": 175, "8.0": 171, "9.0": 172, "10.0": 174}, "expected_frequency": 178.54545454545453}, "13": {"chi2_statistic": 19.850305498981673, "critical_value": 21.02606981748307, "p_value": 0.06997119677023389, "is_uniform": "True", "observed_frequencies": {"0.0": 153, "1.0": 155, "2.0": 156, "3.0": 168, "4.0": 153, "5.0": 188, "6.0": 159, "7.0": 146, "8.0": 133, "9.0": 136, "10.0": 128, "11.0": 140, "12.0": 149}, "expected_frequency": 151.07692307692307}, "17": {"chi2_statistic": 13.037678207739306, "critical_value": 26.29622760486423, "p_value": 0.6700008895077367, "is_uniform": "True", "observed_frequencies": {"0.0": 108, "1.0": 117, "2.0": 124, "3.0": 120, "4.0": 103, "5.0": 109, "6.0": 107, "7.0": 129, "8.0": 119, "9.0": 127, "10.0": 116, "11.0": 121, "12.0": 127, "13.0": 122, "14.0": 116, "15.0": 104, "16.0": 95}, "expected_frequency": 115.52941176470588}, "19": {"chi2_statistic": 32.973523421588595, "critical_value": 28.869299430392623, "p_value": 0.016813926235151877, "is_uniform": "False", "observed_frequencies": {"0.0": 100, "1.0": 122, "2.0": 101, "3.0": 126, "4.0": 123, "5.0": 107, "6.0": 108, "7.0": 109, "8.0": 100, "9.0": 100, "10.0": 83, "11.0": 103, "12.0": 113, "13.0": 116, "14.0": 80, "15.0": 82, "16.0": 109, "17.0": 86, "18.0": 96}, "expected_frequency": 103.36842105263158}}, "randomness_tests": {"ks_vs_random": {"statistic": 0.07179226069246436, "p_value": 7.982355759131337e-05, "is_random": "False"}, "ks_vs_theoretical": {"statistic": 0.07179226069246436, "p_value": 7.982355759131337e-05, "is_uniform": "False"}, "anderson_darling": {"statistic": 30.572946831937998, "critical_values": [0.575, 0.655, 0.785, 0.916, 1.09], "significance_levels": [15.0, 10.0, 5.0, 2.5, 1.0]}}, "autocorrelation": {"1": {"correlation": -0.004438518987321963, "p_value": 0.8441971964381885, "is_significant": "False"}, "2": {"correlation": 0.028058083176327464, "p_value": 0.21413680318883488, "is_significant": "False"}, "3": {"correlation": 0.0005965801722696402, "p_value": 0.9789369984922505, "is_significant": "False"}, "7": {"correlation": -0.0663467287143262, "p_value": 0.003320388236638827, "is_significant": "True"}, "14": {"correlation": -0.027887973796472627, "p_value": 0.2183418173672207, "is_significant": "False"}, "30": {"correlation": -0.014905593828620658, "p_value": 0.5123905797965418, "is_significant": "False"}}, "runs_test": {"runs_observed": 985, "runs_expected": 983.0, "z_score": 0.09028173526788814, "p_value": 0.9280633340179305, "is_random": "True"}, "frequency_test": {"chi2_statistic": 123.0672097759673, "p_value": 0.051010113183890815, "is_uniform": "True", "most_frequent_value": 0.0, "most_frequent_count": "17", "least_frequent_value": 99.0, "least_frequent_count": "18", "expected_frequency": 19.64}, "is_modular_arithmetic": "POSSIBLE_MODULAR_ARITHMETIC"}, "GZBD": {"modular_tests": {"2": {"chi2_statistic": 0.6265984654731458, "critical_value": 3.841458820694124, "p_value": 0.4286057711699779, "is_uniform": "True", "observed_frequencies": {"0.0": 995, "1.0": 960}, "expected_frequency": 977.5}, "3": {"chi2_statistic": 3.960102301790281, "critical_value": 5.991464547107979, "p_value": 0.13806217512643304, "is_uniform": "True", "observed_frequencies": {"0.0": 693, "1.0": 634, "2.0": 628}, "expected_frequency": 651.6666666666666}, "5": {"chi2_statistic": 1.1611253196930946, "critical_value": 9.487729036781154, "p_value": 0.8844566632223366, "is_uniform": "True", "observed_frequencies": {"0.0": 387, "1.0": 390, "2.0": 377, "3.0": 395, "4.0": 406}, "expected_frequency": 391.0}, "7": {"chi2_statistic": 5.927365728900256, "critical_value": 12.591587243743977, "p_value": 0.43137568304422436, "is_uniform": "True", "observed_frequencies": {"0.0": 286, "1.0": 312, "2.0": 266, "3.0": 284, "4.0": 271, "5.0": 271, "6.0": 265}, "expected_frequency": 279.2857142857143}, "11": {"chi2_statistic": 13.347314578005115, "critical_value": 18.307038053275146, "p_value": 0.20489604758514446, "is_uniform": "True", "observed_frequencies": {"0.0": 192, "1.0": 150, "2.0": 181, "3.0": 203, "4.0": 191, "5.0": 162, "6.0": 187, "7.0": 174, "8.0": 180, "9.0": 165, "10.0": 170}, "expected_frequency": 177.72727272727272}, "13": {"chi2_statistic": 33.0158567774936, "critical_value": 21.02606981748307, "p_value": 0.0009625177598150403, "is_uniform": "False", "observed_frequencies": {"0.0": 147, "1.0": 164, "2.0": 151, "3.0": 190, "4.0": 173, "5.0": 150, "6.0": 160, "7.0": 162, "8.0": 139, "9.0": 138, "10.0": 123, "11.0": 145, "12.0": 113}, "expected_frequency": 150.3846153846154}, "17": {"chi2_statistic": 27.321739130434782, "critical_value": 26.29622760486423, "p_value": 0.03804263882105963, "is_uniform": "False", "observed_frequencies": {"0.0": 120, "1.0": 119, "2.0": 109, "3.0": 118, "4.0": 125, "5.0": 89, "6.0": 143, "7.0": 129, "8.0": 133, "9.0": 113, "10.0": 98, "11.0": 110, "12.0": 119, "13.0": 119, "14.0": 116, "15.0": 95, "16.0": 100}, "expected_frequency": 115.0}, "19": {"chi2_statistic": 21.787212276214834, "critical_value": 28.869299430392623, "p_value": 0.24156941315709712, "is_uniform": "True", "observed_frequencies": {"0.0": 97, "1.0": 96, "2.0": 118, "3.0": 117, "4.0": 116, "5.0": 98, "6.0": 113, "7.0": 96, "8.0": 101, "9.0": 98, "10.0": 78, "11.0": 115, "12.0": 101, "13.0": 86, "14.0": 99, "15.0": 109, "16.0": 100, "17.0": 118, "18.0": 99}, "expected_frequency": 102.89473684210526}}, "randomness_tests": {"ks_vs_random": {"statistic": 0.08849104859335039, "p_value": 4.4201727056745726e-07, "is_random": "False"}, "ks_vs_theoretical": {"statistic": 0.08439897698209718, "p_value": 1.767949251410994e-06, "is_uniform": "False"}, "anderson_darling": {"statistic": 33.76084865982966, "critical_values": [0.575, 0.655, 0.785, 0.916, 1.09], "significance_levels": [15.0, 10.0, 5.0, 2.5, 1.0]}}, "autocorrelation": {"1": {"correlation": 0.0004886232667412777, "p_value": 0.982778745240311, "is_significant": "False"}, "2": {"correlation": 0.014071114829929291, "p_value": 0.5342877705599842, "is_significant": "False"}, "3": {"correlation": -0.017780089409561793, "p_value": 0.43238972576333135, "is_significant": "False"}, "7": {"correlation": -0.0010484437615504464, "p_value": 0.9631153069811584, "is_significant": "False"}, "14": {"correlation": 0.04350269895758302, "p_value": 0.055331828523160986, "is_significant": "False"}, "30": {"correlation": -0.006768769999023316, "p_value": 0.7666275450844555, "is_significant": "False"}}, "runs_test": {"runs_observed": 980, "runs_expected": 978.4997442455243, "z_score": 0.06787864866945745, "p_value": 0.9458822354380367, "is_random": "True"}, "frequency_test": {"chi2_statistic": 144.48849104859323, "p_value": 0.001959758028760472, "is_uniform": "False", "most_frequent_value": 0.0, "most_frequent_count": "15", "least_frequent_value": 99.0, "least_frequent_count": "18", "expected_frequency": 19.55}, "is_modular_arithmetic": "POSSIBLE_MODULAR_ARITHMETIC"}, "GALI": {"modular_tests": {"2": {"chi2_statistic": 0.05714285714285714, "critical_value": 3.841458820694124, "p_value": 0.8110701293339075, "is_uniform": "True", "observed_frequencies": {"0.0": 564, "1.0": 556}, "expected_frequency": 560.0}, "3": {"chi2_statistic": 3.923214285714286, "critical_value": 5.991464547107979, "p_value": 0.14063222313076218, "is_uniform": "True", "observed_frequencies": {"0.0": 382, "1.0": 395, "2.0": 343}, "expected_frequency": 373.3333333333333}, "5": {"chi2_statistic": 3.25, "critical_value": 9.487729036781154, "p_value": 0.5168931474110094, "is_uniform": "True", "observed_frequencies": {"0.0": 216, "1.0": 214, "2.0": 228, "3.0": 246, "4.0": 216}, "expected_frequency": 224.0}, "7": {"chi2_statistic": 6.425000000000001, "critical_value": 12.591587243743977, "p_value": 0.3773010780084647, "is_uniform": "True", "observed_frequencies": {"0.0": 156, "1.0": 169, "2.0": 133, "3.0": 159, "4.0": 168, "5.0": 164, "6.0": 171}, "expected_frequency": 160.0}, "11": {"chi2_statistic": 1.4107142857142858, "critical_value": 18.307038053275146, "p_value": 0.9991875128223198, "is_uniform": "True", "observed_frequencies": {"0.0": 100, "1.0": 109, "2.0": 102, "3.0": 104, "4.0": 104, "5.0": 100, "6.0": 99, "7.0": 107, "8.0": 97, "9.0": 100, "10.0": 98}, "expected_frequency": 101.81818181818181}, "13": {"chi2_statistic": 27.041071428571428, "critical_value": 21.02606981748307, "p_value": 0.007622671514777024, "is_uniform": "False", "observed_frequencies": {"0.0": 102, "1.0": 97, "2.0": 94, "3.0": 102, "4.0": 92, "5.0": 91, "6.0": 99, "7.0": 87, "8.0": 78, "9.0": 58, "10.0": 74, "11.0": 67, "12.0": 79}, "expected_frequency": 86.15384615384616}, "17": {"chi2_statistic": 10.985714285714286, "critical_value": 26.29622760486423, "p_value": 0.8103662031961851, "is_uniform": "True", "observed_frequencies": {"0.0": 55, "1.0": 61, "2.0": 71, "3.0": 71, "4.0": 62, "5.0": 71, "6.0": 76, "7.0": 56, "8.0": 64, "9.0": 65, "10.0": 60, "11.0": 72, "12.0": 75, "13.0": 74, "14.0": 66, "15.0": 59, "16.0": 62}, "expected_frequency": 65.88235294117646}, "19": {"chi2_statistic": 18.541071428571428, "critical_value": 28.869299430392623, "p_value": 0.42057972421521905, "is_uniform": "True", "observed_frequencies": {"0.0": 58, "1.0": 66, "2.0": 73, "3.0": 55, "4.0": 60, "5.0": 61, "6.0": 44, "7.0": 57, "8.0": 62, "9.0": 55, "10.0": 49, "11.0": 67, "12.0": 69, "13.0": 56, "14.0": 70, "15.0": 63, "16.0": 52, "17.0": 51, "18.0": 52}, "expected_frequency": 58.94736842105263}}, "randomness_tests": {"ks_vs_random": {"statistic": 0.06785714285714285, "p_value": 0.011497174181822268, "is_random": "False"}, "ks_vs_theoretical": {"statistic": 0.038392857142857145, "p_value": 0.3811835212209135, "is_uniform": "True"}, "anderson_darling": {"statistic": 13.62419317036074, "critical_values": [0.574, 0.654, 0.784, 0.915, 1.088], "significance_levels": [15.0, 10.0, 5.0, 2.5, 1.0]}}, "autocorrelation": {"1": {"correlation": -0.05250877844720171, "p_value": 0.0791322471940119, "is_significant": "False"}, "2": {"correlation": 0.019170714923914606, "p_value": 0.5219492827223462, "is_significant": "False"}, "3": {"correlation": 0.04687039339291034, "p_value": 0.11744516213071154, "is_significant": "False"}, "7": {"correlation": -0.00794093602860591, "p_value": 0.7912950839672721, "is_significant": "False"}, "14": {"correlation": 0.002062572170861905, "p_value": 0.9453744058748181, "is_significant": "False"}, "30": {"correlation": 0.009536147423005594, "p_value": 0.7531540539347219, "is_significant": "False"}}, "runs_test": {"runs_observed": 608, "runs_expected": 560.9357142857143, "z_score": 2.8142099899936324, "p_value": 0.004889727555789758, "is_random": "False"}, "frequency_test": {"chi2_statistic": 87.50000000000006, "p_value": 0.7891204438307752, "is_uniform": "True", "most_frequent_value": 0.0, "most_frequent_count": "8", "least_frequent_value": 99.0, "least_frequent_count": "8", "expected_frequency": 11.2}, "is_modular_arithmetic": "NON_RANDOM_BUT_NOT_MODULAR"}}, "methodology": ["Chi-square tests for modular uniformity", "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> tests for randomness", "Autocorrelation analysis", "Runs tests", "Frequency distribution analysis"]}