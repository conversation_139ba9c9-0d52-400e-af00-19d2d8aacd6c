"""
Comprehensive backtest of all strategies on previous 20+ results
Test LCG formulas, pattern predictions, and detect manipulation
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

def comprehensive_backtest():
    """Backtest all strategies on recent historical data"""
    
    print("🔬 COMPREHENSIVE STRATEGY BACKTEST")
    print("=" * 70)
    print("Testing ALL strategies on previous 20+ results")
    print("=" * 70)
    
    # Load data
    df = pd.read_csv('data/raw/extensive_historical_data.csv')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    print(f"✅ Loaded {len(df)} total records")
    
    # Test each game
    all_results = {}
    
    for game in ['DSWR', 'FRBD', 'GZBD', 'GALI']:
        if game in df.columns:
            game_data = pd.to_numeric(df[game], errors='coerce').dropna()
            
            print(f"\n🎲 {game} COMPREHENSIVE BACKTEST:")
            print("=" * 50)
            
            # Use last 50 records for testing (more robust)
            if len(game_data) >= 50:
                test_results = backtest_game_strategies(game_data, game)
                all_results[game] = test_results
            else:
                print(f"   ❌ Insufficient data ({len(game_data)} records)")
    
    # Generate comprehensive analysis
    generate_backtest_analysis(all_results)
    
    return all_results

def backtest_game_strategies(data, game_name):
    """Backtest all strategies for a specific game"""
    
    # Use last 30 for testing, previous data for training
    test_size = 30
    train_data = data.iloc[:-test_size]
    test_data = data.iloc[-test_size:]
    
    print(f"📊 Training data: {len(train_data)} records")
    print(f"📊 Testing data: {len(test_data)} records")
    print(f"📅 Test period: Last {test_size} draws")
    
    results = {
        'test_size': test_size,
        'strategies': {}
    }
    
    # Strategy 1: LCG Formula Predictions
    lcg_results = test_lcg_strategy(train_data, test_data, game_name)
    results['strategies']['lcg_formula'] = lcg_results
    
    # Strategy 2: Recent Avoidance
    avoidance_results = test_recent_avoidance_strategy(train_data, test_data)
    results['strategies']['recent_avoidance'] = avoidance_results
    
    # Strategy 3: Pattern Breaking
    pattern_results = test_pattern_breaking_strategy(train_data, test_data)
    results['strategies']['pattern_breaking'] = pattern_results
    
    # Strategy 4: Range Rotation
    range_results = test_range_rotation_strategy(train_data, test_data)
    results['strategies']['range_rotation'] = range_results
    
    # Strategy 5: Mean Reversion
    mean_results = test_mean_reversion_strategy(train_data, test_data)
    results['strategies']['mean_reversion'] = mean_results
    
    # Strategy 6: Prime Pattern
    prime_results = test_prime_pattern_strategy(train_data, test_data)
    results['strategies']['prime_pattern'] = prime_results
    
    # Strategy 7: Random Baseline
    random_results = test_random_baseline(test_data)
    results['strategies']['random_baseline'] = random_results
    
    return results

def test_lcg_strategy(train_data, test_data, game_name):
    """Test Linear Congruential Generator formula predictions"""
    
    print(f"\n🔢 Testing LCG Formula Strategy...")
    
    # LCG parameters discovered in our analysis
    lcg_params = {
        'DSWR': {'a': 1103515245, 'c': 1013904223, 'm': 2**32},
        'FRBD': {'a': 48271, 'c': 0, 'm': 101},
        'GZBD': {'a': 1664525, 'c': 1013904223, 'm': 256},
        'GALI': None  # No clear LCG pattern
    }
    
    if game_name not in lcg_params or lcg_params[game_name] is None:
        print(f"   ❌ No LCG formula available for {game_name}")
        return {'accuracy': 0, 'predictions': [], 'actuals': [], 'method': 'No LCG formula'}
    
    params = lcg_params[game_name]
    a, c, m = params['a'], params['c'], params['m']
    
    predictions = []
    actuals = []
    
    # Generate predictions for each test point
    for i in range(len(test_data)):
        if i == 0:
            # Use last training value as seed
            seed = int(train_data.iloc[-1])
        else:
            # Use previous actual value as seed
            seed = int(test_data.iloc[i-1])
        
        # Apply LCG formula
        next_val = (a * seed + c) % m
        
        # Map to 0-99 range
        if m == 100:
            prediction = next_val
        else:
            prediction = next_val % 100
        
        predictions.append(prediction)
        actuals.append(int(test_data.iloc[i]))
    
    # Calculate accuracy
    exact_matches = sum(1 for p, a in zip(predictions, actuals) if p == a)
    within_5 = sum(1 for p, a in zip(predictions, actuals) if abs(p - a) <= 5)
    within_10 = sum(1 for p, a in zip(predictions, actuals) if abs(p - a) <= 10)
    
    accuracy = exact_matches / len(predictions) * 100
    
    print(f"   📊 LCG Formula Results:")
    print(f"      Exact matches: {exact_matches}/{len(predictions)} ({accuracy:.1f}%)")
    print(f"      Within ±5: {within_5}/{len(predictions)} ({within_5/len(predictions)*100:.1f}%)")
    print(f"      Within ±10: {within_10}/{len(predictions)} ({within_10/len(predictions)*100:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'within_5_accuracy': within_5/len(predictions)*100,
        'within_10_accuracy': within_10/len(predictions)*100,
        'predictions': predictions,
        'actuals': actuals,
        'method': f'LCG: X(n+1) = ({a} * X + {c}) mod {m}'
    }

def test_recent_avoidance_strategy(train_data, test_data):
    """Test recent number avoidance strategy"""
    
    print(f"\n🎯 Testing Recent Avoidance Strategy...")
    
    correct_predictions = 0
    total_predictions = 0
    predictions = []
    actuals = []
    
    # Combine train and test data for rolling analysis
    all_data = pd.concat([train_data, test_data]).reset_index(drop=True)
    train_size = len(train_data)
    
    for i in range(train_size, len(all_data)):
        # Look at last 10 numbers
        recent_10 = set(all_data.iloc[i-10:i])
        actual_number = int(all_data.iloc[i])
        
        # Strategy: predict number will NOT be in recent 10
        prediction_correct = actual_number not in recent_10
        
        if prediction_correct:
            correct_predictions += 1
        
        total_predictions += 1
        predictions.append(f"NOT in {sorted(recent_10)}")
        actuals.append(actual_number)
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    print(f"   📊 Recent Avoidance Results:")
    print(f"      Correct predictions: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'predictions': predictions,
        'actuals': actuals,
        'method': 'Avoid numbers from last 10 draws'
    }

def test_pattern_breaking_strategy(train_data, test_data):
    """Test pattern breaking strategy"""
    
    print(f"\n🔄 Testing Pattern Breaking Strategy...")
    
    correct_predictions = 0
    total_predictions = 0
    predictions = []
    actuals = []
    
    # Combine data for rolling analysis
    all_data = pd.concat([train_data, test_data]).reset_index(drop=True)
    train_size = len(train_data)
    
    for i in range(train_size, len(all_data)):
        if i >= 2:
            # Check for arithmetic progression in last 2 numbers
            last_two = all_data.iloc[i-2:i].values
            diff = last_two[1] - last_two[0]
            expected_continuation = last_two[1] + diff
            actual_number = int(all_data.iloc[i])
            
            # Only make prediction if continuation would be valid
            if 0 <= expected_continuation <= 99:
                # Strategy: predict pattern will break (number will NOT be continuation)
                prediction_correct = abs(actual_number - expected_continuation) > 5
                
                if prediction_correct:
                    correct_predictions += 1
                
                total_predictions += 1
                predictions.append(f"NOT {expected_continuation:.0f} (break pattern)")
                actuals.append(actual_number)
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    print(f"   📊 Pattern Breaking Results:")
    print(f"      Correct predictions: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'predictions': predictions,
        'actuals': actuals,
        'method': 'Predict arithmetic pattern breaks'
    }

def test_range_rotation_strategy(train_data, test_data):
    """Test range rotation strategy"""
    
    print(f"\n📏 Testing Range Rotation Strategy...")
    
    ranges = [(0, 33), (34, 66), (67, 99)]
    correct_predictions = 0
    total_predictions = 0
    predictions = []
    actuals = []
    
    # Combine data for rolling analysis
    all_data = pd.concat([train_data, test_data]).reset_index(drop=True)
    train_size = len(train_data)
    
    for i in range(train_size, len(all_data)):
        if i >= 10:
            # Analyze last 10 numbers
            recent_10 = all_data.iloc[i-10:i]
            actual_number = int(all_data.iloc[i])
            
            # Count usage per range
            range_counts = [0, 0, 0]
            for value in recent_10:
                for j, (start, end) in enumerate(ranges):
                    if start <= value <= end:
                        range_counts[j] += 1
                        break
            
            # Predict least used range
            least_used_range_idx = range_counts.index(min(range_counts))
            predicted_range = ranges[least_used_range_idx]
            
            # Check if actual number is in predicted range
            prediction_correct = predicted_range[0] <= actual_number <= predicted_range[1]
            
            if prediction_correct:
                correct_predictions += 1
            
            total_predictions += 1
            predictions.append(f"Range {predicted_range}")
            actuals.append(actual_number)
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    print(f"   📊 Range Rotation Results:")
    print(f"      Correct predictions: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'predictions': predictions,
        'actuals': actuals,
        'method': 'Predict least used range'
    }

def test_mean_reversion_strategy(train_data, test_data):
    """Test mean reversion strategy"""
    
    print(f"\n📈 Testing Mean Reversion Strategy...")
    
    correct_predictions = 0
    total_predictions = 0
    predictions = []
    actuals = []
    
    # Combine data for rolling analysis
    all_data = pd.concat([train_data, test_data]).reset_index(drop=True)
    train_size = len(train_data)
    
    for i in range(train_size, len(all_data)):
        if i >= 30:
            # Calculate 30-day rolling mean
            recent_30 = all_data.iloc[i-30:i]
            rolling_mean = recent_30.mean()
            current_value = all_data.iloc[i-1]
            actual_next = int(all_data.iloc[i])
            
            # Only predict when far from mean
            if abs(current_value - rolling_mean) > 10:
                # Predict direction toward mean
                if current_value > rolling_mean:
                    predicted_direction = "DOWN"
                    prediction_correct = actual_next < current_value
                else:
                    predicted_direction = "UP"
                    prediction_correct = actual_next > current_value
                
                if prediction_correct:
                    correct_predictions += 1
                
                total_predictions += 1
                predictions.append(f"{predicted_direction} toward {rolling_mean:.1f}")
                actuals.append(actual_next)
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    print(f"   📊 Mean Reversion Results:")
    print(f"      Correct predictions: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'predictions': predictions,
        'actuals': actuals,
        'method': 'Predict reversion to 30-day mean'
    }

def test_prime_pattern_strategy(train_data, test_data):
    """Test prime pattern strategy"""
    
    print(f"\n🔢 Testing Prime Pattern Strategy...")
    
    primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97]
    
    correct_predictions = 0
    total_predictions = len(test_data)
    predictions = []
    actuals = []
    
    for i in range(len(test_data)):
        actual_number = int(test_data.iloc[i])
        
        # Simple strategy: predict prime numbers are more likely
        # (Based on our 99.5% prime correlation finding)
        prediction_correct = actual_number in primes
        
        if prediction_correct:
            correct_predictions += 1
        
        predictions.append("Prime number")
        actuals.append(actual_number)
    
    accuracy = (correct_predictions / total_predictions) * 100 if total_predictions > 0 else 0
    
    print(f"   📊 Prime Pattern Results:")
    print(f"      Correct predictions: {correct_predictions}/{total_predictions} ({accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'predictions': predictions,
        'actuals': actuals,
        'method': 'Predict prime numbers more likely'
    }

def test_random_baseline(test_data):
    """Test random baseline for comparison"""
    
    print(f"\n🎲 Testing Random Baseline...")
    
    # Random prediction should hit ~1% for exact match, ~10% for ±5
    total_predictions = len(test_data)
    
    # Simulate random predictions
    np.random.seed(42)  # For reproducible results
    random_predictions = np.random.randint(0, 100, total_predictions)
    actuals = [int(x) for x in test_data]
    
    exact_matches = sum(1 for p, a in zip(random_predictions, actuals) if p == a)
    within_5 = sum(1 for p, a in zip(random_predictions, actuals) if abs(p - a) <= 5)
    
    accuracy = (exact_matches / total_predictions) * 100
    within_5_accuracy = (within_5 / total_predictions) * 100
    
    print(f"   📊 Random Baseline Results:")
    print(f"      Exact matches: {exact_matches}/{total_predictions} ({accuracy:.1f}%)")
    print(f"      Within ±5: {within_5}/{total_predictions} ({within_5_accuracy:.1f}%)")
    
    return {
        'accuracy': accuracy,
        'within_5_accuracy': within_5_accuracy,
        'predictions': random_predictions.tolist(),
        'actuals': actuals,
        'method': 'Random number generation'
    }

def generate_backtest_analysis(all_results):
    """Generate comprehensive analysis of backtest results"""
    
    print(f"\n🎯 COMPREHENSIVE BACKTEST ANALYSIS")
    print("=" * 60)
    
    # Collect all strategy accuracies
    strategy_performance = {}
    
    for game, results in all_results.items():
        print(f"\n🎲 {game} STRATEGY PERFORMANCE:")
        print("-" * 40)
        
        strategies = results['strategies']
        
        # Sort strategies by accuracy
        sorted_strategies = sorted(strategies.items(), 
                                 key=lambda x: x[1]['accuracy'], 
                                 reverse=True)
        
        for strategy_name, strategy_results in sorted_strategies:
            accuracy = strategy_results['accuracy']
            method = strategy_results['method']
            
            if strategy_name not in strategy_performance:
                strategy_performance[strategy_name] = []
            strategy_performance[strategy_name].append(accuracy)
            
            # Performance rating
            if accuracy >= 80:
                rating = "🏆 EXCELLENT"
            elif accuracy >= 60:
                rating = "🥇 VERY GOOD"
            elif accuracy >= 40:
                rating = "🥈 GOOD"
            elif accuracy >= 20:
                rating = "🥉 FAIR"
            else:
                rating = "❌ POOR"
            
            print(f"   {rating} {strategy_name}: {accuracy:.1f}%")
            print(f"      Method: {method}")
    
    # Overall strategy ranking
    print(f"\n🏆 OVERALL STRATEGY RANKING:")
    print("-" * 40)
    
    overall_performance = {}
    for strategy, accuracies in strategy_performance.items():
        avg_accuracy = np.mean(accuracies)
        overall_performance[strategy] = avg_accuracy
    
    sorted_overall = sorted(overall_performance.items(), 
                           key=lambda x: x[1], 
                           reverse=True)
    
    for i, (strategy, avg_accuracy) in enumerate(sorted_overall, 1):
        if avg_accuracy >= 50:
            status = "🚨 EXPLOITABLE"
        elif avg_accuracy >= 30:
            status = "⚠️ PROMISING"
        elif avg_accuracy >= 15:
            status = "💡 MARGINAL"
        else:
            status = "❌ INEFFECTIVE"
        
        print(f"   {i}. {strategy}: {avg_accuracy:.1f}% {status}")
    
    # Manipulation detection
    print(f"\n🔍 MANIPULATION DETECTION:")
    print("-" * 40)
    
    # Check if any strategy performs suspiciously well
    best_strategy = sorted_overall[0]
    best_accuracy = best_strategy[1]
    
    if best_accuracy > 80:
        print(f"   🚨 SUSPICIOUS: {best_strategy[0]} shows {best_accuracy:.1f}% accuracy")
        print(f"   📊 This suggests strong algorithmic predictability")
        print(f"   ⚠️ Possible manipulation or flawed randomness")
    elif best_accuracy > 50:
        print(f"   ⚠️ NOTABLE: {best_strategy[0]} shows {best_accuracy:.1f}% accuracy")
        print(f"   📊 Above-random performance detected")
        print(f"   💡 Exploitable patterns confirmed")
    elif best_accuracy > 20:
        print(f"   💡 MARGINAL: Best strategy only {best_accuracy:.1f}% accurate")
        print(f"   📊 Weak patterns detected")
        print(f"   ⚠️ Limited exploitation potential")
    else:
        print(f"   ✅ NO MANIPULATION: All strategies perform poorly")
        print(f"   📊 Data appears genuinely random")
        print(f"   ❌ No exploitable patterns found")
    
    # Final recommendations
    print(f"\n💡 FINAL RECOMMENDATIONS:")
    print("-" * 40)
    
    if best_accuracy > 60:
        print(f"   🎯 RECOMMENDED STRATEGY: {best_strategy[0]}")
        print(f"   📊 Expected accuracy: {best_accuracy:.1f}%")
        print(f"   💰 Proceed with EXTREME caution")
        print(f"   ⚠️ Use minimal stakes (0.5% max)")
    elif best_accuracy > 30:
        print(f"   ⚠️ MARGINAL STRATEGY: {best_strategy[0]}")
        print(f"   📊 Expected accuracy: {best_accuracy:.1f}%")
        print(f"   💰 Paper trade only")
        print(f"   ❌ Not recommended for real money")
    else:
        print(f"   ❌ NO RELIABLE STRATEGY FOUND")
        print(f"   📊 Best accuracy: {best_accuracy:.1f}%")
        print(f"   💰 DO NOT GAMBLE")
        print(f"   ✅ Data appears genuinely random")
    
    # Save results
    save_backtest_results(all_results, overall_performance)

def save_backtest_results(all_results, overall_performance):
    """Save backtest results to file"""
    
    backtest_report = {
        'timestamp': datetime.now().isoformat(),
        'analysis_type': 'Comprehensive Strategy Backtest',
        'test_period': '30 most recent draws per game',
        'game_results': all_results,
        'overall_performance': overall_performance,
        'best_strategy': max(overall_performance.items(), key=lambda x: x[1]),
        'methodology': [
            'LCG formula testing',
            'Recent avoidance strategy',
            'Pattern breaking strategy', 
            'Range rotation strategy',
            'Mean reversion strategy',
            'Prime pattern strategy',
            'Random baseline comparison'
        ]
    }
    
    import os
    os.makedirs('data', exist_ok=True)
    
    with open(f'data/comprehensive_backtest_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(backtest_report, f, indent=2, default=str)
    
    print(f"\n💾 Backtest results saved to data/comprehensive_backtest_*.json")

if __name__ == "__main__":
    comprehensive_backtest()
