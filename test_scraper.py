"""
Test script for the Satta King scraper
"""

from scraper import SattaKingScraper
import json

def test_single_month():
    """Test scraping a single month"""
    scraper = SattaKingScraper()
    
    # Test with June 2025 (current month from the example)
    print("Testing scraper with June 2025...")
    data = scraper.get_month_data("June", 2025)
    
    if data:
        print(f"Successfully scraped {len(data)} records")
        print("Sample data:")
        for record in data[:5]:  # Show first 5 records
            print(json.dumps(record, indent=2))
    else:
        print("No data found")
    
    return data

def test_recent_months():
    """Test scraping recent months"""
    scraper = SattaKingScraper()
    
    months_to_test = [
        ("May", 2025),
        ("April", 2025),
        ("March", 2025)
    ]
    
    all_data = []
    
    for month, year in months_to_test:
        print(f"\nTesting {month} {year}...")
        data = scraper.get_month_data(month, year)
        if data:
            print(f"Found {len(data)} records")
            all_data.extend(data)
        else:
            print("No data found")
    
    print(f"\nTotal records found: {len(all_data)}")
    return all_data

if __name__ == "__main__":
    # Test single month first
    data = test_single_month()
    
    # If that works, test multiple months
    if data:
        print("\n" + "="*50)
        print("Single month test successful, testing multiple months...")
        all_data = test_recent_months()
    else:
        print("Single month test failed. Check the parsing logic.")
